/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   combineReducers: () => (/* reexport safe */ redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers),
/* harmony export */   configureStore: () => (/* binding */ configureStore),
/* harmony export */   createSerializableStateInvariantMiddleware: () => (/* binding */ createSerializableStateInvariantMiddleware),
/* harmony export */   createSlice: () => (/* binding */ createSlice)
/* harmony export */ });
/* unused harmony exports ReducerType, SHOULD_AUTOBATCH, TaskAbortError, Tuple, addListener, asyncThunkCreator, autoBatchEnhancer, buildCreateSlice, clearAllListeners, combineSlices, createAction, createActionCreatorInvariantMiddleware, createAsyncThunk, createDraftSafeSelector, createDraftSafeSelectorCreator, createDynamicMiddleware, createEntityAdapter, createImmutableStateInvariantMiddleware, createListenerMiddleware, createReducer, findNonSerializableValue, formatProdErrorMessage, isActionCreator, isAllOf, isAnyOf, isAsyncThunkAction, isFluxStandardAction, isFulfilled, isImmutableDefault, isPending, isPlain, isRejected, isRejectedWithValue, miniSerializeError, nanoid, prepareAutoBatched, removeListener, unwrapResult */
/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! redux */ "./node_modules/redux/dist/redux.mjs");
/* harmony import */ var immer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! immer */ "./node_modules/immer/dist/immer.mjs");
/* harmony import */ var reselect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! reselect */ "./node_modules/reselect/dist/reselect.mjs");
/* harmony import */ var redux_thunk__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! redux-thunk */ "./node_modules/redux-thunk/dist/redux-thunk.mjs");
// src/index.ts




// src/createDraftSafeSelector.ts


var createDraftSafeSelectorCreator = (...args) => {
  const createSelector2 = (0,reselect__WEBPACK_IMPORTED_MODULE_2__.createSelectorCreator)(...args);
  const createDraftSafeSelector2 = Object.assign((...args2) => {
    const selector = createSelector2(...args2);
    const wrappedSelector = (value, ...rest) => selector((0,immer__WEBPACK_IMPORTED_MODULE_1__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_1__.current)(value) : value, ...rest);
    Object.assign(wrappedSelector, selector);
    return wrappedSelector;
  }, {
    withTypes: () => createDraftSafeSelector2
  });
  return createDraftSafeSelector2;
};
var createDraftSafeSelector = /* @__PURE__ */ createDraftSafeSelectorCreator(reselect__WEBPACK_IMPORTED_MODULE_2__.weakMapMemoize);

// src/configureStore.ts


// src/devtoolsExtension.ts

var composeWithDevTools = typeof window !== "undefined" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function() {
  if (arguments.length === 0) return void 0;
  if (typeof arguments[0] === "object") return redux__WEBPACK_IMPORTED_MODULE_0__.compose;
  return redux__WEBPACK_IMPORTED_MODULE_0__.compose.apply(null, arguments);
};
var devToolsEnhancer = typeof window !== "undefined" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function() {
  return function(noop3) {
    return noop3;
  };
};

// src/getDefaultMiddleware.ts


// src/createAction.ts


// src/tsHelpers.ts
var hasMatchFunction = (v) => {
  return v && typeof v.match === "function";
};

// src/createAction.ts
function createAction(type, prepareAction) {
  function actionCreator(...args) {
    if (prepareAction) {
      let prepared = prepareAction(...args);
      if (!prepared) {
        throw new Error( false ? 0 : "prepareAction did not return an object");
      }
      return {
        type,
        payload: prepared.payload,
        ..."meta" in prepared && {
          meta: prepared.meta
        },
        ..."error" in prepared && {
          error: prepared.error
        }
      };
    }
    return {
      type,
      payload: args[0]
    };
  }
  actionCreator.toString = () => `${type}`;
  actionCreator.type = type;
  actionCreator.match = (action) => (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && action.type === type;
  return actionCreator;
}
function isActionCreator(action) {
  return typeof action === "function" && "type" in action && // hasMatchFunction only wants Matchers but I don't see the point in rewriting it
  hasMatchFunction(action);
}
function isFSA(action) {
  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action) && Object.keys(action).every(isValidKey);
}
function isValidKey(key) {
  return ["type", "payload", "error", "meta"].indexOf(key) > -1;
}

// src/actionCreatorInvariantMiddleware.ts
function getMessage(type) {
  const splitType = type ? `${type}`.split("/") : [];
  const actionName = splitType[splitType.length - 1] || "actionCreator";
  return `Detected an action creator with type "${type || "unknown"}" being dispatched. 
Make sure you're calling the action creator before dispatching, i.e. \`dispatch(${actionName}())\` instead of \`dispatch(${actionName})\`. This is necessary even if the action has no payload.`;
}
function createActionCreatorInvariantMiddleware(options = {}) {
  if (false) // removed by dead control flow
{}
  const {
    isActionCreator: isActionCreator2 = isActionCreator
  } = options;
  return () => (next) => (action) => {
    if (isActionCreator2(action)) {
      console.warn(getMessage(action.type));
    }
    return next(action);
  };
}

// src/utils.ts

function getTimeMeasureUtils(maxDelay, fnName) {
  let elapsed = 0;
  return {
    measureTime(fn) {
      const started = Date.now();
      try {
        return fn();
      } finally {
        const finished = Date.now();
        elapsed += finished - started;
      }
    },
    warnIfExceeded() {
      if (elapsed > maxDelay) {
        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. 
If your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.
It is disabled in production builds, so you don't need to worry about that.`);
      }
    }
  };
}
var Tuple = class _Tuple extends Array {
  constructor(...items) {
    super(...items);
    Object.setPrototypeOf(this, _Tuple.prototype);
  }
  static get [Symbol.species]() {
    return _Tuple;
  }
  concat(...arr) {
    return super.concat.apply(this, arr);
  }
  prepend(...arr) {
    if (arr.length === 1 && Array.isArray(arr[0])) {
      return new _Tuple(...arr[0].concat(this));
    }
    return new _Tuple(...arr.concat(this));
  }
};
function freezeDraftable(val) {
  return (0,immer__WEBPACK_IMPORTED_MODULE_1__.isDraftable)(val) ? (0,immer__WEBPACK_IMPORTED_MODULE_1__.produce)(val, () => {
  }) : val;
}
function getOrInsertComputed(map, key, compute) {
  if (map.has(key)) return map.get(key);
  return map.set(key, compute(key)).get(key);
}

// src/immutableStateInvariantMiddleware.ts
function isImmutableDefault(value) {
  return typeof value !== "object" || value == null || Object.isFrozen(value);
}
function trackForMutations(isImmutable, ignorePaths, obj) {
  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);
  return {
    detectMutations() {
      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);
    }
  };
}
function trackProperties(isImmutable, ignorePaths = [], obj, path = "", checkedObjects = /* @__PURE__ */ new Set()) {
  const tracked = {
    value: obj
  };
  if (!isImmutable(obj) && !checkedObjects.has(obj)) {
    checkedObjects.add(obj);
    tracked.children = {};
    for (const key in obj) {
      const childPath = path ? path + "." + key : key;
      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {
        continue;
      }
      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);
    }
  }
  return tracked;
}
function detectMutations(isImmutable, ignoredPaths = [], trackedProperty, obj, sameParentRef = false, path = "") {
  const prevObj = trackedProperty ? trackedProperty.value : void 0;
  const sameRef = prevObj === obj;
  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {
    return {
      wasMutated: true,
      path
    };
  }
  if (isImmutable(prevObj) || isImmutable(obj)) {
    return {
      wasMutated: false
    };
  }
  const keysToDetect = {};
  for (let key in trackedProperty.children) {
    keysToDetect[key] = true;
  }
  for (let key in obj) {
    keysToDetect[key] = true;
  }
  const hasIgnoredPaths = ignoredPaths.length > 0;
  for (let key in keysToDetect) {
    const nestedPath = path ? path + "." + key : key;
    if (hasIgnoredPaths) {
      const hasMatches = ignoredPaths.some((ignored) => {
        if (ignored instanceof RegExp) {
          return ignored.test(nestedPath);
        }
        return nestedPath === ignored;
      });
      if (hasMatches) {
        continue;
      }
    }
    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);
    if (result.wasMutated) {
      return result;
    }
  }
  return {
    wasMutated: false
  };
}
function createImmutableStateInvariantMiddleware(options = {}) {
  if (false) // removed by dead control flow
{} else {
    let stringify2 = function(obj, serializer, indent, decycler) {
      return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);
    }, getSerialize2 = function(serializer, decycler) {
      let stack = [], keys = [];
      if (!decycler) decycler = function(_, value) {
        if (stack[0] === value) return "[Circular ~]";
        return "[Circular ~." + keys.slice(0, stack.indexOf(value)).join(".") + "]";
      };
      return function(key, value) {
        if (stack.length > 0) {
          var thisPos = stack.indexOf(this);
          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);
          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);
          if (~stack.indexOf(value)) value = decycler.call(this, key, value);
        } else stack.push(value);
        return serializer == null ? value : serializer.call(this, key, value);
      };
    };
    var stringify = stringify2, getSerialize = getSerialize2;
    let {
      isImmutable = isImmutableDefault,
      ignoredPaths,
      warnAfter = 32
    } = options;
    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);
    return ({
      getState
    }) => {
      let state = getState();
      let tracker = track(state);
      let result;
      return (next) => (action) => {
        const measureUtils = getTimeMeasureUtils(warnAfter, "ImmutableStateInvariantMiddleware");
        measureUtils.measureTime(() => {
          state = getState();
          result = tracker.detectMutations();
          tracker = track(state);
          if (result.wasMutated) {
            throw new Error( false ? 0 : `A state mutation was detected between dispatches, in the path '${result.path || ""}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);
          }
        });
        const dispatchedAction = next(action);
        measureUtils.measureTime(() => {
          state = getState();
          result = tracker.detectMutations();
          tracker = track(state);
          if (result.wasMutated) {
            throw new Error( false ? 0 : `A state mutation was detected inside a dispatch, in the path: ${result.path || ""}. Take a look at the reducer(s) handling the action ${stringify2(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);
          }
        });
        measureUtils.warnIfExceeded();
        return dispatchedAction;
      };
    };
  }
}

// src/serializableStateInvariantMiddleware.ts

function isPlain(val) {
  const type = typeof val;
  return val == null || type === "string" || type === "boolean" || type === "number" || Array.isArray(val) || (0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(val);
}
function findNonSerializableValue(value, path = "", isSerializable = isPlain, getEntries, ignoredPaths = [], cache) {
  let foundNestedSerializable;
  if (!isSerializable(value)) {
    return {
      keyPath: path || "<root>",
      value
    };
  }
  if (typeof value !== "object" || value === null) {
    return false;
  }
  if (cache?.has(value)) return false;
  const entries = getEntries != null ? getEntries(value) : Object.entries(value);
  const hasIgnoredPaths = ignoredPaths.length > 0;
  for (const [key, nestedValue] of entries) {
    const nestedPath = path ? path + "." + key : key;
    if (hasIgnoredPaths) {
      const hasMatches = ignoredPaths.some((ignored) => {
        if (ignored instanceof RegExp) {
          return ignored.test(nestedPath);
        }
        return nestedPath === ignored;
      });
      if (hasMatches) {
        continue;
      }
    }
    if (!isSerializable(nestedValue)) {
      return {
        keyPath: nestedPath,
        value: nestedValue
      };
    }
    if (typeof nestedValue === "object") {
      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);
      if (foundNestedSerializable) {
        return foundNestedSerializable;
      }
    }
  }
  if (cache && isNestedFrozen(value)) cache.add(value);
  return false;
}
function isNestedFrozen(value) {
  if (!Object.isFrozen(value)) return false;
  for (const nestedValue of Object.values(value)) {
    if (typeof nestedValue !== "object" || nestedValue === null) continue;
    if (!isNestedFrozen(nestedValue)) return false;
  }
  return true;
}
function createSerializableStateInvariantMiddleware(options = {}) {
  if (false) // removed by dead control flow
{} else {
    const {
      isSerializable = isPlain,
      getEntries,
      ignoredActions = [],
      ignoredActionPaths = ["meta.arg", "meta.baseQueryMeta"],
      ignoredPaths = [],
      warnAfter = 32,
      ignoreState = false,
      ignoreActions = false,
      disableCache = false
    } = options;
    const cache = !disableCache && WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;
    return (storeAPI) => (next) => (action) => {
      if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {
        return next(action);
      }
      const result = next(action);
      const measureUtils = getTimeMeasureUtils(warnAfter, "SerializableStateInvariantMiddleware");
      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {
        measureUtils.measureTime(() => {
          const foundActionNonSerializableValue = findNonSerializableValue(action, "", isSerializable, getEntries, ignoredActionPaths, cache);
          if (foundActionNonSerializableValue) {
            const {
              keyPath,
              value
            } = foundActionNonSerializableValue;
            console.error(`A non-serializable value was detected in an action, in the path: \`${keyPath}\`. Value:`, value, "\nTake a look at the logic that dispatched this action: ", action, "\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)", "\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)");
          }
        });
      }
      if (!ignoreState) {
        measureUtils.measureTime(() => {
          const state = storeAPI.getState();
          const foundStateNonSerializableValue = findNonSerializableValue(state, "", isSerializable, getEntries, ignoredPaths, cache);
          if (foundStateNonSerializableValue) {
            const {
              keyPath,
              value
            } = foundStateNonSerializableValue;
            console.error(`A non-serializable value was detected in the state, in the path: \`${keyPath}\`. Value:`, value, `
Take a look at the reducer(s) handling this action type: ${action.type}.
(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);
          }
        });
        measureUtils.warnIfExceeded();
      }
      return result;
    };
  }
}

// src/getDefaultMiddleware.ts
function isBoolean(x) {
  return typeof x === "boolean";
}
var buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {
  const {
    thunk = true,
    immutableCheck = true,
    serializableCheck = true,
    actionCreatorCheck = true
  } = options ?? {};
  let middlewareArray = new Tuple();
  if (thunk) {
    if (isBoolean(thunk)) {
      middlewareArray.push(redux_thunk__WEBPACK_IMPORTED_MODULE_3__.thunk);
    } else {
      middlewareArray.push((0,redux_thunk__WEBPACK_IMPORTED_MODULE_3__.withExtraArgument)(thunk.extraArgument));
    }
  }
  if (true) {
    if (immutableCheck) {
      let immutableOptions = {};
      if (!isBoolean(immutableCheck)) {
        immutableOptions = immutableCheck;
      }
      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));
    }
    if (serializableCheck) {
      let serializableOptions = {};
      if (!isBoolean(serializableCheck)) {
        serializableOptions = serializableCheck;
      }
      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));
    }
    if (actionCreatorCheck) {
      let actionCreatorOptions = {};
      if (!isBoolean(actionCreatorCheck)) {
        actionCreatorOptions = actionCreatorCheck;
      }
      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));
    }
  }
  return middlewareArray;
};

// src/autoBatchEnhancer.ts
var SHOULD_AUTOBATCH = "RTK_autoBatch";
var prepareAutoBatched = () => (payload) => ({
  payload,
  meta: {
    [SHOULD_AUTOBATCH]: true
  }
});
var createQueueWithTimer = (timeout) => {
  return (notify) => {
    setTimeout(notify, timeout);
  };
};
var autoBatchEnhancer = (options = {
  type: "raf"
}) => (next) => (...args) => {
  const store = next(...args);
  let notifying = true;
  let shouldNotifyAtEndOfTick = false;
  let notificationQueued = false;
  const listeners = /* @__PURE__ */ new Set();
  const queueCallback = options.type === "tick" ? queueMicrotask : options.type === "raf" ? (
    // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.
    typeof window !== "undefined" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10)
  ) : options.type === "callback" ? options.queueNotification : createQueueWithTimer(options.timeout);
  const notifyListeners = () => {
    notificationQueued = false;
    if (shouldNotifyAtEndOfTick) {
      shouldNotifyAtEndOfTick = false;
      listeners.forEach((l) => l());
    }
  };
  return Object.assign({}, store, {
    // Override the base `store.subscribe` method to keep original listeners
    // from running if we're delaying notifications
    subscribe(listener2) {
      const wrappedListener = () => notifying && listener2();
      const unsubscribe = store.subscribe(wrappedListener);
      listeners.add(listener2);
      return () => {
        unsubscribe();
        listeners.delete(listener2);
      };
    },
    // Override the base `store.dispatch` method so that we can check actions
    // for the `shouldAutoBatch` flag and determine if batching is active
    dispatch(action) {
      try {
        notifying = !action?.meta?.[SHOULD_AUTOBATCH];
        shouldNotifyAtEndOfTick = !notifying;
        if (shouldNotifyAtEndOfTick) {
          if (!notificationQueued) {
            notificationQueued = true;
            queueCallback(notifyListeners);
          }
        }
        return store.dispatch(action);
      } finally {
        notifying = true;
      }
    }
  });
};

// src/getDefaultEnhancers.ts
var buildGetDefaultEnhancers = (middlewareEnhancer) => function getDefaultEnhancers(options) {
  const {
    autoBatch = true
  } = options ?? {};
  let enhancerArray = new Tuple(middlewareEnhancer);
  if (autoBatch) {
    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === "object" ? autoBatch : void 0));
  }
  return enhancerArray;
};

// src/configureStore.ts
function configureStore(options) {
  const getDefaultMiddleware = buildGetDefaultMiddleware();
  const {
    reducer = void 0,
    middleware,
    devTools = true,
    duplicateMiddlewareCheck = true,
    preloadedState = void 0,
    enhancers = void 0
  } = options || {};
  let rootReducer;
  if (typeof reducer === "function") {
    rootReducer = reducer;
  } else if ((0,redux__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(reducer)) {
    rootReducer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducer);
  } else {
    throw new Error( false ? 0 : "`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers");
  }
  if ( true && middleware && typeof middleware !== "function") {
    throw new Error( false ? 0 : "`middleware` field must be a callback");
  }
  let finalMiddleware;
  if (typeof middleware === "function") {
    finalMiddleware = middleware(getDefaultMiddleware);
    if ( true && !Array.isArray(finalMiddleware)) {
      throw new Error( false ? 0 : "when using a middleware builder function, an array of middleware must be returned");
    }
  } else {
    finalMiddleware = getDefaultMiddleware();
  }
  if ( true && finalMiddleware.some((item) => typeof item !== "function")) {
    throw new Error( false ? 0 : "each middleware provided to configureStore must be a function");
  }
  if ( true && duplicateMiddlewareCheck) {
    let middlewareReferences = /* @__PURE__ */ new Set();
    finalMiddleware.forEach((middleware2) => {
      if (middlewareReferences.has(middleware2)) {
        throw new Error( false ? 0 : "Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.");
      }
      middlewareReferences.add(middleware2);
    });
  }
  let finalCompose = redux__WEBPACK_IMPORTED_MODULE_0__.compose;
  if (devTools) {
    finalCompose = composeWithDevTools({
      // Enable capture of stack traces for dispatched Redux actions
      trace: "development" !== "production",
      ...typeof devTools === "object" && devTools
    });
  }
  const middlewareEnhancer = (0,redux__WEBPACK_IMPORTED_MODULE_0__.applyMiddleware)(...finalMiddleware);
  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);
  if ( true && enhancers && typeof enhancers !== "function") {
    throw new Error( false ? 0 : "`enhancers` field must be a callback");
  }
  let storeEnhancers = typeof enhancers === "function" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();
  if ( true && !Array.isArray(storeEnhancers)) {
    throw new Error( false ? 0 : "`enhancers` callback must return an array");
  }
  if ( true && storeEnhancers.some((item) => typeof item !== "function")) {
    throw new Error( false ? 0 : "each enhancer provided to configureStore must be a function");
  }
  if ( true && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {
    console.error("middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`");
  }
  const composedEnhancer = finalCompose(...storeEnhancers);
  return (0,redux__WEBPACK_IMPORTED_MODULE_0__.createStore)(rootReducer, preloadedState, composedEnhancer);
}

// src/createReducer.ts


// src/mapBuilders.ts
function executeReducerBuilderCallback(builderCallback) {
  const actionsMap = {};
  const actionMatchers = [];
  let defaultCaseReducer;
  const builder = {
    addCase(typeOrActionCreator, reducer) {
      if (true) {
        if (actionMatchers.length > 0) {
          throw new Error( false ? 0 : "`builder.addCase` should only be called before calling `builder.addMatcher`");
        }
        if (defaultCaseReducer) {
          throw new Error( false ? 0 : "`builder.addCase` should only be called before calling `builder.addDefaultCase`");
        }
      }
      const type = typeof typeOrActionCreator === "string" ? typeOrActionCreator : typeOrActionCreator.type;
      if (!type) {
        throw new Error( false ? 0 : "`builder.addCase` cannot be called with an empty action type");
      }
      if (type in actionsMap) {
        throw new Error( false ? 0 : `\`builder.addCase\` cannot be called with two reducers for the same action type '${type}'`);
      }
      actionsMap[type] = reducer;
      return builder;
    },
    addMatcher(matcher, reducer) {
      if (true) {
        if (defaultCaseReducer) {
          throw new Error( false ? 0 : "`builder.addMatcher` should only be called before calling `builder.addDefaultCase`");
        }
      }
      actionMatchers.push({
        matcher,
        reducer
      });
      return builder;
    },
    addDefaultCase(reducer) {
      if (true) {
        if (defaultCaseReducer) {
          throw new Error( false ? 0 : "`builder.addDefaultCase` can only be called once");
        }
      }
      defaultCaseReducer = reducer;
      return builder;
    }
  };
  builderCallback(builder);
  return [actionsMap, actionMatchers, defaultCaseReducer];
}

// src/createReducer.ts
function isStateFunction(x) {
  return typeof x === "function";
}
function createReducer(initialState, mapOrBuilderCallback) {
  if (true) {
    if (typeof mapOrBuilderCallback === "object") {
      throw new Error( false ? 0 : "The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer");
    }
  }
  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);
  let getInitialState;
  if (isStateFunction(initialState)) {
    getInitialState = () => freezeDraftable(initialState());
  } else {
    const frozenInitialState = freezeDraftable(initialState);
    getInitialState = () => frozenInitialState;
  }
  function reducer(state = getInitialState(), action) {
    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({
      matcher
    }) => matcher(action)).map(({
      reducer: reducer2
    }) => reducer2)];
    if (caseReducers.filter((cr) => !!cr).length === 0) {
      caseReducers = [finalDefaultCaseReducer];
    }
    return caseReducers.reduce((previousState, caseReducer) => {
      if (caseReducer) {
        if ((0,immer__WEBPACK_IMPORTED_MODULE_1__.isDraft)(previousState)) {
          const draft = previousState;
          const result = caseReducer(draft, action);
          if (result === void 0) {
            return previousState;
          }
          return result;
        } else if (!(0,immer__WEBPACK_IMPORTED_MODULE_1__.isDraftable)(previousState)) {
          const result = caseReducer(previousState, action);
          if (result === void 0) {
            if (previousState === null) {
              return previousState;
            }
            throw Error("A case reducer on a non-draftable value must not return undefined");
          }
          return result;
        } else {
          return (0,immer__WEBPACK_IMPORTED_MODULE_1__.produce)(previousState, (draft) => {
            return caseReducer(draft, action);
          });
        }
      }
      return previousState;
    }, state);
  }
  reducer.getInitialState = getInitialState;
  return reducer;
}

// src/matchers.ts
var matches = (matcher, action) => {
  if (hasMatchFunction(matcher)) {
    return matcher.match(action);
  } else {
    return matcher(action);
  }
};
function isAnyOf(...matchers) {
  return (action) => {
    return matchers.some((matcher) => matches(matcher, action));
  };
}
function isAllOf(...matchers) {
  return (action) => {
    return matchers.every((matcher) => matches(matcher, action));
  };
}
function hasExpectedRequestMetadata(action, validStatus) {
  if (!action || !action.meta) return false;
  const hasValidRequestId = typeof action.meta.requestId === "string";
  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;
  return hasValidRequestId && hasValidRequestStatus;
}
function isAsyncThunkArray(a) {
  return typeof a[0] === "function" && "pending" in a[0] && "fulfilled" in a[0] && "rejected" in a[0];
}
function isPending(...asyncThunks) {
  if (asyncThunks.length === 0) {
    return (action) => hasExpectedRequestMetadata(action, ["pending"]);
  }
  if (!isAsyncThunkArray(asyncThunks)) {
    return isPending()(asyncThunks[0]);
  }
  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.pending));
}
function isRejected(...asyncThunks) {
  if (asyncThunks.length === 0) {
    return (action) => hasExpectedRequestMetadata(action, ["rejected"]);
  }
  if (!isAsyncThunkArray(asyncThunks)) {
    return isRejected()(asyncThunks[0]);
  }
  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.rejected));
}
function isRejectedWithValue(...asyncThunks) {
  const hasFlag = (action) => {
    return action && action.meta && action.meta.rejectedWithValue;
  };
  if (asyncThunks.length === 0) {
    return isAllOf(isRejected(...asyncThunks), hasFlag);
  }
  if (!isAsyncThunkArray(asyncThunks)) {
    return isRejectedWithValue()(asyncThunks[0]);
  }
  return isAllOf(isRejected(...asyncThunks), hasFlag);
}
function isFulfilled(...asyncThunks) {
  if (asyncThunks.length === 0) {
    return (action) => hasExpectedRequestMetadata(action, ["fulfilled"]);
  }
  if (!isAsyncThunkArray(asyncThunks)) {
    return isFulfilled()(asyncThunks[0]);
  }
  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.fulfilled));
}
function isAsyncThunkAction(...asyncThunks) {
  if (asyncThunks.length === 0) {
    return (action) => hasExpectedRequestMetadata(action, ["pending", "fulfilled", "rejected"]);
  }
  if (!isAsyncThunkArray(asyncThunks)) {
    return isAsyncThunkAction()(asyncThunks[0]);
  }
  return isAnyOf(...asyncThunks.flatMap((asyncThunk) => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));
}

// src/nanoid.ts
var urlAlphabet = "ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW";
var nanoid = (size = 21) => {
  let id = "";
  let i = size;
  while (i--) {
    id += urlAlphabet[Math.random() * 64 | 0];
  }
  return id;
};

// src/createAsyncThunk.ts
var commonProperties = ["name", "message", "stack", "code"];
var RejectWithValue = class {
  constructor(payload, meta) {
    this.payload = payload;
    this.meta = meta;
  }
  /*
  type-only property to distinguish between RejectWithValue and FulfillWithMeta
  does not exist at runtime
  */
  _type;
};
var FulfillWithMeta = class {
  constructor(payload, meta) {
    this.payload = payload;
    this.meta = meta;
  }
  /*
  type-only property to distinguish between RejectWithValue and FulfillWithMeta
  does not exist at runtime
  */
  _type;
};
var miniSerializeError = (value) => {
  if (typeof value === "object" && value !== null) {
    const simpleError = {};
    for (const property of commonProperties) {
      if (typeof value[property] === "string") {
        simpleError[property] = value[property];
      }
    }
    return simpleError;
  }
  return {
    message: String(value)
  };
};
var externalAbortMessage = "External signal was aborted";
var createAsyncThunk = /* @__PURE__ */ (() => {
  function createAsyncThunk2(typePrefix, payloadCreator, options) {
    const fulfilled = createAction(typePrefix + "/fulfilled", (payload, requestId, arg, meta) => ({
      payload,
      meta: {
        ...meta || {},
        arg,
        requestId,
        requestStatus: "fulfilled"
      }
    }));
    const pending = createAction(typePrefix + "/pending", (requestId, arg, meta) => ({
      payload: void 0,
      meta: {
        ...meta || {},
        arg,
        requestId,
        requestStatus: "pending"
      }
    }));
    const rejected = createAction(typePrefix + "/rejected", (error, requestId, arg, payload, meta) => ({
      payload,
      error: (options && options.serializeError || miniSerializeError)(error || "Rejected"),
      meta: {
        ...meta || {},
        arg,
        requestId,
        rejectedWithValue: !!payload,
        requestStatus: "rejected",
        aborted: error?.name === "AbortError",
        condition: error?.name === "ConditionError"
      }
    }));
    function actionCreator(arg, {
      signal
    } = {}) {
      return (dispatch, getState, extra) => {
        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();
        const abortController = new AbortController();
        let abortHandler;
        let abortReason;
        function abort(reason) {
          abortReason = reason;
          abortController.abort();
        }
        if (signal) {
          if (signal.aborted) {
            abort(externalAbortMessage);
          } else {
            signal.addEventListener("abort", () => abort(externalAbortMessage), {
              once: true
            });
          }
        }
        const promise = async function() {
          let finalAction;
          try {
            let conditionResult = options?.condition?.(arg, {
              getState,
              extra
            });
            if (isThenable(conditionResult)) {
              conditionResult = await conditionResult;
            }
            if (conditionResult === false || abortController.signal.aborted) {
              throw {
                name: "ConditionError",
                message: "Aborted due to condition callback returning false."
              };
            }
            const abortedPromise = new Promise((_, reject) => {
              abortHandler = () => {
                reject({
                  name: "AbortError",
                  message: abortReason || "Aborted"
                });
              };
              abortController.signal.addEventListener("abort", abortHandler);
            });
            dispatch(pending(requestId, arg, options?.getPendingMeta?.({
              requestId,
              arg
            }, {
              getState,
              extra
            })));
            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {
              dispatch,
              getState,
              extra,
              requestId,
              signal: abortController.signal,
              abort,
              rejectWithValue: (value, meta) => {
                return new RejectWithValue(value, meta);
              },
              fulfillWithValue: (value, meta) => {
                return new FulfillWithMeta(value, meta);
              }
            })).then((result) => {
              if (result instanceof RejectWithValue) {
                throw result;
              }
              if (result instanceof FulfillWithMeta) {
                return fulfilled(result.payload, requestId, arg, result.meta);
              }
              return fulfilled(result, requestId, arg);
            })]);
          } catch (err) {
            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);
          } finally {
            if (abortHandler) {
              abortController.signal.removeEventListener("abort", abortHandler);
            }
          }
          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;
          if (!skipDispatch) {
            dispatch(finalAction);
          }
          return finalAction;
        }();
        return Object.assign(promise, {
          abort,
          requestId,
          arg,
          unwrap() {
            return promise.then(unwrapResult);
          }
        });
      };
    }
    return Object.assign(actionCreator, {
      pending,
      rejected,
      fulfilled,
      settled: isAnyOf(rejected, fulfilled),
      typePrefix
    });
  }
  createAsyncThunk2.withTypes = () => createAsyncThunk2;
  return createAsyncThunk2;
})();
function unwrapResult(action) {
  if (action.meta && action.meta.rejectedWithValue) {
    throw action.payload;
  }
  if (action.error) {
    throw action.error;
  }
  return action.payload;
}
function isThenable(value) {
  return value !== null && typeof value === "object" && typeof value.then === "function";
}

// src/createSlice.ts
var asyncThunkSymbol = /* @__PURE__ */ Symbol.for("rtk-slice-createasyncthunk");
var asyncThunkCreator = {
  [asyncThunkSymbol]: createAsyncThunk
};
var ReducerType = /* @__PURE__ */ ((ReducerType2) => {
  ReducerType2["reducer"] = "reducer";
  ReducerType2["reducerWithPrepare"] = "reducerWithPrepare";
  ReducerType2["asyncThunk"] = "asyncThunk";
  return ReducerType2;
})(ReducerType || {});
function getType(slice, actionKey) {
  return `${slice}/${actionKey}`;
}
function buildCreateSlice({
  creators
} = {}) {
  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];
  return function createSlice2(options) {
    const {
      name,
      reducerPath = name
    } = options;
    if (!name) {
      throw new Error( false ? 0 : "`name` is a required option for createSlice");
    }
    if (typeof process !== "undefined" && "development" === "development") {
      if (options.initialState === void 0) {
        console.error("You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`");
      }
    }
    const reducers = (typeof options.reducers === "function" ? options.reducers(buildReducerCreators()) : options.reducers) || {};
    const reducerNames = Object.keys(reducers);
    const context = {
      sliceCaseReducersByName: {},
      sliceCaseReducersByType: {},
      actionCreators: {},
      sliceMatchers: []
    };
    const contextMethods = {
      addCase(typeOrActionCreator, reducer2) {
        const type = typeof typeOrActionCreator === "string" ? typeOrActionCreator : typeOrActionCreator.type;
        if (!type) {
          throw new Error( false ? 0 : "`context.addCase` cannot be called with an empty action type");
        }
        if (type in context.sliceCaseReducersByType) {
          throw new Error( false ? 0 : "`context.addCase` cannot be called with two reducers for the same action type: " + type);
        }
        context.sliceCaseReducersByType[type] = reducer2;
        return contextMethods;
      },
      addMatcher(matcher, reducer2) {
        context.sliceMatchers.push({
          matcher,
          reducer: reducer2
        });
        return contextMethods;
      },
      exposeAction(name2, actionCreator) {
        context.actionCreators[name2] = actionCreator;
        return contextMethods;
      },
      exposeCaseReducer(name2, reducer2) {
        context.sliceCaseReducersByName[name2] = reducer2;
        return contextMethods;
      }
    };
    reducerNames.forEach((reducerName) => {
      const reducerDefinition = reducers[reducerName];
      const reducerDetails = {
        reducerName,
        type: getType(name, reducerName),
        createNotation: typeof options.reducers === "function"
      };
      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {
        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);
      } else {
        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);
      }
    });
    function buildReducer() {
      if (true) {
        if (typeof options.extraReducers === "object") {
          throw new Error( false ? 0 : "The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice");
        }
      }
      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === "function" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];
      const finalCaseReducers = {
        ...extraReducers,
        ...context.sliceCaseReducersByType
      };
      return createReducer(options.initialState, (builder) => {
        for (let key in finalCaseReducers) {
          builder.addCase(key, finalCaseReducers[key]);
        }
        for (let sM of context.sliceMatchers) {
          builder.addMatcher(sM.matcher, sM.reducer);
        }
        for (let m of actionMatchers) {
          builder.addMatcher(m.matcher, m.reducer);
        }
        if (defaultCaseReducer) {
          builder.addDefaultCase(defaultCaseReducer);
        }
      });
    }
    const selectSelf = (state) => state;
    const injectedSelectorCache = /* @__PURE__ */ new Map();
    const injectedStateCache = /* @__PURE__ */ new WeakMap();
    let _reducer;
    function reducer(state, action) {
      if (!_reducer) _reducer = buildReducer();
      return _reducer(state, action);
    }
    function getInitialState() {
      if (!_reducer) _reducer = buildReducer();
      return _reducer.getInitialState();
    }
    function makeSelectorProps(reducerPath2, injected = false) {
      function selectSlice(state) {
        let sliceState = state[reducerPath2];
        if (typeof sliceState === "undefined") {
          if (injected) {
            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);
          } else if (true) {
            throw new Error( false ? 0 : "selectSlice returned undefined for an uninjected slice reducer");
          }
        }
        return sliceState;
      }
      function getSelectors(selectState = selectSelf) {
        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */ new WeakMap());
        return getOrInsertComputed(selectorCache, selectState, () => {
          const map = {};
          for (const [name2, selector] of Object.entries(options.selectors ?? {})) {
            map[name2] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);
          }
          return map;
        });
      }
      return {
        reducerPath: reducerPath2,
        getSelectors,
        get selectors() {
          return getSelectors(selectSlice);
        },
        selectSlice
      };
    }
    const slice = {
      name,
      reducer,
      actions: context.actionCreators,
      caseReducers: context.sliceCaseReducersByName,
      getInitialState,
      ...makeSelectorProps(reducerPath),
      injectInto(injectable, {
        reducerPath: pathOpt,
        ...config
      } = {}) {
        const newReducerPath = pathOpt ?? reducerPath;
        injectable.inject({
          reducerPath: newReducerPath,
          reducer
        }, config);
        return {
          ...slice,
          ...makeSelectorProps(newReducerPath, true)
        };
      }
    };
    return slice;
  };
}
function wrapSelector(selector, selectState, getInitialState, injected) {
  function wrapper(rootState, ...args) {
    let sliceState = selectState(rootState);
    if (typeof sliceState === "undefined") {
      if (injected) {
        sliceState = getInitialState();
      } else if (true) {
        throw new Error( false ? 0 : "selectState returned undefined for an uninjected slice reducer");
      }
    }
    return selector(sliceState, ...args);
  }
  wrapper.unwrapped = selector;
  return wrapper;
}
var createSlice = /* @__PURE__ */ buildCreateSlice();
function buildReducerCreators() {
  function asyncThunk(payloadCreator, config) {
    return {
      _reducerDefinitionType: "asyncThunk" /* asyncThunk */,
      payloadCreator,
      ...config
    };
  }
  asyncThunk.withTypes = () => asyncThunk;
  return {
    reducer(caseReducer) {
      return Object.assign({
        // hack so the wrapping function has the same name as the original
        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original
        [caseReducer.name](...args) {
          return caseReducer(...args);
        }
      }[caseReducer.name], {
        _reducerDefinitionType: "reducer" /* reducer */
      });
    },
    preparedReducer(prepare, reducer) {
      return {
        _reducerDefinitionType: "reducerWithPrepare" /* reducerWithPrepare */,
        prepare,
        reducer
      };
    },
    asyncThunk
  };
}
function handleNormalReducerDefinition({
  type,
  reducerName,
  createNotation
}, maybeReducerWithPrepare, context) {
  let caseReducer;
  let prepareCallback;
  if ("reducer" in maybeReducerWithPrepare) {
    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {
      throw new Error( false ? 0 : "Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.");
    }
    caseReducer = maybeReducerWithPrepare.reducer;
    prepareCallback = maybeReducerWithPrepare.prepare;
  } else {
    caseReducer = maybeReducerWithPrepare;
  }
  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));
}
function isAsyncThunkSliceReducerDefinition(reducerDefinition) {
  return reducerDefinition._reducerDefinitionType === "asyncThunk" /* asyncThunk */;
}
function isCaseReducerWithPrepareDefinition(reducerDefinition) {
  return reducerDefinition._reducerDefinitionType === "reducerWithPrepare" /* reducerWithPrepare */;
}
function handleThunkCaseReducerDefinition({
  type,
  reducerName
}, reducerDefinition, context, cAT) {
  if (!cAT) {
    throw new Error( false ? 0 : "Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.");
  }
  const {
    payloadCreator,
    fulfilled,
    pending,
    rejected,
    settled,
    options
  } = reducerDefinition;
  const thunk = cAT(type, payloadCreator, options);
  context.exposeAction(reducerName, thunk);
  if (fulfilled) {
    context.addCase(thunk.fulfilled, fulfilled);
  }
  if (pending) {
    context.addCase(thunk.pending, pending);
  }
  if (rejected) {
    context.addCase(thunk.rejected, rejected);
  }
  if (settled) {
    context.addMatcher(thunk.settled, settled);
  }
  context.exposeCaseReducer(reducerName, {
    fulfilled: fulfilled || noop,
    pending: pending || noop,
    rejected: rejected || noop,
    settled: settled || noop
  });
}
function noop() {
}

// src/entities/entity_state.ts
function getInitialEntityState() {
  return {
    ids: [],
    entities: {}
  };
}
function createInitialStateFactory(stateAdapter) {
  function getInitialState(additionalState = {}, entities) {
    const state = Object.assign(getInitialEntityState(), additionalState);
    return entities ? stateAdapter.setAll(state, entities) : state;
  }
  return {
    getInitialState
  };
}

// src/entities/state_selectors.ts
function createSelectorsFactory() {
  function getSelectors(selectState, options = {}) {
    const {
      createSelector: createSelector2 = createDraftSafeSelector
    } = options;
    const selectIds = (state) => state.ids;
    const selectEntities = (state) => state.entities;
    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map((id) => entities[id]));
    const selectId = (_, id) => id;
    const selectById = (entities, id) => entities[id];
    const selectTotal = createSelector2(selectIds, (ids) => ids.length);
    if (!selectState) {
      return {
        selectIds,
        selectEntities,
        selectAll,
        selectTotal,
        selectById: createSelector2(selectEntities, selectId, selectById)
      };
    }
    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);
    return {
      selectIds: createSelector2(selectState, selectIds),
      selectEntities: selectGlobalizedEntities,
      selectAll: createSelector2(selectState, selectAll),
      selectTotal: createSelector2(selectState, selectTotal),
      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)
    };
  }
  return {
    getSelectors
  };
}

// src/entities/state_adapter.ts

var isDraftTyped = immer__WEBPACK_IMPORTED_MODULE_1__.isDraft;
function createSingleArgumentStateOperator(mutator) {
  const operator = createStateOperator((_, state) => mutator(state));
  return function operation(state) {
    return operator(state, void 0);
  };
}
function createStateOperator(mutator) {
  return function operation(state, arg) {
    function isPayloadActionArgument(arg2) {
      return isFSA(arg2);
    }
    const runMutator = (draft) => {
      if (isPayloadActionArgument(arg)) {
        mutator(arg.payload, draft);
      } else {
        mutator(arg, draft);
      }
    };
    if (isDraftTyped(state)) {
      runMutator(state);
      return state;
    }
    return (0,immer__WEBPACK_IMPORTED_MODULE_1__.produce)(state, runMutator);
  };
}

// src/entities/utils.ts

function selectIdValue(entity, selectId) {
  const key = selectId(entity);
  if ( true && key === void 0) {
    console.warn("The entity passed to the `selectId` implementation returned undefined.", "You should probably provide your own `selectId` implementation.", "The entity that was passed:", entity, "The `selectId` implementation:", selectId.toString());
  }
  return key;
}
function ensureEntitiesArray(entities) {
  if (!Array.isArray(entities)) {
    entities = Object.values(entities);
  }
  return entities;
}
function getCurrent(value) {
  return (0,immer__WEBPACK_IMPORTED_MODULE_1__.isDraft)(value) ? (0,immer__WEBPACK_IMPORTED_MODULE_1__.current)(value) : value;
}
function splitAddedUpdatedEntities(newEntities, selectId, state) {
  newEntities = ensureEntitiesArray(newEntities);
  const existingIdsArray = getCurrent(state.ids);
  const existingIds = new Set(existingIdsArray);
  const added = [];
  const addedIds = /* @__PURE__ */ new Set([]);
  const updated = [];
  for (const entity of newEntities) {
    const id = selectIdValue(entity, selectId);
    if (existingIds.has(id) || addedIds.has(id)) {
      updated.push({
        id,
        changes: entity
      });
    } else {
      addedIds.add(id);
      added.push(entity);
    }
  }
  return [added, updated, existingIdsArray];
}

// src/entities/unsorted_state_adapter.ts
function createUnsortedStateAdapter(selectId) {
  function addOneMutably(entity, state) {
    const key = selectIdValue(entity, selectId);
    if (key in state.entities) {
      return;
    }
    state.ids.push(key);
    state.entities[key] = entity;
  }
  function addManyMutably(newEntities, state) {
    newEntities = ensureEntitiesArray(newEntities);
    for (const entity of newEntities) {
      addOneMutably(entity, state);
    }
  }
  function setOneMutably(entity, state) {
    const key = selectIdValue(entity, selectId);
    if (!(key in state.entities)) {
      state.ids.push(key);
    }
    ;
    state.entities[key] = entity;
  }
  function setManyMutably(newEntities, state) {
    newEntities = ensureEntitiesArray(newEntities);
    for (const entity of newEntities) {
      setOneMutably(entity, state);
    }
  }
  function setAllMutably(newEntities, state) {
    newEntities = ensureEntitiesArray(newEntities);
    state.ids = [];
    state.entities = {};
    addManyMutably(newEntities, state);
  }
  function removeOneMutably(key, state) {
    return removeManyMutably([key], state);
  }
  function removeManyMutably(keys, state) {
    let didMutate = false;
    keys.forEach((key) => {
      if (key in state.entities) {
        delete state.entities[key];
        didMutate = true;
      }
    });
    if (didMutate) {
      state.ids = state.ids.filter((id) => id in state.entities);
    }
  }
  function removeAllMutably(state) {
    Object.assign(state, {
      ids: [],
      entities: {}
    });
  }
  function takeNewKey(keys, update, state) {
    const original3 = state.entities[update.id];
    if (original3 === void 0) {
      return false;
    }
    const updated = Object.assign({}, original3, update.changes);
    const newKey = selectIdValue(updated, selectId);
    const hasNewKey = newKey !== update.id;
    if (hasNewKey) {
      keys[update.id] = newKey;
      delete state.entities[update.id];
    }
    ;
    state.entities[newKey] = updated;
    return hasNewKey;
  }
  function updateOneMutably(update, state) {
    return updateManyMutably([update], state);
  }
  function updateManyMutably(updates, state) {
    const newKeys = {};
    const updatesPerEntity = {};
    updates.forEach((update) => {
      if (update.id in state.entities) {
        updatesPerEntity[update.id] = {
          id: update.id,
          // Spreads ignore falsy values, so this works even if there isn't
          // an existing update already at this key
          changes: {
            ...updatesPerEntity[update.id]?.changes,
            ...update.changes
          }
        };
      }
    });
    updates = Object.values(updatesPerEntity);
    const didMutateEntities = updates.length > 0;
    if (didMutateEntities) {
      const didMutateIds = updates.filter((update) => takeNewKey(newKeys, update, state)).length > 0;
      if (didMutateIds) {
        state.ids = Object.values(state.entities).map((e) => selectIdValue(e, selectId));
      }
    }
  }
  function upsertOneMutably(entity, state) {
    return upsertManyMutably([entity], state);
  }
  function upsertManyMutably(newEntities, state) {
    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);
    addManyMutably(added, state);
    updateManyMutably(updated, state);
  }
  return {
    removeAll: createSingleArgumentStateOperator(removeAllMutably),
    addOne: createStateOperator(addOneMutably),
    addMany: createStateOperator(addManyMutably),
    setOne: createStateOperator(setOneMutably),
    setMany: createStateOperator(setManyMutably),
    setAll: createStateOperator(setAllMutably),
    updateOne: createStateOperator(updateOneMutably),
    updateMany: createStateOperator(updateManyMutably),
    upsertOne: createStateOperator(upsertOneMutably),
    upsertMany: createStateOperator(upsertManyMutably),
    removeOne: createStateOperator(removeOneMutably),
    removeMany: createStateOperator(removeManyMutably)
  };
}

// src/entities/sorted_state_adapter.ts
function findInsertIndex(sortedItems, item, comparisonFunction) {
  let lowIndex = 0;
  let highIndex = sortedItems.length;
  while (lowIndex < highIndex) {
    let middleIndex = lowIndex + highIndex >>> 1;
    const currentItem = sortedItems[middleIndex];
    const res = comparisonFunction(item, currentItem);
    if (res >= 0) {
      lowIndex = middleIndex + 1;
    } else {
      highIndex = middleIndex;
    }
  }
  return lowIndex;
}
function insert(sortedItems, item, comparisonFunction) {
  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);
  sortedItems.splice(insertAtIndex, 0, item);
  return sortedItems;
}
function createSortedStateAdapter(selectId, comparer) {
  const {
    removeOne,
    removeMany,
    removeAll
  } = createUnsortedStateAdapter(selectId);
  function addOneMutably(entity, state) {
    return addManyMutably([entity], state);
  }
  function addManyMutably(newEntities, state, existingIds) {
    newEntities = ensureEntitiesArray(newEntities);
    const existingKeys = new Set(existingIds ?? getCurrent(state.ids));
    const models = newEntities.filter((model) => !existingKeys.has(selectIdValue(model, selectId)));
    if (models.length !== 0) {
      mergeFunction(state, models);
    }
  }
  function setOneMutably(entity, state) {
    return setManyMutably([entity], state);
  }
  function setManyMutably(newEntities, state) {
    newEntities = ensureEntitiesArray(newEntities);
    if (newEntities.length !== 0) {
      for (const item of newEntities) {
        delete state.entities[selectId(item)];
      }
      mergeFunction(state, newEntities);
    }
  }
  function setAllMutably(newEntities, state) {
    newEntities = ensureEntitiesArray(newEntities);
    state.entities = {};
    state.ids = [];
    addManyMutably(newEntities, state, []);
  }
  function updateOneMutably(update, state) {
    return updateManyMutably([update], state);
  }
  function updateManyMutably(updates, state) {
    let appliedUpdates = false;
    let replacedIds = false;
    for (let update of updates) {
      const entity = state.entities[update.id];
      if (!entity) {
        continue;
      }
      appliedUpdates = true;
      Object.assign(entity, update.changes);
      const newId = selectId(entity);
      if (update.id !== newId) {
        replacedIds = true;
        delete state.entities[update.id];
        const oldIndex = state.ids.indexOf(update.id);
        state.ids[oldIndex] = newId;
        state.entities[newId] = entity;
      }
    }
    if (appliedUpdates) {
      mergeFunction(state, [], appliedUpdates, replacedIds);
    }
  }
  function upsertOneMutably(entity, state) {
    return upsertManyMutably([entity], state);
  }
  function upsertManyMutably(newEntities, state) {
    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);
    if (added.length) {
      addManyMutably(added, state, existingIdsArray);
    }
    if (updated.length) {
      updateManyMutably(updated, state);
    }
  }
  function areArraysEqual(a, b) {
    if (a.length !== b.length) {
      return false;
    }
    for (let i = 0; i < a.length; i++) {
      if (a[i] === b[i]) {
        continue;
      }
      return false;
    }
    return true;
  }
  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {
    const currentEntities = getCurrent(state.entities);
    const currentIds = getCurrent(state.ids);
    const stateEntities = state.entities;
    let ids = currentIds;
    if (replacedIds) {
      ids = new Set(currentIds);
    }
    let sortedEntities = [];
    for (const id of ids) {
      const entity = currentEntities[id];
      if (entity) {
        sortedEntities.push(entity);
      }
    }
    const wasPreviouslyEmpty = sortedEntities.length === 0;
    for (const item of addedItems) {
      stateEntities[selectId(item)] = item;
      if (!wasPreviouslyEmpty) {
        insert(sortedEntities, item, comparer);
      }
    }
    if (wasPreviouslyEmpty) {
      sortedEntities = addedItems.slice().sort(comparer);
    } else if (appliedUpdates) {
      sortedEntities.sort(comparer);
    }
    const newSortedIds = sortedEntities.map(selectId);
    if (!areArraysEqual(currentIds, newSortedIds)) {
      state.ids = newSortedIds;
    }
  };
  return {
    removeOne,
    removeMany,
    removeAll,
    addOne: createStateOperator(addOneMutably),
    updateOne: createStateOperator(updateOneMutably),
    upsertOne: createStateOperator(upsertOneMutably),
    setOne: createStateOperator(setOneMutably),
    setMany: createStateOperator(setManyMutably),
    setAll: createStateOperator(setAllMutably),
    addMany: createStateOperator(addManyMutably),
    updateMany: createStateOperator(updateManyMutably),
    upsertMany: createStateOperator(upsertManyMutably)
  };
}

// src/entities/create_adapter.ts
function createEntityAdapter(options = {}) {
  const {
    selectId,
    sortComparer
  } = {
    sortComparer: false,
    selectId: (instance) => instance.id,
    ...options
  };
  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);
  const stateFactory = createInitialStateFactory(stateAdapter);
  const selectorsFactory = createSelectorsFactory();
  return {
    selectId,
    sortComparer,
    ...stateFactory,
    ...selectorsFactory,
    ...stateAdapter
  };
}

// src/listenerMiddleware/index.ts


// src/listenerMiddleware/exceptions.ts
var task = "task";
var listener = "listener";
var completed = "completed";
var cancelled = "cancelled";
var taskCancelled = `task-${cancelled}`;
var taskCompleted = `task-${completed}`;
var listenerCancelled = `${listener}-${cancelled}`;
var listenerCompleted = `${listener}-${completed}`;
var TaskAbortError = class {
  constructor(code) {
    this.code = code;
    this.message = `${task} ${cancelled} (reason: ${code})`;
  }
  name = "TaskAbortError";
  message;
};

// src/listenerMiddleware/utils.ts
var assertFunction = (func, expected) => {
  if (typeof func !== "function") {
    throw new TypeError( false ? 0 : `${expected} is not a function`);
  }
};
var noop2 = () => {
};
var catchRejection = (promise, onError = noop2) => {
  promise.catch(onError);
  return promise;
};
var addAbortSignalListener = (abortSignal, callback) => {
  abortSignal.addEventListener("abort", callback, {
    once: true
  });
  return () => abortSignal.removeEventListener("abort", callback);
};
var abortControllerWithReason = (abortController, reason) => {
  const signal = abortController.signal;
  if (signal.aborted) {
    return;
  }
  if (!("reason" in signal)) {
    Object.defineProperty(signal, "reason", {
      enumerable: true,
      value: reason,
      configurable: true,
      writable: true
    });
  }
  ;
  abortController.abort(reason);
};

// src/listenerMiddleware/task.ts
var validateActive = (signal) => {
  if (signal.aborted) {
    const {
      reason
    } = signal;
    throw new TaskAbortError(reason);
  }
};
function raceWithSignal(signal, promise) {
  let cleanup = noop2;
  return new Promise((resolve, reject) => {
    const notifyRejection = () => reject(new TaskAbortError(signal.reason));
    if (signal.aborted) {
      notifyRejection();
      return;
    }
    cleanup = addAbortSignalListener(signal, notifyRejection);
    promise.finally(() => cleanup()).then(resolve, reject);
  }).finally(() => {
    cleanup = noop2;
  });
}
var runTask = async (task2, cleanUp) => {
  try {
    await Promise.resolve();
    const value = await task2();
    return {
      status: "ok",
      value
    };
  } catch (error) {
    return {
      status: error instanceof TaskAbortError ? "cancelled" : "rejected",
      error
    };
  } finally {
    cleanUp?.();
  }
};
var createPause = (signal) => {
  return (promise) => {
    return catchRejection(raceWithSignal(signal, promise).then((output) => {
      validateActive(signal);
      return output;
    }));
  };
};
var createDelay = (signal) => {
  const pause = createPause(signal);
  return (timeoutMs) => {
    return pause(new Promise((resolve) => setTimeout(resolve, timeoutMs)));
  };
};

// src/listenerMiddleware/index.ts
var {
  assign
} = Object;
var INTERNAL_NIL_TOKEN = {};
var alm = "listenerMiddleware";
var createFork = (parentAbortSignal, parentBlockingPromises) => {
  const linkControllers = (controller) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));
  return (taskExecutor, opts) => {
    assertFunction(taskExecutor, "taskExecutor");
    const childAbortController = new AbortController();
    linkControllers(childAbortController);
    const result = runTask(async () => {
      validateActive(parentAbortSignal);
      validateActive(childAbortController.signal);
      const result2 = await taskExecutor({
        pause: createPause(childAbortController.signal),
        delay: createDelay(childAbortController.signal),
        signal: childAbortController.signal
      });
      validateActive(childAbortController.signal);
      return result2;
    }, () => abortControllerWithReason(childAbortController, taskCompleted));
    if (opts?.autoJoin) {
      parentBlockingPromises.push(result.catch(noop2));
    }
    return {
      result: createPause(parentAbortSignal)(result),
      cancel() {
        abortControllerWithReason(childAbortController, taskCancelled);
      }
    };
  };
};
var createTakePattern = (startListening, signal) => {
  const take = async (predicate, timeout) => {
    validateActive(signal);
    let unsubscribe = () => {
    };
    const tuplePromise = new Promise((resolve, reject) => {
      let stopListening = startListening({
        predicate,
        effect: (action, listenerApi) => {
          listenerApi.unsubscribe();
          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);
        }
      });
      unsubscribe = () => {
        stopListening();
        reject();
      };
    });
    const promises = [tuplePromise];
    if (timeout != null) {
      promises.push(new Promise((resolve) => setTimeout(resolve, timeout, null)));
    }
    try {
      const output = await raceWithSignal(signal, Promise.race(promises));
      validateActive(signal);
      return output;
    } finally {
      unsubscribe();
    }
  };
  return (predicate, timeout) => catchRejection(take(predicate, timeout));
};
var getListenerEntryPropsFrom = (options) => {
  let {
    type,
    actionCreator,
    matcher,
    predicate,
    effect
  } = options;
  if (type) {
    predicate = createAction(type).match;
  } else if (actionCreator) {
    type = actionCreator.type;
    predicate = actionCreator.match;
  } else if (matcher) {
    predicate = matcher;
  } else if (predicate) {
  } else {
    throw new Error( false ? 0 : "Creating or removing a listener requires one of the known fields for matching an action");
  }
  assertFunction(effect, "options.listener");
  return {
    predicate,
    type,
    effect
  };
};
var createListenerEntry = /* @__PURE__ */ assign((options) => {
  const {
    type,
    predicate,
    effect
  } = getListenerEntryPropsFrom(options);
  const entry = {
    id: nanoid(),
    effect,
    type,
    predicate,
    pending: /* @__PURE__ */ new Set(),
    unsubscribe: () => {
      throw new Error( false ? 0 : "Unsubscribe not initialized");
    }
  };
  return entry;
}, {
  withTypes: () => createListenerEntry
});
var findListenerEntry = (listenerMap, options) => {
  const {
    type,
    effect,
    predicate
  } = getListenerEntryPropsFrom(options);
  return Array.from(listenerMap.values()).find((entry) => {
    const matchPredicateOrType = typeof type === "string" ? entry.type === type : entry.predicate === predicate;
    return matchPredicateOrType && entry.effect === effect;
  });
};
var cancelActiveListeners = (entry) => {
  entry.pending.forEach((controller) => {
    abortControllerWithReason(controller, listenerCancelled);
  });
};
var createClearListenerMiddleware = (listenerMap) => {
  return () => {
    listenerMap.forEach(cancelActiveListeners);
    listenerMap.clear();
  };
};
var safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {
  try {
    errorHandler(errorToNotify, errorInfo);
  } catch (errorHandlerError) {
    setTimeout(() => {
      throw errorHandlerError;
    }, 0);
  }
};
var addListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/add`), {
  withTypes: () => addListener
});
var clearAllListeners = /* @__PURE__ */ createAction(`${alm}/removeAll`);
var removeListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/remove`), {
  withTypes: () => removeListener
});
var defaultErrorHandler = (...args) => {
  console.error(`${alm}/error`, ...args);
};
var createListenerMiddleware = (middlewareOptions = {}) => {
  const listenerMap = /* @__PURE__ */ new Map();
  const {
    extra,
    onError = defaultErrorHandler
  } = middlewareOptions;
  assertFunction(onError, "onError");
  const insertEntry = (entry) => {
    entry.unsubscribe = () => listenerMap.delete(entry.id);
    listenerMap.set(entry.id, entry);
    return (cancelOptions) => {
      entry.unsubscribe();
      if (cancelOptions?.cancelActive) {
        cancelActiveListeners(entry);
      }
    };
  };
  const startListening = (options) => {
    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options);
    return insertEntry(entry);
  };
  assign(startListening, {
    withTypes: () => startListening
  });
  const stopListening = (options) => {
    const entry = findListenerEntry(listenerMap, options);
    if (entry) {
      entry.unsubscribe();
      if (options.cancelActive) {
        cancelActiveListeners(entry);
      }
    }
    return !!entry;
  };
  assign(stopListening, {
    withTypes: () => stopListening
  });
  const notifyListener = async (entry, action, api, getOriginalState) => {
    const internalTaskController = new AbortController();
    const take = createTakePattern(startListening, internalTaskController.signal);
    const autoJoinPromises = [];
    try {
      entry.pending.add(internalTaskController);
      await Promise.resolve(entry.effect(
        action,
        // Use assign() rather than ... to avoid extra helper functions added to bundle
        assign({}, api, {
          getOriginalState,
          condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),
          take,
          delay: createDelay(internalTaskController.signal),
          pause: createPause(internalTaskController.signal),
          extra,
          signal: internalTaskController.signal,
          fork: createFork(internalTaskController.signal, autoJoinPromises),
          unsubscribe: entry.unsubscribe,
          subscribe: () => {
            listenerMap.set(entry.id, entry);
          },
          cancelActiveListeners: () => {
            entry.pending.forEach((controller, _, set) => {
              if (controller !== internalTaskController) {
                abortControllerWithReason(controller, listenerCancelled);
                set.delete(controller);
              }
            });
          },
          cancel: () => {
            abortControllerWithReason(internalTaskController, listenerCancelled);
            entry.pending.delete(internalTaskController);
          },
          throwIfCancelled: () => {
            validateActive(internalTaskController.signal);
          }
        })
      ));
    } catch (listenerError) {
      if (!(listenerError instanceof TaskAbortError)) {
        safelyNotifyError(onError, listenerError, {
          raisedBy: "effect"
        });
      }
    } finally {
      await Promise.all(autoJoinPromises);
      abortControllerWithReason(internalTaskController, listenerCompleted);
      entry.pending.delete(internalTaskController);
    }
  };
  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);
  const middleware = (api) => (next) => (action) => {
    if (!(0,redux__WEBPACK_IMPORTED_MODULE_0__.isAction)(action)) {
      return next(action);
    }
    if (addListener.match(action)) {
      return startListening(action.payload);
    }
    if (clearAllListeners.match(action)) {
      clearListenerMiddleware();
      return;
    }
    if (removeListener.match(action)) {
      return stopListening(action.payload);
    }
    let originalState = api.getState();
    const getOriginalState = () => {
      if (originalState === INTERNAL_NIL_TOKEN) {
        throw new Error( false ? 0 : `${alm}: getOriginalState can only be called synchronously`);
      }
      return originalState;
    };
    let result;
    try {
      result = next(action);
      if (listenerMap.size > 0) {
        const currentState = api.getState();
        const listenerEntries = Array.from(listenerMap.values());
        for (const entry of listenerEntries) {
          let runListener = false;
          try {
            runListener = entry.predicate(action, currentState, originalState);
          } catch (predicateError) {
            runListener = false;
            safelyNotifyError(onError, predicateError, {
              raisedBy: "predicate"
            });
          }
          if (!runListener) {
            continue;
          }
          notifyListener(entry, action, api, getOriginalState);
        }
      }
    } finally {
      originalState = INTERNAL_NIL_TOKEN;
    }
    return result;
  };
  return {
    middleware,
    startListening,
    stopListening,
    clearListeners: clearListenerMiddleware
  };
};

// src/dynamicMiddleware/index.ts

var createMiddlewareEntry = (middleware) => ({
  middleware,
  applied: /* @__PURE__ */ new Map()
});
var matchInstance = (instanceId) => (action) => action?.meta?.instanceId === instanceId;
var createDynamicMiddleware = () => {
  const instanceId = nanoid();
  const middlewareMap = /* @__PURE__ */ new Map();
  const withMiddleware = Object.assign(createAction("dynamicMiddleware/add", (...middlewares) => ({
    payload: middlewares,
    meta: {
      instanceId
    }
  })), {
    withTypes: () => withMiddleware
  });
  const addMiddleware = Object.assign(function addMiddleware2(...middlewares) {
    middlewares.forEach((middleware2) => {
      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);
    });
  }, {
    withTypes: () => addMiddleware
  });
  const getFinalMiddleware = (api) => {
    const appliedMiddleware = Array.from(middlewareMap.values()).map((entry) => getOrInsertComputed(entry.applied, api, entry.middleware));
    return (0,redux__WEBPACK_IMPORTED_MODULE_0__.compose)(...appliedMiddleware);
  };
  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));
  const middleware = (api) => (next) => (action) => {
    if (isWithMiddleware(action)) {
      addMiddleware(...action.payload);
      return api.dispatch;
    }
    return getFinalMiddleware(api)(next)(action);
  };
  return {
    middleware,
    addMiddleware,
    withMiddleware,
    instanceId
  };
};

// src/combineSlices.ts

var isSliceLike = (maybeSliceLike) => "reducerPath" in maybeSliceLike && typeof maybeSliceLike.reducerPath === "string";
var getReducers = (slices) => slices.flatMap((sliceOrMap) => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));
var ORIGINAL_STATE = Symbol.for("rtk-state-proxy-original");
var isStateProxy = (value) => !!value && !!value[ORIGINAL_STATE];
var stateProxyMap = /* @__PURE__ */ new WeakMap();
var createStateProxy = (state, reducerMap, initialStateCache) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {
  get: (target, prop, receiver) => {
    if (prop === ORIGINAL_STATE) return target;
    const result = Reflect.get(target, prop, receiver);
    if (typeof result === "undefined") {
      const cached = initialStateCache[prop];
      if (typeof cached !== "undefined") return cached;
      const reducer = reducerMap[prop];
      if (reducer) {
        const reducerResult = reducer(void 0, {
          type: nanoid()
        });
        if (typeof reducerResult === "undefined") {
          throw new Error( false ? 0 : `The slice reducer for key "${prop.toString()}" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);
        }
        initialStateCache[prop] = reducerResult;
        return reducerResult;
      }
    }
    return result;
  }
}));
var original = (state) => {
  if (!isStateProxy(state)) {
    throw new Error( false ? 0 : "original must be used on state Proxy");
  }
  return state[ORIGINAL_STATE];
};
var emptyObject = {};
var noopReducer = (state = emptyObject) => state;
function combineSlices(...slices) {
  const reducerMap = Object.fromEntries(getReducers(slices));
  const getReducer = () => Object.keys(reducerMap).length ? (0,redux__WEBPACK_IMPORTED_MODULE_0__.combineReducers)(reducerMap) : noopReducer;
  let reducer = getReducer();
  function combinedReducer(state, action) {
    return reducer(state, action);
  }
  combinedReducer.withLazyLoadedSlices = () => combinedReducer;
  const initialStateCache = {};
  const inject = (slice, config = {}) => {
    const {
      reducerPath,
      reducer: reducerToInject
    } = slice;
    const currentReducer = reducerMap[reducerPath];
    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {
      if (typeof process !== "undefined" && "development" === "development") {
        console.error(`called \`inject\` to override already-existing reducer ${reducerPath} without specifying \`overrideExisting: true\``);
      }
      return combinedReducer;
    }
    if (config.overrideExisting && currentReducer !== reducerToInject) {
      delete initialStateCache[reducerPath];
    }
    reducerMap[reducerPath] = reducerToInject;
    reducer = getReducer();
    return combinedReducer;
  };
  const selector = Object.assign(function makeSelector(selectorFn, selectState) {
    return function selector2(state, ...args) {
      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap, initialStateCache), ...args);
    };
  }, {
    original
  });
  return Object.assign(combinedReducer, {
    inject,
    selector
  });
}

// src/formatProdErrorMessage.ts
function formatProdErrorMessage(code) {
  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;
}

//# sourceMappingURL=redux-toolkit.modern.mjs.map

/***/ }),

/***/ "./node_modules/defuddle/dist/index.js":
/*!*********************************************!*\
  !*** ./node_modules/defuddle/dist/index.js ***!
  \*********************************************/
/***/ (function(module) {

!function(t,e){ true?module.exports=e():0}("undefined"!=typeof self?self:this,(()=>(()=>{"use strict";var t={0:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.mathRules=e.createCleanMathEl=void 0;const n=r(282);e.createCleanMathEl=(t,e,r,n)=>{const o=t.createElement("math");if(o.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),o.setAttribute("display",n?"block":"inline"),o.setAttribute("data-latex",r||""),null==e?void 0:e.mathml){const r=t.createElement("div");r.innerHTML=e.mathml;const n=r.querySelector("math");n&&(o.innerHTML=n.innerHTML)}else r&&(o.textContent=r);return o},e.mathRules=[{selector:n.mathSelectors,element:"math",transform:(t,r)=>{if(!function(t){return"classList"in t&&"getAttribute"in t&&"querySelector"in t}(t))return t;const o=(0,n.getMathMLFromElement)(t),i=(0,n.getBasicLatexFromElement)(t),a=(0,n.isBlockDisplay)(t),s=(0,e.createCleanMathEl)(r,o,i,a);if(t.parentElement){t.parentElement.querySelectorAll('\n\t\t\t\t\t/* MathJax scripts and previews */\n\t\t\t\t\tscript[type^="math/"],\n\t\t\t\t\t.MathJax_Preview,\n\n\t\t\t\t\t/* External math library scripts */\n\t\t\t\t\tscript[type="text/javascript"][src*="mathjax"],\n\t\t\t\t\tscript[type="text/javascript"][src*="katex"]\n\t\t\t\t').forEach((t=>t.remove()))}return s}}]},20:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GrokExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.messageContainerSelector=".relative.group.flex.flex-col.justify-center.w-full",this.messageBubbles=t.querySelectorAll(this.messageContainerSelector),this.footnotes=[],this.footnoteCounter=0}canExtract(){return!!this.messageBubbles&&this.messageBubbles.length>0}extractMessages(){const t=[];return this.footnotes=[],this.footnoteCounter=0,this.messageBubbles&&0!==this.messageBubbles.length?(this.messageBubbles.forEach((e=>{var r;const n=e.classList.contains("items-end"),o=e.classList.contains("items-start");if(!n&&!o)return;const i=e.querySelector(".message-bubble");if(!i)return;let a="",s="",l="";if(n)a=i.textContent||"",s="user",l="You";else if(o){s="assistant",l="Grok";const t=i.cloneNode(!0);null===(r=t.querySelector(".relative.border.border-border-l1.bg-surface-base"))||void 0===r||r.remove(),a=t.innerHTML,a=this.processFootnotes(a)}a.trim()&&t.push({author:l,content:a.trim(),metadata:{role:s}})})),t):t}getFootnotes(){return this.footnotes}getMetadata(){var t;const e=this.getTitle(),r=(null===(t=this.messageBubbles)||void 0===t?void 0:t.length)||0;return{title:e,site:"Grok",url:this.url,messageCount:r,description:`Grok conversation with ${r} messages`}}getTitle(){var t,e;const r=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(r&&"Grok"!==r&&!r.startsWith("Grok by "))return r.replace(/\s-\s*Grok$/,"").trim();const n=this.document.querySelector(`${this.messageContainerSelector}.items-end`);if(n){const t=n.querySelector(".message-bubble");if(t){const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";return r.length>50?r.slice(0,50)+"...":r}}return"Grok Conversation"}processFootnotes(t){return t.replace(/<a\s+(?:[^>]*?\s+)?href="([^"]*)"[^>]*>(.*?)<\/a>/gi,((t,e,r)=>{if(!e||e.startsWith("#")||!e.match(/^https?:\/\//i))return t;let n;if(this.footnotes.find((t=>t.url===e)))n=this.footnotes.findIndex((t=>t.url===e))+1;else{this.footnoteCounter++,n=this.footnoteCounter;let t=e;try{const r=new URL(e).hostname.replace(/^www\./,"");t=`<a href="${e}" target="_blank" rel="noopener noreferrer">${r}</a>`}catch(r){t=`<a href="${e}" target="_blank" rel="noopener noreferrer">${e}</a>`,console.warn(`GrokExtractor: Could not parse URL for footnote: ${e}`)}this.footnotes.push({url:e,text:t})}return`${r}<sup id="fnref:${n}" class="footnote-ref"><a href="#fn:${n}" class="footnote-link">${n}</a></sup>`}))}}e.GrokExtractor=o},181:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ConversationExtractor=void 0;const n=r(279),o=r(628);class i extends n.BaseExtractor{getFootnotes(){return[]}extract(){var t;const e=this.extractMessages(),r=this.getMetadata(),n=this.getFootnotes(),i=this.createContentHtml(e,n),a=document.implementation.createHTMLDocument(),s=a.createElement("article");s.innerHTML=i,a.body.appendChild(s);const l=new o.Defuddle(a).parse(),c=l.content;return{content:c,contentHtml:c,extractedContent:{messageCount:e.length.toString()},variables:{title:r.title||"Conversation",site:r.site,description:r.description||`${r.site} conversation with ${e.length} messages`,wordCount:(null===(t=l.wordCount)||void 0===t?void 0:t.toString())||""}}}createContentHtml(t,e){return`${t.map(((e,r)=>{const n=e.timestamp?`<div class="message-timestamp">${e.timestamp}</div>`:"",o=/<p[^>]*>[\s\S]*?<\/p>/i.test(e.content)?e.content:`<p>${e.content}</p>`,i=e.metadata?Object.entries(e.metadata).map((([t,e])=>`data-${t}="${e}"`)).join(" "):"";return`\n\t\t\t<div class="message message-${e.author.toLowerCase()}" ${i}>\n\t\t\t\t<div class="message-header">\n\t\t\t\t\t<p class="message-author"><strong>${e.author}</strong></p>\n\t\t\t\t\t${n}\n\t\t\t\t</div>\n\t\t\t\t<div class="message-content">\n\t\t\t\t\t${o}\n\t\t\t\t</div>\n\t\t\t</div>${r<t.length-1?"\n<hr>":""}`})).join("\n").trim()}\n${e.length>0?`\n\t\t\t<div id="footnotes">\n\t\t\t\t<ol>\n\t\t\t\t\t${e.map(((t,e)=>`\n\t\t\t\t\t\t<li class="footnote" id="fn:${e+1}">\n\t\t\t\t\t\t\t<p>\n\t\t\t\t\t\t\t\t<a href="${t.url}" target="_blank">${t.text}</a>&nbsp;<a href="#fnref:${e+1}" class="footnote-backref">\u21a9</a>\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</li>\n\t\t\t\t\t`)).join("")}\n\t\t\t\t</ol>\n\t\t\t</div>`:""}`.trim()}}e.ConversationExtractor=i},248:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TwitterExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){var r;super(t,e),this.mainTweet=null,this.threadTweets=[];const n=t.querySelector('[aria-label="Timeline: Conversation"]');if(!n){const e=t.querySelector('article[data-testid="tweet"]');return void(e&&(this.mainTweet=e))}const o=Array.from(n.querySelectorAll('article[data-testid="tweet"]')),i=null===(r=n.querySelector("section, h2"))||void 0===r?void 0:r.parentElement;i&&o.forEach(((t,e)=>{if(i.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING)return o.splice(e),!1})),this.mainTweet=o[0]||null,this.threadTweets=o.slice(1)}canExtract(){return!!this.mainTweet}extract(){const t=this.extractTweet(this.mainTweet),e=this.threadTweets.map((t=>this.extractTweet(t))).join("\n<hr>\n"),r=`\n\t\t\t<div class="tweet-thread">\n\t\t\t\t<div class="main-tweet">\n\t\t\t\t\t${t}\n\t\t\t\t</div>\n\t\t\t\t${e?`\n\t\t\t\t\t<hr>\n\t\t\t\t\t<div class="thread-tweets">\n\t\t\t\t\t\t${e}\n\t\t\t\t\t</div>\n\t\t\t\t`:""}\n\t\t\t</div>\n\t\t`.trim(),n=this.getTweetId(),o=this.getTweetAuthor();return{content:r,contentHtml:r,extractedContent:{tweetId:n,tweetAuthor:o},variables:{title:`Thread by ${o}`,author:o,site:"X (Twitter)",description:this.createDescription(this.mainTweet)}}}formatTweetText(t){if(!t)return"";const e=document.createElement("div");e.innerHTML=t,e.querySelectorAll("a").forEach((t=>{var e;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";t.replaceWith(r)})),e.querySelectorAll("span, div").forEach((t=>{t.replaceWith(...Array.from(t.childNodes))}));return e.innerHTML.split("\n").map((t=>t.trim())).filter((t=>t)).map((t=>`<p>${t}</p>`)).join("\n")}extractTweet(t){var e,r,n;if(!t)return"";const o=t.cloneNode(!0);o.querySelectorAll('img[src*="/emoji/"]').forEach((t=>{if("img"===t.tagName.toLowerCase()&&t.getAttribute("alt")){const e=t.getAttribute("alt");e&&t.replaceWith(e)}}));const i=(null===(e=o.querySelector('[data-testid="tweetText"]'))||void 0===e?void 0:e.innerHTML)||"",a=this.formatTweetText(i),s=this.extractImages(t),l=this.extractUserInfo(t),c=null===(n=null===(r=t.querySelector('[aria-labelledby*="id__"]'))||void 0===r?void 0:r.querySelector('[data-testid="User-Name"]'))||void 0===n?void 0:n.closest('[aria-labelledby*="id__"]'),u=c?this.extractTweet(c):"";return`\n\t\t\t<div class="tweet">\n\t\t\t\t<div class="tweet-header">\n\t\t\t\t\t<span class="tweet-author"><strong>${l.fullName}</strong> <span class="tweet-handle">${l.handle}</span></span>\n\t\t\t\t\t${l.date?`<a href="${l.permalink}" class="tweet-date">${l.date}</a>`:""}\n\t\t\t\t</div>\n\t\t\t\t${a?`<div class="tweet-text">${a}</div>`:""}\n\t\t\t\t${s.length?`\n\t\t\t\t\t<div class="tweet-media">\n\t\t\t\t\t\t${s.join("\n")}\n\t\t\t\t\t</div>\n\t\t\t\t`:""}\n\t\t\t\t${u?`\n\t\t\t\t\t<blockquote class="quoted-tweet">\n\t\t\t\t\t\t${u}\n\t\t\t\t\t</blockquote>\n\t\t\t\t`:""}\n\t\t\t</div>\n\t\t`.trim()}extractUserInfo(t){var e,r,n,o,i,a,s,l,c;const u=t.querySelector('[data-testid="User-Name"]');if(!u)return{fullName:"",handle:"",date:"",permalink:""};const d=u.querySelectorAll("a");let m=(null===(r=null===(e=null==d?void 0:d[0])||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||"",h=(null===(o=null===(n=null==d?void 0:d[1])||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";m&&h||(m=(null===(a=null===(i=u.querySelector('span[style*="color: rgb(15, 20, 25)"] span'))||void 0===i?void 0:i.textContent)||void 0===a?void 0:a.trim())||"",h=(null===(l=null===(s=u.querySelector('span[style*="color: rgb(83, 100, 113)"]'))||void 0===s?void 0:s.textContent)||void 0===l?void 0:l.trim())||"");const p=t.querySelector("time"),g=(null==p?void 0:p.getAttribute("datetime"))||"";return{fullName:m,handle:h,date:g?new Date(g).toISOString().split("T")[0]:"",permalink:(null===(c=null==p?void 0:p.closest("a"))||void 0===c?void 0:c.href)||""}}extractImages(t){var e,r;const n=['[data-testid="tweetPhoto"]','[data-testid="tweet-image"]','img[src*="media"]'],o=[],i=null===(r=null===(e=t.querySelector('[aria-labelledby*="id__"]'))||void 0===e?void 0:e.querySelector('[data-testid="User-Name"]'))||void 0===r?void 0:r.closest('[aria-labelledby*="id__"]');for(const e of n){t.querySelectorAll(e).forEach((t=>{var e,r;if(!(null==i?void 0:i.contains(t))&&"img"===t.tagName.toLowerCase()&&t.getAttribute("alt")){const n=(null===(e=t.getAttribute("src"))||void 0===e?void 0:e.replace(/&name=\w+$/,"&name=large"))||"",i=(null===(r=t.getAttribute("alt"))||void 0===r?void 0:r.replace(/\s+/g," ").trim())||"";o.push(`<img src="${n}" alt="${i}" />`)}}))}return o}getTweetId(){const t=this.url.match(/status\/(\d+)/);return(null==t?void 0:t[1])||""}getTweetAuthor(){var t,e,r;const n=null===(t=this.mainTweet)||void 0===t?void 0:t.querySelector('[data-testid="User-Name"]'),o=null==n?void 0:n.querySelectorAll("a"),i=(null===(r=null===(e=null==o?void 0:o[1])||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||"";return i.startsWith("@")?i:`@${i}`}createDescription(t){var e;if(!t)return"";return((null===(e=t.querySelector('[data-testid="tweetText"]'))||void 0===e?void 0:e.textContent)||"").trim().slice(0,140).replace(/\s+/g," ")}}e.TwitterExtractor=o},258:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.YoutubeExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e,r){super(t,e,r),this.videoElement=t.querySelector("video"),this.schemaOrgData=r}canExtract(){return!0}extract(){const t=this.getVideoData(),e=t.description||"",r=this.formatDescription(e),n=`<iframe width="560" height="315" src="https://www.youtube.com/embed/${this.getVideoId()}?si=_m0qv33lAuJFoGNh" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe><br>${r}`;return{content:n,contentHtml:n,extractedContent:{videoId:this.getVideoId(),author:t.author||""},variables:{title:t.name||"",author:t.author||"",site:"YouTube",image:Array.isArray(t.thumbnailUrl)&&t.thumbnailUrl[0]||"",published:t.uploadDate,description:e.slice(0,200).trim()}}}formatDescription(t){return`<p>${t.replace(/\n/g,"<br>")}</p>`}getVideoData(){if(!this.schemaOrgData)return{};return(Array.isArray(this.schemaOrgData)?this.schemaOrgData.find((t=>"VideoObject"===t["@type"])):"VideoObject"===this.schemaOrgData["@type"]?this.schemaOrgData:null)||{}}getVideoId(){return new URLSearchParams(new URL(this.url).search).get("v")||""}}e.YoutubeExtractor=o},279:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BaseExtractor=void 0;e.BaseExtractor=class{constructor(t,e,r){this.document=t,this.url=e,this.schemaOrgData=r}}},282:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.mathSelectors=e.isBlockDisplay=e.getBasicLatexFromElement=e.getMathMLFromElement=void 0;e.getMathMLFromElement=t=>{if("math"===t.tagName.toLowerCase()){const e="block"===t.getAttribute("display");return{mathml:t.outerHTML,latex:t.getAttribute("alttext")||null,isBlock:e}}const e=t.getAttribute("data-mathml");if(e){const t=document.createElement("div");t.innerHTML=e;const r=t.querySelector("math");if(r){const t="block"===r.getAttribute("display");return{mathml:r.outerHTML,latex:r.getAttribute("alttext")||null,isBlock:t}}}const r=t.querySelector(".MJX_Assistive_MathML, mjx-assistive-mml");if(r){const t=r.querySelector("math");if(t){const e=t.getAttribute("display"),n=r.getAttribute("display"),o="block"===e||"block"===n;return{mathml:t.outerHTML,latex:t.getAttribute("alttext")||null,isBlock:o}}}const n=t.querySelector(".katex-mathml math");return n?{mathml:n.outerHTML,latex:null,isBlock:!1}:null};e.getBasicLatexFromElement=t=>{var e,r,n;const o=t.getAttribute("data-latex");if(o)return o;if("img"===t.tagName.toLowerCase()&&t.classList.contains("latex")){const e=t.getAttribute("alt");if(e)return e;const r=t.getAttribute("src");if(r){const t=r.match(/latex\.php\?latex=([^&]+)/);if(t)return decodeURIComponent(t[1]).replace(/\+/g," ").replace(/%5C/g,"\\")}}const i=t.querySelector('annotation[encoding="application/x-tex"]');if(null==i?void 0:i.textContent)return i.textContent.trim();if(t.matches(".katex")){const e=t.querySelector('.katex-mathml annotation[encoding="application/x-tex"]');if(null==e?void 0:e.textContent)return e.textContent.trim()}if(t.matches('script[type="math/tex"]')||t.matches('script[type="math/tex; mode=display"]'))return(null===(e=t.textContent)||void 0===e?void 0:e.trim())||null;if(t.parentElement){const e=t.parentElement.querySelector('script[type="math/tex"], script[type="math/tex; mode=display"]');if(e)return(null===(r=e.textContent)||void 0===r?void 0:r.trim())||null}return t.getAttribute("alt")||(null===(n=t.textContent)||void 0===n?void 0:n.trim())||null};e.isBlockDisplay=t=>{if("block"===t.getAttribute("display"))return!0;const e=t.className.toLowerCase();if(e.includes("display")||e.includes("block"))return!0;if(t.closest('.katex-display, .MathJax_Display, [data-display="block"]'))return!0;const r=t.previousElementSibling;if("p"===(null==r?void 0:r.tagName.toLowerCase()))return!0;if(t.matches(".mwe-math-fallback-image-display"))return!0;if(t.matches(".katex"))return null!==t.closest(".katex-display");if(t.hasAttribute("display"))return"true"===t.getAttribute("display");if(t.matches('script[type="math/tex; mode=display"]'))return!0;if(t.hasAttribute("display"))return"true"===t.getAttribute("display");const n=t.closest("[display]");return!!n&&"true"===n.getAttribute("display")},e.mathSelectors=['img.latex[src*="latex.php"]',"span.MathJax","mjx-container",'script[type="math/tex"]','script[type="math/tex; mode=display"]','.MathJax_Preview + script[type="math/tex"]',".MathJax_Display",".MathJax_SVG",".MathJax_MathML",".mwe-math-element",".mwe-math-fallback-image-inline",".mwe-math-fallback-image-display",".mwe-math-mathml-inline",".mwe-math-mathml-display",".katex",".katex-display",".katex-mathml",".katex-html","[data-katex]",'script[type="math/katex"]',"math","[data-math]","[data-latex]","[data-tex]",'script[type^="math/"]','annotation[encoding="application/x-tex"]'].join(",")},397:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ClaudeExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.articles=t.querySelectorAll('div[data-testid="user-message"], div[data-testid="assistant-message"], div.font-claude-message')}canExtract(){return!!this.articles&&this.articles.length>0}extractMessages(){const t=[];return this.articles?(this.articles.forEach((e=>{let r,n;if(e.hasAttribute("data-testid")){if("user-message"!==e.getAttribute("data-testid"))return;r="you",n=e.innerHTML}else{if(!e.classList.contains("font-claude-message"))return;r="assistant",n=e.innerHTML}n&&t.push({author:"you"===r?"You":"Claude",content:n.trim(),metadata:{role:r}})})),t):t}getMetadata(){const t=this.getTitle(),e=this.extractMessages();return{title:t,site:"Claude",url:this.url,messageCount:e.length,description:`Claude conversation with ${e.length} messages`}}getTitle(){var t,e,r,n,o;const i=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(i&&"Claude"!==i)return i.replace(/ - Claude$/,"");const a=null===(r=null===(e=this.document.querySelector("header .font-tiempos"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim();if(a)return a;const s=null===(o=null===(n=this.articles)||void 0===n?void 0:n.item(0))||void 0===o?void 0:o.querySelector('[data-testid="user-message"]');if(s){const t=s.textContent||"";return t.length>50?t.slice(0,50)+"...":t}return"Claude Conversation"}}e.ClaudeExtractor=o},458:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.HackerNewsExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){super(t,e),this.mainPost=t.querySelector(".fatitem"),this.isCommentPage=this.detectCommentPage(),this.mainComment=this.isCommentPage?this.findMainComment():null}detectCommentPage(){var t;return!!(null===(t=this.mainPost)||void 0===t?void 0:t.querySelector('.navs a[href*="parent"]'))}findMainComment(){var t;return(null===(t=this.mainPost)||void 0===t?void 0:t.querySelector(".comment"))||null}canExtract(){return!!this.mainPost}extract(){const t=this.getPostContent(),e=this.extractComments(),r=this.createContentHtml(t,e),n=this.getPostTitle(),o=this.getPostAuthor(),i=this.createDescription(),a=this.getPostDate();return{content:r,contentHtml:r,extractedContent:{postId:this.getPostId(),postAuthor:o},variables:{title:n,author:o,site:"Hacker News",description:i,published:a}}}createContentHtml(t,e){return`\n\t\t\t<div class="hackernews-post">\n\t\t\t\t<div class="post-content">\n\t\t\t\t\t${t}\n\t\t\t\t</div>\n\t\t\t\t${e?`\n\t\t\t\t\t<hr>\n\t\t\t\t\t<h2>Comments</h2>\n\t\t\t\t\t<div class="hackernews-comments">\n\t\t\t\t\t\t${e}\n\t\t\t\t\t</div>\n\t\t\t\t`:""}\n\t\t\t</div>\n\t\t`.trim()}getPostContent(){var t,e,r,n,o,i;if(!this.mainPost)return"";if(this.isCommentPage&&this.mainComment){const i=(null===(t=this.mainComment.querySelector(".hnuser"))||void 0===t?void 0:t.textContent)||"[deleted]",a=(null===(e=this.mainComment.querySelector(".commtext"))||void 0===e?void 0:e.innerHTML)||"",s=this.mainComment.querySelector(".age"),l=((null==s?void 0:s.getAttribute("title"))||"").split("T")[0]||"",c=(null===(n=null===(r=this.mainComment.querySelector(".score"))||void 0===r?void 0:r.textContent)||void 0===n?void 0:n.trim())||"",u=(null===(o=this.mainPost.querySelector('.navs a[href*="parent"]'))||void 0===o?void 0:o.getAttribute("href"))||"";return`\n\t\t\t\t<div class="comment main-comment">\n\t\t\t\t\t<div class="comment-metadata">\n\t\t\t\t\t\t<span class="comment-author"><strong>${i}</strong></span> \u2022\n\t\t\t\t\t\t<span class="comment-date">${l}</span>\n\t\t\t\t\t\t${c?` \u2022 <span class="comment-points">${c}</span>`:""}\n\t\t\t\t\t\t${u?` \u2022 <a href="https://news.ycombinator.com/${u}" class="parent-link">parent</a>`:""}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="comment-content">${a}</div>\n\t\t\t\t</div>\n\t\t\t`.trim()}const a=this.mainPost.querySelector("tr.athing"),s=(null==a||a.nextElementSibling,(null===(i=null==a?void 0:a.querySelector(".titleline a"))||void 0===i?void 0:i.getAttribute("href"))||"");let l="";s&&(l+=`<p><a href="${s}" target="_blank">${s}</a></p>`);const c=this.mainPost.querySelector(".toptext");return c&&(l+=`<div class="post-text">${c.innerHTML}</div>`),l}extractComments(){const t=Array.from(this.document.querySelectorAll("tr.comtr"));return this.processComments(t)}processComments(t){var e,r,n,o;let i="";const a=new Set;let s=-1,l=[];for(const c of t){const t=c.getAttribute("id");if(!t||a.has(t))continue;a.add(t);const u=(null===(e=c.querySelector(".ind img"))||void 0===e?void 0:e.getAttribute("width"))||"0",d=parseInt(u)/40,m=c.querySelector(".commtext"),h=(null===(r=c.querySelector(".hnuser"))||void 0===r?void 0:r.textContent)||"[deleted]",p=c.querySelector(".age"),g=(null===(o=null===(n=c.querySelector(".score"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";if(!m)continue;const f=`https://news.ycombinator.com/item?id=${t}`,v=((null==p?void 0:p.getAttribute("title"))||"").split("T")[0]||"";if(0===d){for(;l.length>0;)i+="</blockquote>",l.pop();i+="<blockquote>",l=[0],s=0}else if(d<s)for(;l.length>0&&l[l.length-1]>=d;)i+="</blockquote>",l.pop();else d>s&&(i+="<blockquote>",l.push(d));i+=`<div class="comment">\n\t<div class="comment-metadata">\n\t\t<span class="comment-author"><strong>${h}</strong></span> \u2022\n\t\t<a href="${f}" class="comment-link">${v}</a>\n\t\t${g?` \u2022 <span class="comment-points">${g}</span>`:""}\n\t</div>\n\t<div class="comment-content">${m.innerHTML}</div>\n</div>`,s=d}for(;l.length>0;)i+="</blockquote>",l.pop();return i}getPostId(){const t=this.url.match(/id=(\d+)/);return(null==t?void 0:t[1])||""}getPostTitle(){var t,e,r,n,o;if(this.isCommentPage&&this.mainComment){const r=(null===(t=this.mainComment.querySelector(".hnuser"))||void 0===t?void 0:t.textContent)||"[deleted]",n=(null===(e=this.mainComment.querySelector(".commtext"))||void 0===e?void 0:e.textContent)||"";return`Comment by ${r}: ${n.trim().slice(0,50)+(n.length>50?"...":"")}`}return(null===(o=null===(n=null===(r=this.mainPost)||void 0===r?void 0:r.querySelector(".titleline"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||""}getPostAuthor(){var t,e,r;return(null===(r=null===(e=null===(t=this.mainPost)||void 0===t?void 0:t.querySelector(".hnuser"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||""}createDescription(){const t=this.getPostTitle(),e=this.getPostAuthor();return this.isCommentPage?`Comment by ${e} on Hacker News`:`${t} - by ${e} on Hacker News`}getPostDate(){if(!this.mainPost)return"";const t=this.mainPost.querySelector(".age");return((null==t?void 0:t.getAttribute("title"))||"").split("T")[0]||""}}e.HackerNewsExtractor=o},552:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isElement=function(t){return t.nodeType===r.ELEMENT_NODE},e.isTextNode=function(t){return t.nodeType===r.TEXT_NODE},e.isCommentNode=function(t){return t.nodeType===r.COMMENT_NODE},e.getComputedStyle=function(t){const e=n(t.ownerDocument);return e?e.getComputedStyle(t):null},e.getWindow=n,e.logDebug=function(t,...e){"undefined"!=typeof window&&window.defuddleDebug&&console.log("Defuddle:",t,...e)};const r={ELEMENT_NODE:1,ATTRIBUTE_NODE:2,TEXT_NODE:3,CDATA_SECTION_NODE:4,ENTITY_REFERENCE_NODE:5,ENTITY_NODE:6,PROCESSING_INSTRUCTION_NODE:7,COMMENT_NODE:8,DOCUMENT_NODE:9,DOCUMENT_TYPE_NODE:10,DOCUMENT_FRAGMENT_NODE:11,NOTATION_NODE:12};function n(t){return t.defaultView?t.defaultView:t.ownerWindow?t.ownerWindow:t.window?t.window:null}},608:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MetadataExtractor=void 0;e.MetadataExtractor=class{static extract(t,e,r){var n,o;let i="",a="";try{if(a=(null===(n=t.location)||void 0===n?void 0:n.href)||"",a||(a=this.getMetaContent(r,"property","og:url")||this.getMetaContent(r,"property","twitter:url")||this.getSchemaProperty(e,"url")||this.getSchemaProperty(e,"mainEntityOfPage.url")||this.getSchemaProperty(e,"mainEntity.url")||this.getSchemaProperty(e,"WebSite.url")||(null===(o=t.querySelector('link[rel="canonical"]'))||void 0===o?void 0:o.getAttribute("href"))||""),a)try{i=new URL(a).hostname.replace(/^www\./,"")}catch(t){console.warn("Failed to parse URL:",t)}}catch(e){const r=t.querySelector("base[href]");if(r)try{a=r.getAttribute("href")||"",i=new URL(a).hostname.replace(/^www\./,"")}catch(t){console.warn("Failed to parse base URL:",t)}}return{title:this.getTitle(t,e,r),description:this.getDescription(t,e,r),domain:i,favicon:this.getFavicon(t,a,r),image:this.getImage(t,e,r),published:this.getPublished(t,e,r),author:this.getAuthor(t,e,r),site:this.getSite(t,e,r),schemaOrgData:e,wordCount:0,parseTime:0}}static getAuthor(t,e,r){let n;if(n=this.getMetaContent(r,"name","sailthru.author")||this.getMetaContent(r,"property","author")||this.getMetaContent(r,"name","author")||this.getMetaContent(r,"name","byl")||this.getMetaContent(r,"name","authorList"),n)return n;let o=this.getSchemaProperty(e,"author.name")||this.getSchemaProperty(e,"author.[].name");if(o){const t=o.split(",").map((t=>t.trim().replace(/,$/,"").trim())).filter(Boolean);if(t.length>0){let e=[...new Set(t)];return e.length>10&&(e=e.slice(0,10)),e.join(", ")}}const i=[];if(['[itemprop="author"]',".author",'[href*="author"]',".authors a"].forEach((e=>{t.querySelectorAll(e).forEach((t=>{var e;(e=t.textContent)&&e.split(",").forEach((t=>{const e=t.trim().replace(/,$/,"").trim(),r=e.toLowerCase();e&&"author"!==r&&"authors"!==r&&i.push(e)}))}))})),i.length>0){let t=[...new Set(i.map((t=>t.trim())).filter(Boolean))];if(t.length>0)return t.length>10&&(t=t.slice(0,10)),t.join(", ")}return n=this.getMetaContent(r,"name","copyright")||this.getSchemaProperty(e,"copyrightHolder.name")||this.getMetaContent(r,"property","og:site_name")||this.getSchemaProperty(e,"publisher.name")||this.getSchemaProperty(e,"sourceOrganization.name")||this.getSchemaProperty(e,"isPartOf.name")||this.getMetaContent(r,"name","twitter:creator")||this.getMetaContent(r,"name","application-name"),n||""}static getSite(t,e,r){return this.getSchemaProperty(e,"publisher.name")||this.getMetaContent(r,"property","og:site_name")||this.getSchemaProperty(e,"WebSite.name")||this.getSchemaProperty(e,"sourceOrganization.name")||this.getMetaContent(r,"name","copyright")||this.getSchemaProperty(e,"copyrightHolder.name")||this.getSchemaProperty(e,"isPartOf.name")||this.getMetaContent(r,"name","application-name")||this.getAuthor(t,e,r)||""}static getTitle(t,e,r){var n,o;const i=this.getMetaContent(r,"property","og:title")||this.getMetaContent(r,"name","twitter:title")||this.getSchemaProperty(e,"headline")||this.getMetaContent(r,"name","title")||this.getMetaContent(r,"name","sailthru.title")||(null===(o=null===(n=t.querySelector("title"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";return this.cleanTitle(i,this.getSite(t,e,r))}static cleanTitle(t,e){if(!t||!e)return t;const r=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),n=[`\\s*[\\|\\-\u2013\u2014]\\s*${r}\\s*$`,`^\\s*${r}\\s*[\\|\\-\u2013\u2014]\\s*`];for(const e of n){const r=new RegExp(e,"i");if(r.test(t)){t=t.replace(r,"");break}}return t.trim()}static getDescription(t,e,r){return this.getMetaContent(r,"name","description")||this.getMetaContent(r,"property","description")||this.getMetaContent(r,"property","og:description")||this.getSchemaProperty(e,"description")||this.getMetaContent(r,"name","twitter:description")||this.getMetaContent(r,"name","sailthru.description")||""}static getImage(t,e,r){return this.getMetaContent(r,"property","og:image")||this.getMetaContent(r,"name","twitter:image")||this.getSchemaProperty(e,"image.url")||this.getMetaContent(r,"name","sailthru.image.full")||""}static getFavicon(t,e,r){var n,o;const i=this.getMetaContent(r,"property","og:image:favicon");if(i)return i;const a=null===(n=t.querySelector("link[rel='icon']"))||void 0===n?void 0:n.getAttribute("href");if(a)return a;const s=null===(o=t.querySelector("link[rel='shortcut icon']"))||void 0===o?void 0:o.getAttribute("href");if(s)return s;if(e)try{return new URL("/favicon.ico",e).href}catch(t){console.warn("Failed to construct favicon URL:",t)}return""}static getPublished(t,e,r){var n,o;return this.getSchemaProperty(e,"datePublished")||this.getMetaContent(r,"name","publishDate")||this.getMetaContent(r,"property","article:published_time")||(null===(o=null===(n=t.querySelector('abbr[itemprop="datePublished"]'))||void 0===n?void 0:n.title)||void 0===o?void 0:o.trim())||this.getTimeElement(t)||this.getMetaContent(r,"name","sailthru.date")||""}static getMetaContent(t,e,r){var n,o;const i=t.find((t=>{const n="name"===e?t.name:t.property;return(null==n?void 0:n.toLowerCase())===r.toLowerCase()}));return i&&null!==(o=null===(n=i.content)||void 0===n?void 0:n.trim())&&void 0!==o?o:""}static getTimeElement(t){var e,r,n,o;const i=Array.from(t.querySelectorAll("time"))[0];return i&&null!==(o=null!==(r=null===(e=i.getAttribute("datetime"))||void 0===e?void 0:e.trim())&&void 0!==r?r:null===(n=i.textContent)||void 0===n?void 0:n.trim())&&void 0!==o?o:""}static getSchemaProperty(t,e,r=""){if(!t)return r;const n=(t,e,r,o=!0)=>{if("string"==typeof t)return 0===e.length?[t]:[];if(!t||"object"!=typeof t)return[];if(Array.isArray(t)){const i=e[0];if(/^\\[\\d+\\]$/.test(i)){const a=parseInt(i.slice(1,-1));return t[a]?n(t[a],e.slice(1),r,o):[]}return 0===e.length&&t.every((t=>"string"==typeof t||"number"==typeof t))?t.map(String):t.flatMap((t=>n(t,e,r,o)))}const[i,...a]=e;if(!i)return"string"==typeof t?[t]:"object"==typeof t&&t.name?[t.name]:[];if(t.hasOwnProperty(i))return n(t[i],a,r?`${r}.${i}`:i,!0);if(!o){const o=[];for(const i in t)if("object"==typeof t[i]){const a=n(t[i],e,r?`${r}.${i}`:i,!1);o.push(...a)}if(o.length>0)return o}return[]};try{let o=n(t,e.split("."),"",!0);0===o.length&&(o=n(t,e.split("."),"",!1));return o.length>0?o.filter(Boolean).join(", "):r}catch(t){return console.error(`Error in getSchemaProperty for ${e}:`,t),r}}}},610:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.standardizeFootnotes=function(t){const e=t.ownerDocument;if(!e)return void console.warn("standardizeFootnotes: No document available");new o(e).standardizeFootnotes(t)};const n=r(640);class o{constructor(t){this.doc=t}createFootnoteItem(t,e,r){const n="string"==typeof e?this.doc:e.ownerDocument,o=n.createElement("li");if(o.className="footnote",o.id=`fn:${t}`,"string"==typeof e){const t=n.createElement("p");t.innerHTML=e,o.appendChild(t)}else{const t=Array.from(e.querySelectorAll("p"));if(0===t.length){const t=n.createElement("p");t.innerHTML=e.innerHTML,o.appendChild(t)}else t.forEach((t=>{const e=n.createElement("p");e.innerHTML=t.innerHTML,o.appendChild(e)}))}const i=o.querySelector("p:last-of-type")||o;return r.forEach(((t,e)=>{const o=n.createElement("a");o.href=`#${t}`,o.title="return to article",o.className="footnote-backref",o.innerHTML="\u21a9",e<r.length-1&&(o.innerHTML+=" "),i.appendChild(o)})),o}collectFootnotes(t){const e={};let r=1;const o=new Set;return t.querySelectorAll(n.FOOTNOTE_LIST_SELECTORS).forEach((t=>{if(t.matches('div.footnote[data-component-name="FootnoteToDOM"]')){const n=t.querySelector("a.footnote-number"),i=t.querySelector(".footnote-content");if(n&&i){const t=n.id.replace("footnote-","").toLowerCase();t&&!o.has(t)&&(e[r]={content:i,originalId:t,refs:[]},o.add(t),r++)}return}t.querySelectorAll('li, div[role="listitem"]').forEach((t=>{var n,i,a,s;let l="",c=null;const u=t.querySelector(".citations");if(null===(n=null==u?void 0:u.id)||void 0===n?void 0:n.toLowerCase().startsWith("r")){l=u.id.toLowerCase();const t=u.querySelector(".citation-content");t&&(c=t)}else{if(t.id.toLowerCase().startsWith("bib.bib"))l=t.id.replace("bib.bib","").toLowerCase();else if(t.id.toLowerCase().startsWith("fn:"))l=t.id.replace("fn:","").toLowerCase();else if(t.id.toLowerCase().startsWith("fn"))l=t.id.replace("fn","").toLowerCase();else if(t.hasAttribute("data-counter"))l=(null===(a=null===(i=t.getAttribute("data-counter"))||void 0===i?void 0:i.replace(/\.$/,""))||void 0===a?void 0:a.toLowerCase())||"";else{const e=null===(s=t.id.split("/").pop())||void 0===s?void 0:s.match(/cite_note-(.+)/);l=e?e[1].toLowerCase():t.id.toLowerCase()}c=t}l&&!o.has(l)&&(e[r]={content:c||t,originalId:l,refs:[]},o.add(l),r++)}))})),e}findOuterFootnoteContainer(t){let e=t,r=t.parentElement;for(;r&&("span"===r.tagName.toLowerCase()||"sup"===r.tagName.toLowerCase());)e=r,r=r.parentElement;return e}createFootnoteReference(t,e){const r=this.doc.createElement("sup");r.id=e;const n=this.doc.createElement("a");return n.href=`#fn:${t}`,n.textContent=t,r.appendChild(n),r}standardizeFootnotes(t){const e=this.collectFootnotes(t),r=t.querySelectorAll(n.FOOTNOTE_INLINE_REFERENCES),o=new Map;r.forEach((t=>{var r,n,i,a;if(!t)return;let s="",l="";if(t.matches('a[id^="ref-link"]'))s=(null===(r=t.textContent)||void 0===r?void 0:r.trim())||"";else if(t.matches('a[role="doc-biblioref"]')){const e=t.getAttribute("data-xml-rid");if(e)s=e;else{const e=t.getAttribute("href");(null==e?void 0:e.startsWith("#core-R"))&&(s=e.replace("#core-",""))}}else if(t.matches("a.footnote-anchor, span.footnote-hovercard-target a")){const e=(null===(n=t.id)||void 0===n?void 0:n.replace("footnote-anchor-",""))||"";e&&(s=e.toLowerCase())}else if(t.matches("cite.ltx_cite")){const e=t.querySelector("a");if(e){const t=e.getAttribute("href");if(t){const e=null===(i=t.split("/").pop())||void 0===i?void 0:i.match(/bib\.bib(\d+)/);e&&(s=e[1].toLowerCase())}}}else if(t.matches("sup.reference")){const e=t.querySelectorAll("a");Array.from(e).forEach((t=>{var e;const r=t.getAttribute("href");if(r){const t=null===(e=r.split("/").pop())||void 0===e?void 0:e.match(/(?:cite_note|cite_ref)-(.+)/);t&&(s=t[1].toLowerCase())}}))}else if(t.matches('sup[id^="fnref:"]'))s=t.id.replace("fnref:","").toLowerCase();else if(t.matches('sup[id^="fnr"]'))s=t.id.replace("fnr","").toLowerCase();else if(t.matches("span.footnote-reference"))s=t.getAttribute("data-footnote-id")||"";else if(t.matches("span.footnote-link"))s=t.getAttribute("data-footnote-id")||"",l=t.getAttribute("data-footnote-content")||"";else if(t.matches("a.citation"))s=(null===(a=t.textContent)||void 0===a?void 0:a.trim())||"",l=t.getAttribute("href")||"";else if(t.matches('a[id^="fnref"]'))s=t.id.replace("fnref","").toLowerCase();else{const e=t.getAttribute("href");if(e){const t=e.replace(/^[#]/,"");s=t.toLowerCase()}}if(s){const r=Object.entries(e).find((([t,e])=>e.originalId===s.toLowerCase()));if(r){const[e,n]=r,i=n.refs.length>0?`fnref:${e}-${n.refs.length+1}`:`fnref:${e}`;n.refs.push(i);const a=this.findOuterFootnoteContainer(t);if("sup"===a.tagName.toLowerCase()){o.has(a)||o.set(a,[]);o.get(a).push(this.createFootnoteReference(e,i))}else a.replaceWith(this.createFootnoteReference(e,i))}}})),o.forEach(((t,e)=>{if(t.length>0){const r=this.doc.createDocumentFragment();t.forEach((t=>{const e=t.querySelector("a");if(e){const n=this.doc.createElement("sup");n.id=t.id,n.appendChild(e.cloneNode(!0)),r.appendChild(n)}})),e.replaceWith(r)}}));const i=this.doc.createElement("div");i.id="footnotes";const a=this.doc.createElement("ol");Object.entries(e).forEach((([t,e])=>{const r=this.createFootnoteItem(parseInt(t),e.content,e.refs);a.appendChild(r)}));t.querySelectorAll(n.FOOTNOTE_LIST_SELECTORS).forEach((t=>t.remove())),a.children.length>0&&(i.appendChild(a),t.appendChild(i))}}},628:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Defuddle=void 0;const n=r(608),o=r(917),i=r(640),a=r(840),s=r(968),l=r(552);e.Defuddle=class{constructor(t,e={}){this.doc=t,this.options=e,this.debug=e.debug||!1}parse(){const t=this.parseInternal();if(t.wordCount<200){console.log("Initial parse returned very little content, trying again");const e=this.parseInternal({removePartialSelectors:!1});if(e.wordCount>t.wordCount)return this._log("Retry produced more content"),e}return t}parseInternal(t={}){var e,r,i;const l=Date.now(),c=Object.assign(Object.assign({removeExactSelectors:!0,removePartialSelectors:!0},this.options),t),u=this._extractSchemaOrgData(this.doc),d=[];this.doc.querySelectorAll("meta").forEach((t=>{const e=t.getAttribute("name"),r=t.getAttribute("property");let n=t.getAttribute("content");n&&d.push({name:e,property:r,content:this._decodeHTMLEntities(n)})}));const m=n.MetadataExtractor.extract(this.doc,u,d);try{const t=c.url||this.doc.URL,n=o.ExtractorRegistry.findExtractor(this.doc,t,u);if(n&&n.canExtract()){const t=n.extract(),o=Date.now();return{content:t.contentHtml,title:(null===(e=t.variables)||void 0===e?void 0:e.title)||m.title,description:m.description,domain:m.domain,favicon:m.favicon,image:m.image,published:(null===(r=t.variables)||void 0===r?void 0:r.published)||m.published,author:(null===(i=t.variables)||void 0===i?void 0:i.author)||m.author,site:m.site,schemaOrgData:m.schemaOrgData,wordCount:this.countWords(t.contentHtml),parseTime:Math.round(o-l),extractorType:n.constructor.name.replace("Extractor","").toLowerCase(),metaTags:d}}const h=this._evaluateMediaQueries(this.doc),p=this.findSmallImages(this.doc),g=this.doc.cloneNode(!0);this.applyMobileStyles(g,h);const f=this.findMainContent(g);if(!f){const t=Date.now();return Object.assign(Object.assign({content:this.doc.body.innerHTML},m),{wordCount:this.countWords(this.doc.body.innerHTML),parseTime:Math.round(t-l),metaTags:d})}this.removeSmallImages(g,p),this.removeHiddenElements(g),s.ContentScorer.scoreAndRemove(g,this.debug),(c.removeExactSelectors||c.removePartialSelectors)&&this.removeBySelector(g,c.removeExactSelectors,c.removePartialSelectors),(0,a.standardizeContent)(f,m,this.doc,this.debug);const v=f.outerHTML,b=Date.now();return Object.assign(Object.assign({content:v},m),{wordCount:this.countWords(v),parseTime:Math.round(b-l),metaTags:d})}catch(t){console.error("Defuddle","Error processing document:",t);const e=Date.now();return Object.assign(Object.assign({content:this.doc.body.innerHTML},m),{wordCount:this.countWords(this.doc.body.innerHTML),parseTime:Math.round(e-l),metaTags:d})}}countWords(t){const e=this.doc.createElement("div");e.innerHTML=t;return(e.textContent||"").trim().replace(/\s+/g," ").split(" ").filter((t=>t.length>0)).length}_log(...t){this.debug&&console.log("Defuddle:",...t)}_evaluateMediaQueries(t){const e=[],r=/max-width[^:]*:\s*(\d+)/;try{const n=Array.from(t.styleSheets).filter((t=>{try{return t.cssRules,!0}catch(t){return t instanceof DOMException&&t.name,!1}}));n.flatMap((t=>{try{return"undefined"==typeof CSSMediaRule?[]:Array.from(t.cssRules).filter((t=>t instanceof CSSMediaRule&&t.conditionText.includes("max-width")))}catch(t){return this.debug&&console.warn("Defuddle: Failed to process stylesheet:",t),[]}})).forEach((t=>{const n=t.conditionText.match(r);if(n){const r=parseInt(n[1]);if(i.MOBILE_WIDTH<=r){Array.from(t.cssRules).filter((t=>t instanceof CSSStyleRule)).forEach((t=>{try{e.push({selector:t.selectorText,styles:t.style.cssText})}catch(t){this.debug&&console.warn("Defuddle: Failed to process CSS rule:",t)}}))}}}))}catch(t){console.error("Defuddle: Error evaluating media queries:",t)}return e}applyMobileStyles(t,e){e.forEach((({selector:e,styles:r})=>{try{t.querySelectorAll(e).forEach((t=>{t.setAttribute("style",(t.getAttribute("style")||"")+r)}))}catch(t){console.error("Defuddle","Error applying styles for selector:",e,t)}}))}removeHiddenElements(t){let e=0;const r=new Set,n=Array.from(t.getElementsByTagName("*"));for(let o=0;o<n.length;o+=100){const i=n.slice(o,o+100),a=i.map((e=>{var r,n;try{return null===(r=e.ownerDocument.defaultView)||void 0===r?void 0:r.getComputedStyle(e)}catch(r){const o=e.getAttribute("style");if(!o)return null;const i=t.createElement("style");i.textContent=`* { ${o} }`,t.head.appendChild(i);const a=null===(n=e.ownerDocument.defaultView)||void 0===n?void 0:n.getComputedStyle(e);return t.head.removeChild(i),a}}));i.forEach(((t,n)=>{const o=a[n];!o||"none"!==o.display&&"hidden"!==o.visibility&&"0"!==o.opacity||(r.add(t),e++)}))}this._log("Removed hidden elements:",e)}removeBySelector(t,e=!0,r=!0){const n=Date.now();let o=0,a=0;const s=new Set;if(e){t.querySelectorAll(i.EXACT_SELECTORS.join(",")).forEach((t=>{(null==t?void 0:t.parentNode)&&(s.add(t),o++)}))}if(r){const e=i.PARTIAL_SELECTORS.join("|"),r=new RegExp(e,"i"),n=i.TEST_ATTRIBUTES.map((t=>`[${t}]`)).join(",");t.querySelectorAll(n).forEach((t=>{if(s.has(t))return;const e=i.TEST_ATTRIBUTES.map((e=>"class"===e?t.className&&"string"==typeof t.className?t.className:"":"id"===e?t.id||"":t.getAttribute(e)||"")).join(" ").toLowerCase();e.trim()&&r.test(e)&&(s.add(t),a++)}))}s.forEach((t=>t.remove()));const l=Date.now();this._log("Removed clutter elements:",{exactSelectors:o,partialSelectors:a,total:s.size,processingTime:`${(l-n).toFixed(2)}ms`})}findSmallImages(t){const e=new Set,r=/scale\(([\d.]+)\)/,n=Date.now();let o=0;const i=[...Array.from(t.getElementsByTagName("img")),...Array.from(t.getElementsByTagName("svg"))];if(0===i.length)return e;const a=i.map((t=>({element:t,naturalWidth:"img"===t.tagName.toLowerCase()&&parseInt(t.getAttribute("width")||"0")||0,naturalHeight:"img"===t.tagName.toLowerCase()&&parseInt(t.getAttribute("height")||"0")||0,attrWidth:parseInt(t.getAttribute("width")||"0"),attrHeight:parseInt(t.getAttribute("height")||"0")})));for(let t=0;t<a.length;t+=50){const n=a.slice(t,t+50);try{const t=n.map((({element:t})=>{var e;try{return null===(e=t.ownerDocument.defaultView)||void 0===e?void 0:e.getComputedStyle(t)}catch(t){return null}})),i=n.map((({element:t})=>{try{return t.getBoundingClientRect()}catch(t){return null}}));n.forEach(((n,a)=>{var s;try{const l=t[a],c=i[a];if(!l)return;const u=l.transform,d=u?parseFloat((null===(s=u.match(r))||void 0===s?void 0:s[1])||"1"):1,m=[n.naturalWidth,n.attrWidth,parseInt(l.width)||0,c?c.width*d:0].filter((t=>"number"==typeof t&&t>0)),h=[n.naturalHeight,n.attrHeight,parseInt(l.height)||0,c?c.height*d:0].filter((t=>"number"==typeof t&&t>0));if(m.length>0&&h.length>0){const t=Math.min(...m),r=Math.min(...h);if(t<33||r<33){const t=this.getElementIdentifier(n.element);t&&(e.add(t),o++)}}}catch(t){this.debug&&console.warn("Defuddle: Failed to process element dimensions:",t)}}))}catch(t){this.debug&&console.warn("Defuddle: Failed to process batch:",t)}}const s=Date.now();return this._log("Found small elements:",{count:o,processingTime:`${(s-n).toFixed(2)}ms`}),e}removeSmallImages(t,e){let r=0;["img","svg"].forEach((n=>{const o=t.getElementsByTagName(n);Array.from(o).forEach((t=>{const n=this.getElementIdentifier(t);n&&e.has(n)&&(t.remove(),r++)}))})),this._log("Removed small elements:",r)}getElementIdentifier(t){if("img"===t.tagName.toLowerCase()){const e=t.getAttribute("data-src");if(e)return`src:${e}`;const r=t.getAttribute("src")||"",n=t.getAttribute("srcset")||"",o=t.getAttribute("data-srcset");if(r)return`src:${r}`;if(n)return`srcset:${n}`;if(o)return`srcset:${o}`}const e=t.id||"",r=t.className||"",n="svg"===t.tagName.toLowerCase()&&t.getAttribute("viewBox")||"";return e?`id:${e}`:n?`viewBox:${n}`:r?`class:${r}`:null}findMainContent(t){const e=[];if(i.ENTRY_POINT_ELEMENTS.forEach(((r,n)=>{t.querySelectorAll(r).forEach((t=>{let r=40*(i.ENTRY_POINT_ELEMENTS.length-n);r+=s.ContentScorer.scoreElement(t),e.push({element:t,score:r})}))})),0===e.length)return this.findContentByScoring(t);if(e.sort(((t,e)=>e.score-t.score)),this.debug&&this._log("Content candidates:",e.map((t=>({element:t.element.tagName,selector:this.getElementSelector(t.element),score:t.score})))),1===e.length&&"body"===e[0].element.tagName.toLowerCase()){const e=this.findTableBasedContent(t);if(e)return e}return e[0].element}findTableBasedContent(t){if(!Array.from(t.getElementsByTagName("table")).some((t=>{const e=parseInt(t.getAttribute("width")||"0"),r=this.getComputedStyle(t);return e>400||(null==r?void 0:r.width.includes("px"))&&parseInt(r.width)>400||"center"===t.getAttribute("align")||t.className.toLowerCase().includes("content")||t.className.toLowerCase().includes("article")})))return null;const e=Array.from(t.getElementsByTagName("td"));return s.ContentScorer.findBestElement(e)}findContentByScoring(t){const e=[];return i.BLOCK_ELEMENTS.forEach((r=>{Array.from(t.getElementsByTagName(r)).forEach((t=>{const r=s.ContentScorer.scoreElement(t);r>0&&e.push({score:r,element:t})}))})),e.length>0?e.sort(((t,e)=>e.score-t.score))[0].element:null}getElementSelector(t){const e=[];let r=t;for(;r&&r!==this.doc.documentElement;){let t=r.tagName.toLowerCase();r.id?t+="#"+r.id:r.className&&"string"==typeof r.className&&(t+="."+r.className.trim().split(/\s+/).join(".")),e.unshift(t),r=r.parentElement}return e.join(" > ")}getComputedStyle(t){return(0,l.getComputedStyle)(t)}_extractSchemaOrgData(t){const e=t.querySelectorAll('script[type="application/ld+json"]'),r=[];e.forEach((t=>{let e=t.textContent||"";try{e=e.replace(/\/\*[\s\S]*?\*\/|^\s*\/\/.*$/gm,"").replace(/^\s*<!\[CDATA\[([\s\S]*?)\]\]>\s*$/,"$1").replace(/^\s*(\*\/|\/\*)\s*|\s*(\*\/|\/\*)\s*$/g,"").trim();const t=JSON.parse(e);t["@graph"]&&Array.isArray(t["@graph"])?r.push(...t["@graph"]):r.push(t)}catch(t){console.error("Defuddle: Error parsing schema.org data:",t),this.debug&&console.error("Defuddle: Problematic JSON content:",e)}}));const n=t=>{if("string"==typeof t)return this._decodeHTMLEntities(t);if(Array.isArray(t))return t.map(n);if("object"==typeof t&&null!==t){const e={};for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=n(t[r]));return e}return t};return r.map(n)}_decodeHTMLEntities(t){const e=this.doc.createElement("textarea");return e.innerHTML=t,e.value}}},632:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ChatGPTExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.articles=t.querySelectorAll('article[data-testid^="conversation-turn-"]'),this.footnotes=[],this.footnoteCounter=0}canExtract(){return!!this.articles&&this.articles.length>0}extractMessages(){const t=[];return this.footnotes=[],this.footnoteCounter=0,this.articles?(this.articles.forEach((e=>{var r,n;const o=e.querySelector("h5.sr-only, h6.sr-only"),i=(null===(n=null===(r=null==o?void 0:o.textContent)||void 0===r?void 0:r.trim())||void 0===n?void 0:n.replace(/:\s*$/,""))||"";let a="";const s=e.getAttribute("data-message-author-role");s&&(a=s);let l=e.innerHTML||"";l=l.replace(/\u200B/g,"");const c=document.createElement("div");c.innerHTML=l,c.querySelectorAll('h5.sr-only, h6.sr-only, span[data-state="closed"]').forEach((t=>t.remove())),l=c.innerHTML;l=l.replace(/(&ZeroWidthSpace;)?(<span[^>]*?>\s*<a(?=[^>]*?href="([^"]+)")(?=[^>]*?target="_blank")(?=[^>]*?rel="noopener")[^>]*?>[\s\S]*?<\/a>\s*<\/span>)/gi,((t,e,r,n)=>{let o="",i="";try{o=new URL(n).hostname.replace(/^www\./,"");const t=n.split("#:~:text=");if(t.length>1){i=decodeURIComponent(t[1]),i=i.replace(/%2C/g,",");const e=i.split(",");i=e.length>1&&e[0].trim()?` \u2014 ${e[0].trim()}...`:e[0].trim()?` \u2014 ${i.trim()}`:""}}catch(t){console.error(`Failed to parse URL: ${n}`,t),o=n}let a,s=this.footnotes.findIndex((t=>t.url===n));return-1===s?(this.footnoteCounter++,a=this.footnoteCounter,this.footnotes.push({url:n,text:`<a href="${n}">${o}</a>${i}`})):a=s+1,`<sup id="fnref:${a}"><a href="#fn:${a}">${a}</a></sup>`})),l=l.replace(/<p[^>]*>\s*<\/p>/g,""),t.push({author:i,content:l.trim(),metadata:{role:a||"unknown"}})})),t):t}getFootnotes(){return this.footnotes}getMetadata(){const t=this.getTitle(),e=this.extractMessages();return{title:t,site:"ChatGPT",url:this.url,messageCount:e.length,description:`ChatGPT conversation with ${e.length} messages`}}getTitle(){var t,e,r;const n=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(n&&"ChatGPT"!==n)return n;const o=null===(r=null===(e=this.articles)||void 0===e?void 0:e.item(0))||void 0===r?void 0:r.querySelector(".text-message");if(o){const t=o.textContent||"";return t.length>50?t.slice(0,50)+"...":t}return"ChatGPT Conversation"}}e.ChatGPTExtractor=o},640:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ALLOWED_ATTRIBUTES_DEBUG=e.ALLOWED_ATTRIBUTES=e.ALLOWED_EMPTY_ELEMENTS=e.FOOTNOTE_LIST_SELECTORS=e.FOOTNOTE_INLINE_REFERENCES=e.PARTIAL_SELECTORS=e.TEST_ATTRIBUTES=e.EXACT_SELECTORS=e.INLINE_ELEMENTS=e.PRESERVE_ELEMENTS=e.BLOCK_ELEMENTS=e.MOBILE_WIDTH=e.ENTRY_POINT_ELEMENTS=void 0,e.ENTRY_POINT_ELEMENTS=["#post",".post-content",".article-content","#article-content",".article_post",".article-wrapper",".entry-content",".content-article",".post",".markdown-body","article",'[role="article"]',"main",'[role="main"]',"body"],e.MOBILE_WIDTH=600,e.BLOCK_ELEMENTS=["div","section","article","main","aside","header","footer","nav","content"],e.PRESERVE_ELEMENTS=new Set(["pre","code","table","thead","tbody","tr","td","th","ul","ol","li","dl","dt","dd","figure","figcaption","picture","details","summary","blockquote","form","fieldset"]),e.INLINE_ELEMENTS=new Set(["a","span","strong","em","i","b","u","code","br","small","sub","sup","mark","date","del","ins","q","abbr","cite","relative-time","time","font"]),e.EXACT_SELECTORS=["noscript",'script:not([type^="math/"])',"style","meta","link",'.ad:not([class*="gradient"])','[class^="ad-" i]','[class$="-ad" i]','[id^="ad-" i]','[id$="-ad" i]','[role="banner" i]','[alt*="advert" i]',".promo",".Promo","#barrier-page",".alert",'[id="comments" i]','[id="comment" i]',"header",".header:not(.banner)","#header","#Header","#banner","#Banner","nav",".navigation","#navigation",".hero",'[role="navigation" i]','[role="dialog" i]','[role*="complementary" i]','[class*="pagination" i]',".menu","#menu","#siteSub",".previous",".author",".Author",'[class$="_bio"]',"#categories",".contributor",".date","#date","[data-date]",".entry-meta",".meta",".tags","#tags",".toc",".Toc","#toc",".headline","#headline","#title","#Title","#articleTag",'[href*="/category"]','[href*="/categories"]','[href*="/tag/"]','[href*="/tags/"]','[href*="/topics"]','[href*="author"]','[href*="#toc"]','[href="#top"]','[href="#Top"]','[href="#page-header"]','[href="#content"]','[href="#site-content"]','[href="#main-content"]','[href^="#main"]','[src*="author"]',"footer",".aside","aside","button","canvas","date","dialog","fieldset","form",'input:not([type="checkbox"])',"label","option","select","textarea","time","relative-time","[hidden]",'[aria-hidden="true"]:not([class*="math"])','[style*="display: none"]:not([class*="math"])','[style*="display:none"]:not([class*="math"])','[style*="visibility: hidden"]','[style*="visibility:hidden"]',".hidden",".invisible","instaread-player",'iframe:not([src*="youtube"]):not([src*="youtu.be"]):not([src*="vimeo"]):not([src*="twitter"]):not([src*="x.com"]):not([src*="datawrapper"])','[class="logo" i]',"#logo","#Logo","#newsletter","#Newsletter",".subscribe",".noprint",'[data-print-layout="hide" i]','[data-block="donotprint" i]','[class*="clickable-icon" i]','li span[class*="ltx_tag" i][class*="ltx_tag_item" i]','a[href^="#"][class*="anchor" i]','a[href^="#"][class*="ref" i]','[data-container*="most-viewed" i]',".sidebar",".Sidebar","#sidebar","#Sidebar","#sitesub",'[data-link-name*="skip" i]','[aria-label*="skip" i]',"#skip-link",".copyright","#copyright","#rss","#feed",".gutter","#primaryaudio","#NYT_ABOVE_MAIN_CONTENT_REGION",'[data-testid="photoviewer-children-figure"] > span',"table.infobox",".pencraft:not(.pc-display-contents)",'[data-optimizely="related-articles-section" i]','[data-orientation="vertical"]'],e.TEST_ATTRIBUTES=["class","id","data-test","data-testid","data-test-id","data-qa","data-cy"],e.PARTIAL_SELECTORS=["a-statement","access-wall","activitypub","actioncall","addcomment","advert","adlayout","ad-tldr","ad-placement","ads-container","_ad_","after_content","after_main_article","afterpost","allterms","-alert-","alert-box","appendix","_archive","around-the-web","aroundpages","article-author","article-badges","article-banner","article-bottom-section","article-bottom","article-category","article-card","article-citation","article__copy","article_date","article-date","article-end ","article_header","article-header","article__header","article__hero","article__info","article-info","article-meta","article_meta","article__meta","articlename","article-subject","article_subject","article-snippet","article-separator","article--share","article--topics","articletags","article-tags","article_tags","articletitle","article-title","article_title","articletopics","article-topics","article--lede","articlewell","associated-people","audio-card","author-bio","author-box","author-info","author_info","authorm","author-mini-bio","author-name","author-publish-info","authored-by","avatar","back-to-top","backlink_container","backlinks-section","bio-block","biobox","blog-pager","bookmark-","-bookmark","bottominfo","bottomnav","bottom-of-article","bottom-wrapper","brand-bar","breadcrumb","brdcrumb","button-wrapper","buttons-container","btn-","-btn","byline","captcha","card-text","card-media","card-post","carouselcontainer","carousel-container","cat_header","catlinks","_categories","card-author","card-content","chapter-list","collections","comments","commentbox","comment-button","commentcomp","comment-content","comment-count","comment-form","comment-number","comment-respond","comment-thread","comment-wrap","complementary","consent","contact-","content-card","content-topics","contentpromo","context-bar","context-widget","core-collateral","cover-","created-date","creative-commons_","c-subscribe","_cta","-cta","cta-","cta_","current-issue","custom-list-number","dateline","dateheader","date-header","date-pub","disclaimer","disclosure","discussion","discuss_","disqus","donate","donation","dropdown","eletters","emailsignup","engagement-widget","enhancement","entry-author-info","entry-categories","entry-date","entry-title","entry-utility","-error","error-","eyebrow","expand-reduce","external-anchor","externallinkembedwrapper","extra-services","extra-title","facebook","fancy-box","favorite","featured-content","feature_feed","feedback","feed-links","field-site-sections","fixheader","floating-vid","follower","footer","footnote-back","footnoteback","form-group","for-you","frontmatter","further-reading","fullbleedheader","gated-","gh-feed","gist-meta","goog-","graph-view","hamburger","header_logo","header-logo","header-pattern","hero-list","hide-for-print","hide-print","hide-when-no-script","hidden-print","hidden-sidenote","hidden-accessibility","infoline","instacartIntegration","interlude","interaction","itemendrow","invisible","jumplink","jump-to-","keepreading","keep-reading","keep_reading","keyword_wrap","kicker","labstab","-labels","language-name","lastupdated","latest-content","-ledes-","-license","license-","lightbox-popup","like-button","link-box","links-grid","links-title","listing-dynamic-terms","list-tags","listinks","loading","loa-info","logo_container","ltx_role_refnum","ltx_tag_bibitem","ltx_error","masthead","marketing","media-inquiry","-menu","menu-","metadata","might-like","minibio","more-about","_modal","-modal","more-","morenews","morestories","more_wrapper","most-read","move-helper","mw-editsection","mw-cite-backlink","mw-indicators","mw-jump-link","nav-","nav_","navigation-post","next-","newsgallery","news-story-title","newsletter_","newsletterbanner","newslettercontainer","newsletter-form","newsletter-signup","newslettersignup","newsletterwidget","newsletterwrapper","not-found","notessection","nomobile","noprint","open-slideshow","originally-published","other-blogs","outline-view","pagehead","page-header","page-title","paywall_message","-partners","permission-","plea","popular","popup_links","pop_stories","pop-up","post-author","post-bottom","post__category","postcomment","postdate","post-date","post_date","post-details","post-feeds","postinfo","post-info","post_info","post-inline-date","post-links","postlist","post_list","post_meta","post-meta","postmeta","post_more","postnavi","post-navigation","postpath","post-preview","postsnippet","post_snippet","post-snippet","post-subject","posttax","post-tax","post_tax","posttag","post_tag","post-tag","post_time","posttitle","post-title","post_title","post__title","post-ufi-button","prev-post","prevnext","prev_next","prev-next","previousnext","press-inquiries","print-none","print-header","print:hidden","privacy-notice","privacy-settings","profile","promo_article","promo-bar","promo-box","pubdate","pub_date","pub-date","publish_date","publish-date","publication-date","publicationName","qr-code","qr_code","quick_up","_rail","ratingssection","read_also","readmore","read-next","read_next","read_time","read-time","reading_time","reading-time","reading-list","recent-","recent-articles","recentpost","recent_post","recent-post","recommend","redirectedfrom","recirc","register","related","relevant","reversefootnote","_rss","rss-link","screen-reader-text","scroll_to","scroll-to","_search","-search","section-nav","series-banner","share-box","sharedaddy","share-icons","sharelinks","share-post","share-print","share-section","show-for-print","sidebartitle","sidebar-content","sidebar-wrapper","sideitems","sidebar-author","sidebar-item","side-box","side-logo","sign-in-gate","similar-","similar_","similars-","site-index","site-header","siteheader","site-logo","site-name","site-wordpress","skip-content","skip-to-content","c-skip-link","_skip-link","-slider","slug-wrap","social-author","social-shar","social-date","speechify-ignore","speedbump","sponsor","springercitation","sr-only","_stats","story-date","story-navigation","storyreadtime","storysmall","storypublishdate","subject-label","subhead","submenu","-subscribe-","subscriber-drive","subscription-","_tags","tags__item","tag_list","taxonomy","table-of-contents","tabs-","terminaltout","time-rubric","timestamp","time-read","time-to-read","tip_off","tiptout","-tout-","toc-container","toggle-caption","tooltip","topbar","topic-list","topic-subnav","top-wrapper","tree-item","trending","trust-feat","trust-badge","trust-project","twitter","u-hide","upsell","viewbottom","visually-hidden","welcomebox","widget_pages"],e.FOOTNOTE_INLINE_REFERENCES=["sup.reference","cite.ltx_cite",'sup[id^="fnr"]','span[id^="fnr"]','span[class*="footnote_ref"]',"span.footnote-link","a.citation",'a[id^="ref-link"]','a[href^="#fn"]','a[href^="#cite"]','a[href^="#reference"]','a[href^="#footnote"]','a[href^="#r"]','a[href^="#b"]','a[href*="cite_note"]','a[href*="cite_ref"]',"a.footnote-anchor","span.footnote-hovercard-target a",'a[role="doc-biblioref"]','a[id^="fnref"]','a[id^="ref-link"]'].join(","),e.FOOTNOTE_LIST_SELECTORS=["div.footnote ol","div.footnotes ol",'div[role="doc-endnotes"]','div[role="doc-footnotes"]',"ol.footnotes-list","ol.footnotes","ol.references",'ol[class*="article-references"]',"section.footnotes ol",'section[role="doc-endnotes"]','section[role="doc-footnotes"]','section[role="doc-bibliography"]',"ul.footnotes-list","ul.ltx_biblist",'div.footnote[data-component-name="FootnoteToDOM"]'].join(","),e.ALLOWED_EMPTY_ELEMENTS=new Set(["area","audio","base","br","circle","col","defs","ellipse","embed","figure","g","hr","iframe","img","input","line","link","mask","meta","object","param","path","pattern","picture","polygon","polyline","rect","source","stop","svg","td","th","track","use","video","wbr"]),e.ALLOWED_ATTRIBUTES=new Set(["alt","allow","allowfullscreen","aria-label","checked","colspan","controls","data-latex","data-src","data-srcset","data-lang","dir","display","frameborder","headers","height","href","lang","role","rowspan","src","srcset","title","type","width","accent","accentunder","align","columnalign","columnlines","columnspacing","columnspan","data-mjx-texclass","depth","displaystyle","fence","frame","framespacing","linethickness","lspace","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","rowalign","rowlines","rowspacing","rowspan","rspace","scriptlevel","separator","stretchy","symmetric","voffset","xmlns"]),e.ALLOWED_ATTRIBUTES_DEBUG=new Set(["class","id"])},649:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.imageRules=void 0;const n=r(552),o=/^data:image\/([^;]+);base64,/,i=/\.(jpg|jpeg|png|webp)\s+\d/,a=/^\s*\S+\.(jpg|jpeg|png|webp)\S*\s*$/,s=/\.(jpg|jpeg|png|webp|gif|avif)(\?.*)?$/i,l=/\s(\d+)w/,c=/dpr=(\d+(?:\.\d+)?)/,u=/^([^\s]+)/,d=/^[\w\-\.\/\\]+\.(jpg|jpeg|png|gif|webp|svg)$/i,m=/^\d{4}-\d{2}-\d{2}$/;function h(t,e,r){const o=r.createElement("figure");o.appendChild(t.cloneNode(!0));const i=r.createElement("figcaption"),a=function(t){const e=[],r=new Set,o=t=>{var i;if((0,n.isTextNode)(t)){const n=(null===(i=t.textContent)||void 0===i?void 0:i.trim())||"";n&&!r.has(n)&&(e.push(n),r.add(n))}else if((0,n.isElement)(t)){const e=t.childNodes;for(let t=0;t<e.length;t++)o(e[t])}},i=t.childNodes;for(let t=0;t<i.length;t++)o(i[t]);if(e.length>0)return e.join(" ");return t.innerHTML}(e);return i.innerHTML=a,o.appendChild(i),o}function p(t,e){e.setAttribute("srcset",t);const r=A(t);r&&b(r)&&e.setAttribute("src",r)}function g(t,e,r){for(let n=0;n<t.attributes.length;n++){const o=t.attributes[n];r.includes(o.name)||e.setAttribute(o.name,o.value)}}function f(t){const e=t.match(o);if(!e)return!1;if("svg+xml"===e[1])return!1;const r=e[0].length;return t.length-r<133}function v(t){return t.startsWith("data:image/svg+xml")}function b(t){return!t.startsWith("data:")&&(!(!t||""===t.trim())&&(s.test(t)||t.includes("image")||t.includes("img")||t.includes("photo")))}function y(t){if(E(t))return!0;return t.querySelectorAll("img, video, picture, source").length>0}function E(t){const e=t.tagName.toLowerCase();return"img"===e||"video"===e||"picture"===e||"source"===e}function C(t){if(E(t))return t;const e=t.querySelectorAll("picture");if(e.length>0)return e[0];const r=t.querySelectorAll("img"),n=[];for(let t=0;t<r.length;t++){const e=r[t],o=e.getAttribute("src")||"",i=e.getAttribute("alt")||"";o.includes("data:image/svg+xml")||(f(o)||!i.trim()&&r.length>1||n.push(e))}if(n.length>0)return n[0];const o=t.querySelectorAll("video");if(o.length>0)return o[0];const i=t.querySelectorAll("source");if(i.length>0)return i[0];const a=t.querySelectorAll("img, picture, source, video");return a.length>0?a[0]:null}function w(t){var e,r,n,o;const i=t.querySelector("figcaption");if(i)return i;const a=new Set,s=['[class*="caption"]','[class*="description"]','[class*="alt"]','[class*="title"]','[class*="credit"]','[class*="text"]','[class*="post-thumbnail-text"]','[class*="image-caption"]','[class*="photo-caption"]',"[aria-label]","[title]"].join(", "),l=t.querySelectorAll(s);for(let t=0;t<l.length;t++){const r=l[t];if(E(r))continue;const n=null===(e=r.textContent)||void 0===e?void 0:e.trim();if(n&&n.length>0&&!a.has(n))return a.add(n),r}const c=t.querySelector("img");if(c&&c.hasAttribute("alt")){const e=c.getAttribute("alt");if(e&&e.trim().length>0){const r=t.ownerDocument.createElement("div");return r.textContent=e,r}}if(t.parentElement){const e=t.parentElement.children;for(let n=0;n<e.length;n++){const o=e[n];if(o===t)continue;if(Array.from(o.classList).some((t=>t.includes("caption")||t.includes("credit")||t.includes("text")||t.includes("description")))){const t=null===(r=o.textContent)||void 0===r?void 0:r.trim();if(t&&t.length>0)return o}}}const u=t.querySelectorAll("img");for(let t=0;t<u.length;t++){const e=u[t];if(!e.parentElement)continue;let r=e.nextElementSibling;for(;r;){if(["EM","STRONG","SPAN","I","B","SMALL","CITE"].includes(r.tagName)){const t=null===(n=r.textContent)||void 0===n?void 0:n.trim();if(t&&t.length>0)return r}r=r.nextElementSibling}}for(let t=0;t<u.length;t++){const e=u[t],r=e.parentElement;if(!r)continue;const n=r.querySelectorAll("em, strong, span, i, b, small, cite");for(let t=0;t<n.length;t++){const r=n[t];if(r===e)continue;const i=null===(o=r.textContent)||void 0===o?void 0:o.trim();if(i&&i.length>0)return r}}return null}function x(t){var e;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";return!(r.length<10||r.startsWith("http://")||r.startsWith("https://"))&&(!d.test(r)&&(!r.match(/^\d+$/)&&!m.test(r)))}function S(t,e){const r=t.tagName.toLowerCase();if("img"===r)return T(t,e);if("picture"===r){const r=t.querySelector("img");return r?T(r,e):t.cloneNode(!0)}return"source"===r?function(t,e){const r=e.createElement("img"),n=t.getAttribute("srcset");n&&p(n,r);const o=t.parentElement;if(o){const t=o.querySelectorAll("img"),e=[];for(let r=0;r<t.length;r++){const n=t[r],o=n.getAttribute("src")||"";f(o)||v(o)||""===o||e.push(n)}if(e.length>0){if(g(e[0],r,["src","srcset"]),!r.hasAttribute("src")||!b(r.getAttribute("src")||"")){const t=e[0].getAttribute("src");t&&b(t)&&r.setAttribute("src",t)}}else{const t=o.querySelector("img[data-src]");if(t&&(g(t,r,["src","srcset"]),!r.hasAttribute("src")||!b(r.getAttribute("src")||""))){const e=t.getAttribute("data-src");e&&b(e)&&r.setAttribute("src",e)}}}return r}(t,e):t.cloneNode(!0)}function T(t,e){const r=t.getAttribute("src")||"";if(f(r)||v(r)){const r=t.parentElement;if(r){const n=r.querySelectorAll("source"),o=[];for(let t=0;t<n.length;t++){const e=n[t];e.hasAttribute("data-srcset")&&""!==e.getAttribute("data-srcset")&&o.push(e)}if(o.length>0){const r=e.createElement("img"),n=t.getAttribute("data-src");return n&&!v(n)&&r.setAttribute("src",n),g(t,r,["src"]),r}}}return t.cloneNode(!0)}function A(t){const e=t.split(",");if(0===e.length)return null;const r=e[0].trim().match(u);if(r&&r[1]){const t=r[1];if(v(t)){for(let t=1;t<e.length;t++){const r=e[t].trim().match(u);if(r&&r[1]&&!v(r[1]))return r[1]}return null}return t}return null}function L(t){if(0===t.length)return null;if(1===t.length)return t[0];for(let e=0;e<t.length;e++)if(!t[e].hasAttribute("media"))return t[e];let e=null,r=0;for(let n=0;n<t.length;n++){const o=t[n],i=o.getAttribute("srcset");if(!i)continue;const a=i.match(l),s=i.match(c);if(a&&a[1]){const t=parseInt(a[1],10)*(s?parseFloat(s[1]):1);t>r&&(r=t,e=o)}}return e||t[0]}e.imageRules=[{selector:"picture",element:"picture",transform:(t,e)=>{const r=t.querySelectorAll("source"),n=t.querySelector("img");if(!n){console.warn("Picture element without img fallback:",t.outerHTML);const n=L(r);if(n){const r=n.getAttribute("srcset");if(r){const n=e.createElement("img");return p(r,n),t.innerHTML="",t.appendChild(n),t}}return t}let o=null,i=null;if(r.length>0){const t=L(r);t&&(o=t.getAttribute("srcset"),o&&(i=A(o)))}if(o&&n.setAttribute("srcset",o),i&&b(i))n.setAttribute("src",i);else if(!n.hasAttribute("src")||!b(n.getAttribute("src")||"")){const t=A(n.getAttribute("srcset")||o||"");t&&b(t)&&n.setAttribute("src",t)}return r.forEach((t=>t.remove())),t}},{selector:"uni-image-full-width",element:"figure",transform:(t,e)=>{var r;const n=e.createElement("figure"),o=e.createElement("img"),i=t.querySelector("img");if(!i)return console.warn("uni-image-full-width without img:",t.outerHTML),n;let a=i.getAttribute("src");const s=i.getAttribute("data-loading");if(s)try{const t=JSON.parse(s);t.desktop&&b(t.desktop)&&(a=t.desktop)}catch(t){console.warn("Failed to parse data-loading attribute:",s,t)}if(!a||!b(a))return console.warn("Could not find valid src for uni-image-full-width:",t.outerHTML),n;o.setAttribute("src",a);let l=i.getAttribute("alt");l||(l=t.getAttribute("alt-text")),l&&o.setAttribute("alt",l),n.appendChild(o);const c=t.querySelector("figcaption");if(c){const t=null===(r=c.textContent)||void 0===r?void 0:r.trim();if(t&&t.length>5){const r=e.createElement("figcaption"),o=c.querySelector(".rich-text p");o?r.innerHTML=o.innerHTML:r.textContent=t,n.appendChild(r)}}return n}},{selector:'img[data-src], img[data-srcset], img[loading="lazy"], img.lazy, img.lazyload',element:"img",transform:(t,e)=>{const r=t.getAttribute("src")||"",n=function(t){if(t.hasAttribute("data-src")||t.hasAttribute("data-srcset"))return!0;for(let e=0;e<t.attributes.length;e++){const r=t.attributes[e];if("src"!==r.name){if(r.name.startsWith("data-")&&/\.(jpg|jpeg|png|webp|gif)(\?.*)?$/i.test(r.value))return!0;if(/\.(jpg|jpeg|png|webp|gif)(\?.*)?$/i.test(r.value))return!0}}return!1}(t);f(r)&&n&&t.removeAttribute("src");const o=t.getAttribute("data-src");o&&!t.getAttribute("src")&&t.setAttribute("src",o);const s=t.getAttribute("data-srcset");s&&!t.getAttribute("srcset")&&t.setAttribute("srcset",s);for(let e=0;e<t.attributes.length;e++){const r=t.attributes[e];"src"!==r.name&&"srcset"!==r.name&&"alt"!==r.name&&(i.test(r.value)?t.setAttribute("srcset",r.value):a.test(r.value)&&t.setAttribute("src",r.value))}return t.classList.remove("lazy","lazyload"),t.removeAttribute("data-ll-status"),t.removeAttribute("data-src"),t.removeAttribute("data-srcset"),t.removeAttribute("loading"),t}},{selector:"span:has(img)",element:"span",transform:(t,e)=>{try{if(!y(t))return t;const r=C(t);if(!r)return t;const n=w(t),o=S(r,e);if(n&&x(n)){const t=h(o,n,e);return n.parentNode&&n.parentNode.removeChild(n),t}return o}catch(e){return console.warn("Error processing span with image:",e),t}}},{selector:'figure, p:has([class*="caption"])',element:"figure",transform:(t,e)=>{try{if(!y(t))return t;const r=C(t);if(!r)return t;const n=w(t);if(n&&x(n)){const o=C(t);let i;return o?i=o:(console.warn("Figure rule couldn't find current image element in:",t.outerHTML),i=S(r,e)),h(i,n,e)}return t}catch(e){return console.warn("Error processing complex image element:",e),t}}}]},732:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GeminiExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.messageCount=null,this.conversationContainers=t.querySelectorAll("div.conversation-container"),this.footnotes=[]}canExtract(){return!!this.conversationContainers&&this.conversationContainers.length>0}extractMessages(){this.messageCount=0;const t=[];return this.conversationContainers?(this.extractSources(),this.conversationContainers.forEach((e=>{const r=e.querySelector("user-query");if(r){const e=r.querySelector(".query-text");if(e){const r=e.innerHTML||"";t.push({author:"You",content:r.trim(),metadata:{role:"user"}})}}const n=e.querySelector("model-response");if(n){const e=n.querySelector(".model-response-text .markdown"),r=n.querySelector("#extended-response-markdown-content")||e;if(r){let e=r.innerHTML||"";const n=document.createElement("div");n.innerHTML=e,n.querySelectorAll(".table-content").forEach((t=>{t.classList.remove("table-content")})),e=n.innerHTML,t.push({author:"Gemini",content:e.trim(),metadata:{role:"assistant"}})}}})),this.messageCount=t.length,t):t}extractSources(){const t=this.document.querySelectorAll("browse-item");t&&t.length>0&&t.forEach((t=>{var e,r,n,o;const i=t.querySelector("a");if(i instanceof HTMLAnchorElement){const t=i.href,a=(null===(r=null===(e=i.querySelector(".domain"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||"",s=(null===(o=null===(n=i.querySelector(".title"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||"";t&&(a||s)&&this.footnotes.push({url:t,text:s?`${a}: ${s}`:a})}}))}getFootnotes(){return this.footnotes}getMetadata(){var t;const e=this.getTitle(),r=null!==(t=this.messageCount)&&void 0!==t?t:this.extractMessages().length;return{title:e,site:"Gemini",url:this.url,messageCount:r,description:`Gemini conversation with ${r} messages`}}getTitle(){var t,e,r,n,o;const i=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(i&&"Gemini"!==i&&!i.includes("Gemini"))return i;const a=null===(r=null===(e=this.document.querySelector(".title-text"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim();if(a)return a;const s=null===(o=null===(n=this.conversationContainers)||void 0===n?void 0:n.item(0))||void 0===o?void 0:o.querySelector(".query-text");if(s){const t=s.textContent||"";return t.length>50?t.slice(0,50)+"...":t}return"Gemini Conversation"}}e.GeminiExtractor=o},754:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.codeBlockRules=void 0;const n=r(552),o=[/^language-(\w+)$/,/^lang-(\w+)$/,/^(\w+)-code$/,/^code-(\w+)$/,/^syntax-(\w+)$/,/^code-snippet__(\w+)$/,/^highlight-(\w+)$/,/^(\w+)-snippet$/,/(?:^|\s)(?:language|lang|brush|syntax)-(\w+)(?:\s|$)/i],i=new Set(["abap","actionscript","ada","adoc","agda","antlr4","applescript","arduino","armasm","asciidoc","aspnet","atom","bash","batch","c","clojure","cmake","cobol","coffeescript","cpp","c++","crystal","csharp","cs","dart","django","dockerfile","dotnet","elixir","elm","erlang","fortran","fsharp","gdscript","gitignore","glsl","golang","gradle","graphql","groovy","haskell","hs","haxe","hlsl","html","idris","java","javascript","js","jsx","jsdoc","json","jsonp","julia","kotlin","latex","lisp","elisp","livescript","lua","makefile","markdown","md","markup","masm","mathml","matlab","mongodb","mysql","nasm","nginx","nim","nix","objc","ocaml","pascal","perl","php","postgresql","powershell","prolog","puppet","python","regex","rss","ruby","rb","rust","scala","scheme","shell","sh","solidity","sparql","sql","ssml","svg","swift","tcl","terraform","tex","toml","typescript","ts","tsx","unrealscript","verilog","vhdl","webassembly","wasm","xml","yaml","yml","zig"]);e.codeBlockRules=[{selector:["pre",'div[class*="prismjs"]',".syntaxhighlighter",".highlight",".highlight-source",".wp-block-syntaxhighlighter-code",".wp-block-code",'div[class*="language-"]'].join(", "),element:"pre",transform:(t,e)=>{if(!(t=>"classList"in t&&"getAttribute"in t&&"querySelector"in t)(t))return t;const r=t=>{var e;const r=t.getAttribute("data-lang")||t.getAttribute("data-language");if(r)return r.toLowerCase();const n=Array.from(t.classList||[]);if(null===(e=t.classList)||void 0===e?void 0:e.contains("syntaxhighlighter")){const t=n.find((t=>!["syntaxhighlighter","nogutter"].includes(t)));if(t&&i.has(t.toLowerCase()))return t.toLowerCase()}for(const t of n)for(const e of o){const r=t.toLowerCase().match(e);if(r&&r[1]&&i.has(r[1].toLowerCase()))return r[1].toLowerCase()}for(const t of n)if(i.has(t.toLowerCase()))return t.toLowerCase();return""};let a="",s=t;for(;s&&!a;){a=r(s);const t=s.querySelector("code");!a&&t&&(a=r(t)),s=s.parentElement}const l=t=>{if((0,n.isTextNode)(t))return t.textContent||"";let e="";if((0,n.isElement)(t)){if("BR"===t.tagName)return"\n";if(t.matches('div[class*="line"], span[class*="line"], .ec-line, [data-line-number], [data-line]')){const e=t.querySelector('.code, .content, [class*="code-"], [class*="content-"]');if(e)return(e.textContent||"")+"\n";const r=t.querySelector('.line-number, .gutter, [class*="line-number"], [class*="gutter"]');if(r){return Array.from(t.childNodes).filter((t=>!r.contains(t))).map((t=>l(t))).join("")+"\n"}return t.textContent+"\n"}t.childNodes.forEach((t=>{e+=l(t)}))}return e};let c="";t.matches(".syntaxhighlighter, .wp-block-syntaxhighlighter-code")&&(c=(t=>{const e=t.querySelector(".syntaxhighlighter table .code .container");if(e)return Array.from(e.children).map((t=>{const e=Array.from(t.querySelectorAll("code")).map((t=>{var e;let r=t.textContent||"";return(null===(e=t.classList)||void 0===e?void 0:e.contains("spaces"))&&(r=" ".repeat(r.length)),r})).join("");return e||t.textContent||""})).join("\n");const r=t.querySelectorAll(".code .line");return r.length>0?Array.from(r).map((t=>{const e=Array.from(t.querySelectorAll("code")).map((t=>t.textContent||"")).join("");return e||t.textContent||""})).join("\n"):""})(t)),c||(c=l(t)),c=c.replace(/^\s+|\s+$/g,"").replace(/\t/g,"    ").replace(/\n{3,}/g,"\n\n").replace(/\u00a0/g," ").replace(/^\n+/,"").replace(/\n+$/,"");const u=e.createElement("pre"),d=e.createElement("code");return a&&(d.setAttribute("data-lang",a),d.setAttribute("class",`language-${a}`)),d.textContent=c,u.appendChild(d),u}}]},840:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.standardizeContent=function(t,e,r,o=!1){(function(t){const e=t=>{if((0,c.isElement)(t)){const e=t.tagName.toLowerCase();if("pre"===e||"code"===e)return}if((0,c.isTextNode)(t)){const e=t.textContent||"",r=e.replace(/\xA0+/g,(e=>{var r,n,o,i;if(1===e.length){const e=null===(n=null===(r=t.previousSibling)||void 0===r?void 0:r.textContent)||void 0===n?void 0:n.slice(-1),a=null===(i=null===(o=t.nextSibling)||void 0===o?void 0:o.textContent)||void 0===i?void 0:i.charAt(0);if((null==e?void 0:e.match(/\w/))&&(null==a?void 0:a.match(/\w/)))return"\xa0"}return" ".repeat(e.length)}));r!==e&&(t.textContent=r)}t.hasChildNodes()&&Array.from(t.childNodes).forEach(e)};e(t)})(t),function(t){let e=0;Array.from(t.getElementsByTagName("*")).forEach((t=>{Array.from(t.childNodes).forEach((t=>{(0,c.isCommentNode)(t)&&(t.remove(),e++)}))})),(0,c.logDebug)("Removed HTML comments:",e)}(t),function(t,e,r){const o=t=>t.replace(/\u00A0/g," ").replace(/\s+/g," ").trim().toLowerCase(),i=t.getElementsByTagName("h1");Array.from(i).forEach((t=>{var e;const o=r.createElement("h2");o.innerHTML=t.innerHTML,Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&o.setAttribute(t.name,t.value)})),null===(e=t.parentNode)||void 0===e||e.replaceChild(o,t)}));const a=t.getElementsByTagName("h2");if(a.length>0){const t=a[0],r=o(t.textContent||""),n=o(e);n&&n===r&&t.remove()}}(t,e.title,r),(0,a.standardizeFootnotes)(t),function(t,e){let r=0;u.forEach((n=>{t.querySelectorAll(n.selector).forEach((t=>{if(n.transform){const o=n.transform(t,e);t.replaceWith(o),r++}}))}));t.querySelectorAll("lite-youtube").forEach((t=>{const n=t.getAttribute("videoid");if(!n)return;const o=e.createElement("iframe");o.width="560",o.height="315",o.src=`https://www.youtube.com/embed/${n}`,o.title=t.getAttribute("videotitle")||"YouTube video player",o.frameBorder="0",o.allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share",o.setAttribute("allowfullscreen",""),t.replaceWith(o),r++})),(0,c.logDebug)("Converted embedded elements:",r)}(t,r),o?(m(t,o),d(t),h(t),(0,c.logDebug)("Debug mode: Skipping div flattening to preserve structure")):(p(t,r),m(t,o),function(t){let e=0,r=0,o=!0;for(;o;){r++,o=!1;const i=Array.from(t.getElementsByTagName("*")).filter((t=>{if(n.ALLOWED_EMPTY_ELEMENTS.has(t.tagName.toLowerCase()))return!1;const e=t.textContent||"",r=0===e.trim().length,o=e.includes("\xa0"),i=!t.hasChildNodes()||Array.from(t.childNodes).every((t=>{if((0,c.isTextNode)(t)){const e=t.textContent||"";return 0===e.trim().length&&!e.includes("\xa0")}return!1}));if("div"===t.tagName.toLowerCase()){const e=Array.from(t.children);if(e.length>0&&e.every((t=>{var e;if("span"!==t.tagName.toLowerCase())return!1;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||"";return","===r||""===r||" "===r})))return!0}return r&&!o&&i}));i.length>0&&(i.forEach((t=>{t.remove(),e++})),o=!0)}(0,c.logDebug)("Removed empty elements:",e,"iterations:",r)}(t),d(t),p(t,r),h(t),function(t,e){let r=0;const n=Date.now(),o=t=>{var e;if((0,c.isElement)(t)){const e=t.tagName.toLowerCase();if("pre"===e||"code"===e)return}if(Array.from(t.childNodes).forEach(o),(0,c.isTextNode)(t)){const n=t.textContent||"";if(!n||n.match(/^[\u200C\u200B\u200D\u200E\u200F\uFEFF\xA0\s]*$/))null===(e=t.parentNode)||void 0===e||e.removeChild(t),r++;else{const e=n.replace(/\n{3,}/g,"\n\n").replace(/^[\n\r\t]+/,"").replace(/[\n\r\t]+$/,"").replace(/[ \t]*\n[ \t]*/g,"\n").replace(/[ \t]{3,}/g," ").replace(/^[ ]+$/," ").replace(/\s+([,.!?:;])/g,"$1").replace(/[\u200C\u200B\u200D\u200E\u200F\uFEFF]+/g,"").replace(/(?:\xA0){2,}/g,"\xa0");e!==n&&(t.textContent=e,r+=n.length-e.length)}}},i=t=>{var n;if(!(0,c.isElement)(t))return;const o=t.tagName.toLowerCase();if("pre"===o||"code"===o)return;Array.from(t.childNodes).filter(c.isElement).forEach(i),t.normalize();const a="block"===(null===(n=(0,c.getComputedStyle)(t))||void 0===n?void 0:n.display),s=a?/^[\n\r\t \u200C\u200B\u200D\u200E\u200F\uFEFF\xA0]*$/:/^[\n\r\t\u200C\u200B\u200D\u200E\u200F\uFEFF]*$/,l=a?/^[\n\r\t \u200C\u200B\u200D\u200E\u200F\uFEFF\xA0]*$/:/^[\n\r\t\u200C\u200B\u200D\u200E\u200F\uFEFF]*$/;for(;t.firstChild&&(0,c.isTextNode)(t.firstChild)&&(t.firstChild.textContent||"").match(s);)t.removeChild(t.firstChild),r++;for(;t.lastChild&&(0,c.isTextNode)(t.lastChild)&&(t.lastChild.textContent||"").match(l);)t.removeChild(t.lastChild),r++;if(!a){const r=Array.from(t.childNodes);for(let n=0;n<r.length-1;n++){const o=r[n],i=r[n+1];if((0,c.isElement)(o)||(0,c.isElement)(i)){const r=i.textContent||"",n=o.textContent||"",a=r.match(/^[,.!?:;)\]]/),s=n.match(/[,.!?:;(\[]\s*$/),l=(0,c.isTextNode)(o)&&(o.textContent||"").endsWith(" ")||(0,c.isTextNode)(i)&&(i.textContent||"").startsWith(" ");if(!a&&!s&&!l){const r=e.createTextNode(" ");t.insertBefore(r,i)}}}}};o(t),i(t);const a=Date.now();(0,c.logDebug)("Removed empty lines:",{charactersRemoved:r,processingTime:`${(a-n).toFixed(2)}ms`})}(t,r))};const n=r(640),o=r(0),i=r(754),a=r(610),s=r(864),l=r(649),c=r(552),u=[...o.mathRules,...i.codeBlockRules,...s.headingRules,...l.imageRules,{selector:'div[data-testid^="paragraph"], div[role="paragraph"]',element:"p",transform:(t,e)=>{const r=e.createElement("p");return r.innerHTML=t.innerHTML,Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&r.setAttribute(t.name,t.value)})),r}},{selector:'div[role="list"]',element:"ul",transform:(t,e)=>{var r;const n=t.querySelector('div[role="listitem"] .label'),o=((null===(r=null==n?void 0:n.textContent)||void 0===r?void 0:r.trim())||"").match(/^\d+\)/),i=e.createElement(o?"ol":"ul");return t.querySelectorAll('div[role="listitem"]').forEach((t=>{const r=e.createElement("li"),n=t.querySelector(".content");if(n){n.querySelectorAll('div[role="paragraph"]').forEach((t=>{const r=e.createElement("p");r.innerHTML=t.innerHTML,t.replaceWith(r)}));n.querySelectorAll('div[role="list"]').forEach((t=>{var r;const n=t.querySelector('div[role="listitem"] .label'),o=((null===(r=null==n?void 0:n.textContent)||void 0===r?void 0:r.trim())||"").match(/^\d+\)/),i=e.createElement(o?"ol":"ul");t.querySelectorAll('div[role="listitem"]').forEach((t=>{const r=e.createElement("li"),n=t.querySelector(".content");if(n){n.querySelectorAll('div[role="paragraph"]').forEach((t=>{const r=e.createElement("p");r.innerHTML=t.innerHTML,t.replaceWith(r)})),r.innerHTML=n.innerHTML}i.appendChild(r)})),t.replaceWith(i)})),r.innerHTML=n.innerHTML}i.appendChild(r)})),i}},{selector:'div[role="listitem"]',element:"li",transform:(t,e)=>{const r=t.querySelector(".content");if(!r)return t;return r.querySelectorAll('div[role="paragraph"]').forEach((t=>{const r=e.createElement("p");r.innerHTML=t.innerHTML,t.replaceWith(r)})),r}}];function d(t){let e=0;const r=e=>{let n="",o=e.nextSibling;for(;o;)((0,c.isTextNode)(o)||(0,c.isElement)(o))&&(n+=o.textContent||""),o=o.nextSibling;if(n.trim())return!0;const i=e.parentElement;return!(!i||i===t)&&r(i)};Array.from(t.querySelectorAll("h1, h2, h3, h4, h5, h6")).reverse().forEach((t=>{r(t)||(t.remove(),e++)})),e>0&&(0,c.logDebug)("Removed trailing headings:",e)}function m(t,e){let r=0;const o=t=>{if("svg"===t.tagName.toLowerCase()||"http://www.w3.org/2000/svg"===t.namespaceURI)return;const o=Array.from(t.attributes),i=t.tagName.toLowerCase();o.forEach((o=>{const a=o.name.toLowerCase(),s=o.value;"id"===a&&(s.startsWith("fnref:")||s.startsWith("fn:")||"footnotes"===s)||"class"===a&&("code"===i&&s.startsWith("language-")||"footnote-backref"===s)||(e?n.ALLOWED_ATTRIBUTES.has(a)||n.ALLOWED_ATTRIBUTES_DEBUG.has(a)||a.startsWith("data-")||(t.removeAttribute(o.name),r++):n.ALLOWED_ATTRIBUTES.has(a)||(t.removeAttribute(o.name),r++))}))};o(t),t.querySelectorAll("*").forEach(o),(0,c.logDebug)("Stripped attributes:",r)}function h(t){let e=0;const r=Date.now(),n=Array.from(t.getElementsByTagName("br"));let o=[];const i=()=>{if(o.length>2)for(let t=2;t<o.length;t++)o[t].remove(),e++;o=[]};n.forEach((t=>{var e;let r=!1;if(o.length>0){const n=o[o.length-1];let i=t.previousSibling;for(;i&&(0,c.isTextNode)(i)&&!(null===(e=i.textContent)||void 0===e?void 0:e.trim());)i=i.previousSibling;i===n&&(r=!0)}r?o.push(t):(i(),o=[t])})),i();const a=Date.now();(0,c.logDebug)("Standardized br elements:",{removed:e,processingTime:`${(a-r).toFixed(2)}ms`})}function p(t,e){let r=0;const o=Date.now();let i=!0;function a(t){var e;for(const r of t.childNodes){if((0,c.isTextNode)(r)&&(null===(e=r.textContent)||void 0===e?void 0:e.trim()))return!0;if((0,c.isElement)(r)&&n.INLINE_ELEMENTS.has(r.nodeName.toLowerCase()))return!0}return!1}const s=t=>{const e=t.tagName.toLowerCase();if(n.PRESERVE_ELEMENTS.has(e))return!0;const r=t.getAttribute("role");if(r&&["article","main","navigation","banner","contentinfo"].includes(r))return!0;const o=t.className;if("string"==typeof o&&o.toLowerCase().match(/(?:article|main|content|footnote|reference|bibliography)/))return!0;return!!Array.from(t.children).some((t=>n.PRESERVE_ELEMENTS.has(t.tagName.toLowerCase())||"article"===t.getAttribute("role")||t.className&&"string"==typeof t.className&&t.className.toLowerCase().match(/(?:article|main|content|footnote|reference|bibliography)/)))},l=t=>{var e;if(a(t))return!1;if(!(null===(e=t.textContent)||void 0===e?void 0:e.trim()))return!0;const r=Array.from(t.children);if(0===r.length)return!0;if(r.every((t=>{const e=t.tagName.toLowerCase();return n.BLOCK_ELEMENTS.includes(e)||"p"===e||"h1"===e||"h2"===e||"h3"===e||"h4"===e||"h5"===e||"h6"===e||"ul"===e||"ol"===e||"pre"===e||"blockquote"===e||"figure"===e})))return!0;const o=t.className.toLowerCase();if(/(?:wrapper|container|layout|row|col|grid|flex|outer|inner|content-area)/i.test(o))return!0;const i=Array.from(t.childNodes).filter((t=>{var e;return(0,c.isTextNode)(t)&&(null===(e=t.textContent)||void 0===e?void 0:e.trim())}));if(0===i.length)return!0;return!(!(r.length>0)||r.some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)})))},u=o=>{var i,u;if(!o.isConnected||s(o))return!1;const d=o.tagName.toLowerCase();if(!n.ALLOWED_EMPTY_ELEMENTS.has(d)&&!o.children.length&&!(null===(i=o.textContent)||void 0===i?void 0:i.trim()))return o.remove(),r++,!0;if(o.parentElement===t){const t=Array.from(o.children);if(t.length>0&&!t.some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)}))){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}}if(l(o)){if(!Array.from(o.children).some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)}))){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}const m=Array.from(o.childNodes);if(m.length>0&&m.every((t=>(0,c.isTextNode)(t)||(0,c.isElement)(t)&&n.INLINE_ELEMENTS.has(t.nodeName.toLowerCase())))&&(null===(u=o.textContent)||void 0===u?void 0:u.trim())){const t=e.createElement("p");for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}if(1===o.children.length){const t=o.firstElementChild,e=t.tagName.toLowerCase();if(n.BLOCK_ELEMENTS.includes(e)&&!s(t))return o.replaceWith(t),r++,!0}let h=0,p=o.parentElement;for(;p;){const t=p.tagName.toLowerCase();n.BLOCK_ELEMENTS.includes(t)&&h++,p=p.parentElement}if(h>0&&!a(o)){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}return!1},d=()=>{const e=Array.from(t.children).filter((t=>n.BLOCK_ELEMENTS.includes(t.tagName.toLowerCase())));let r=!1;return e.forEach((t=>{u(t)&&(r=!0)})),r},m=()=>{const e=Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(","))).sort(((t,e)=>{const r=t=>{let e=0,r=t.parentElement;for(;r;){const t=r.tagName.toLowerCase();n.BLOCK_ELEMENTS.includes(t)&&e++,r=r.parentElement}return e};return r(e)-r(t)}));let r=!1;return e.forEach((t=>{u(t)&&(r=!0)})),r},h=()=>{const o=Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(",")));let i=!1;return o.forEach((t=>{const n=Array.from(t.children);if(n.length>0&&n.every((t=>"p"===t.tagName.toLowerCase()))||!s(t)&&l(t)){const n=e.createDocumentFragment();for(;t.firstChild;)n.appendChild(t.firstChild);t.replaceWith(n),r++,i=!0}})),i};do{i=!1,d()&&(i=!0),m()&&(i=!0),h()&&(i=!0)}while(i);const p=Date.now();(0,c.logDebug)("Flattened wrapper elements:",{count:r,processingTime:`${(p-o).toFixed(2)}ms`})}},864:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.headingRules=void 0;const n=r(640);e.headingRules=[{selector:"h1, h2, h3, h4, h5, h6",element:"keep",transform:t=>{var e;const r=t.ownerDocument;if(!r)return console.warn("No document available"),t;const o=r.createElement(t.tagName);Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&o.setAttribute(t.name,t.value)}));const i=t.cloneNode(!0),a=new Map;Array.from(i.querySelectorAll("*")).forEach((t=>{var e,r,n,o,s,l;let c=!1;if("a"===t.tagName.toLowerCase()){const r=t.getAttribute("href");((null==r?void 0:r.includes("#"))||(null==r?void 0:r.startsWith("#")))&&(a.set(t,(null===(e=t.textContent)||void 0===e?void 0:e.trim())||""),c=!0)}if(t.classList.contains("anchor")&&(a.set(t,(null===(r=t.textContent)||void 0===r?void 0:r.trim())||""),c=!0),"button"===t.tagName.toLowerCase()&&(c=!0),("span"===t.tagName.toLowerCase()||"div"===t.tagName.toLowerCase())&&t.querySelector('a[href^="#"]')){const e=t.querySelector('a[href^="#"]');e&&a.set(t,(null===(n=e.textContent)||void 0===n?void 0:n.trim())||""),c=!0}if(c){const e=t.parentElement;e&&e!==i&&(null===(o=e.textContent)||void 0===o?void 0:o.trim())===(null===(s=t.textContent)||void 0===s?void 0:s.trim())&&a.set(e,(null===(l=t.textContent)||void 0===l?void 0:l.trim())||"")}}));Array.from(i.querySelectorAll("*")).filter((t=>{if("a"===t.tagName.toLowerCase()){const e=t.getAttribute("href");return(null==e?void 0:e.includes("#"))||(null==e?void 0:e.startsWith("#"))}return!!t.classList.contains("anchor")||("button"===t.tagName.toLowerCase()||!("span"!==t.tagName.toLowerCase()&&"div"!==t.tagName.toLowerCase()||!t.querySelector('a[href^="#"]')))})).forEach((t=>t.remove()));let s=(null===(e=i.textContent)||void 0===e?void 0:e.trim())||"";return!s&&a.size>0&&(s=Array.from(a.values())[0]),o.textContent=s,o}}]},917:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ExtractorRegistry=void 0;const n=r(959),o=r(248),i=r(258),a=r(458),s=r(632),l=r(397),c=r(20),u=r(732);class d{static initialize(){this.register({patterns:["twitter.com",/\/x\.com\/.*/],extractor:o.TwitterExtractor}),this.register({patterns:["reddit.com","old.reddit.com","new.reddit.com",/^https:\/\/[^\/]+\.reddit\.com/],extractor:n.RedditExtractor}),this.register({patterns:["youtube.com","youtu.be",/youtube\.com\/watch\?v=.*/,/youtu\.be\/.*/],extractor:i.YoutubeExtractor}),this.register({patterns:[/news\.ycombinator\.com\/item\?id=.*/],extractor:a.HackerNewsExtractor}),this.register({patterns:[/^https?:\/\/chatgpt\.com\/(c|share)\/.*/],extractor:s.ChatGPTExtractor}),this.register({patterns:[/^https?:\/\/claude\.ai\/(chat|share)\/.*/],extractor:l.ClaudeExtractor}),this.register({patterns:[/^https?:\/\/grok\.com\/(chat|share)(\/.*)?$/],extractor:c.GrokExtractor}),this.register({patterns:[/^https?:\/\/gemini\.google\.com\/app\/.*/],extractor:u.GeminiExtractor})}static register(t){this.mappings.push(t)}static findExtractor(t,e,r){try{const n=new URL(e).hostname;if(this.domainCache.has(n)){const o=this.domainCache.get(n);return o?new o(t,e,r):null}for(const{patterns:o,extractor:i}of this.mappings){if(o.some((t=>t instanceof RegExp?t.test(e):n.includes(t))))return this.domainCache.set(n,i),new i(t,e,r)}return this.domainCache.set(n,null),null}catch(t){return console.error("Error in findExtractor:",t),null}}static clearCache(){this.domainCache.clear()}}e.ExtractorRegistry=d,d.mappings=[],d.domainCache=new Map,d.initialize()},959:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.RedditExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){super(t,e),this.shredditPost=t.querySelector("shreddit-post")}canExtract(){return!!this.shredditPost}extract(){var t,e;const r=this.getPostContent(),n=this.extractComments(),o=this.createContentHtml(r,n),i=(null===(e=null===(t=this.document.querySelector("h1"))||void 0===t?void 0:t.textContent)||void 0===e?void 0:e.trim())||"",a=this.getSubreddit(),s=this.getPostAuthor(),l=this.createDescription(r);return{content:o,contentHtml:o,extractedContent:{postId:this.getPostId(),subreddit:a,postAuthor:s},variables:{title:i,author:s,site:`r/${a}`,description:l}}}getPostContent(){var t,e,r,n;return((null===(e=null===(t=this.shredditPost)||void 0===t?void 0:t.querySelector('[slot="text-body"]'))||void 0===e?void 0:e.innerHTML)||"")+((null===(n=null===(r=this.shredditPost)||void 0===r?void 0:r.querySelector("#post-image"))||void 0===n?void 0:n.outerHTML)||"")}createContentHtml(t,e){return`\n\t\t\t<div class="reddit-post">\n\t\t\t\t<div class="post-content">\n\t\t\t\t\t${t}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t${e?`\n\t\t\t\t<hr>\n\t\t\t\t<h2>Comments</h2>\n\t\t\t\t<div class="reddit-comments">\n\t\t\t\t\t${e}\n\t\t\t\t</div>\n\t\t\t`:""}\n\t\t`.trim()}extractComments(){const t=Array.from(this.document.querySelectorAll("shreddit-comment"));return this.processComments(t)}getPostId(){const t=this.url.match(/comments\/([a-zA-Z0-9]+)/);return(null==t?void 0:t[1])||""}getSubreddit(){const t=this.url.match(/\/r\/([^/]+)/);return(null==t?void 0:t[1])||""}getPostAuthor(){var t;return(null===(t=this.shredditPost)||void 0===t?void 0:t.getAttribute("author"))||""}createDescription(t){var e;if(!t)return"";const r=document.createElement("div");return r.innerHTML=t,(null===(e=r.textContent)||void 0===e?void 0:e.trim().slice(0,140).replace(/\s+/g," "))||""}processComments(t){var e;let r="",n=-1,o=[];for(const i of t){const t=parseInt(i.getAttribute("depth")||"0"),a=i.getAttribute("author")||"",s=i.getAttribute("score")||"0",l=i.getAttribute("permalink")||"",c=(null===(e=i.querySelector('[slot="comment"]'))||void 0===e?void 0:e.innerHTML)||"",u=i.querySelector("faceplate-timeago"),d=(null==u?void 0:u.getAttribute("ts"))||"",m=d?new Date(d).toISOString().split("T")[0]:"";if(0===t){for(;o.length>0;)r+="</blockquote>",o.pop();r+="<blockquote>",o=[0],n=0}else if(t<n)for(;o.length>0&&o[o.length-1]>=t;)r+="</blockquote>",o.pop();else t>n&&(r+="<blockquote>",o.push(t));r+=`<div class="comment">\n\t<div class="comment-metadata">\n\t\t<span class="comment-author"><strong>${a}</strong></span> \u2022\n\t\t<a href="https://reddit.com${l}" class="comment-link">${s} points</a> \u2022\n\t\t<span class="comment-date">${m}</span>\n\t</div>\n\t<div class="comment-content">${c}</div>\n</div>`,n=t}for(;o.length>0;)r+="</blockquote>",o.pop();return r}}e.RedditExtractor=o},968:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.ContentScorer=void 0;const n=r(640),o=["admonition","article","content","entry","image","img","font","figure","figcaption","pre","main","post","story","table"],i=["advertisement","all rights reserved","banner","cookie","comments","copyright","follow me","follow us","footer","header","homepage","login","menu","more articles","more like this","most read","nav","navigation","newsletter","newsletter","popular","privacy","recommended","register","related","responses","share","sidebar","sign in","sign up","signup","social","sponsored","subscribe","subscribe","terms","trending"],a=["ad","banner","cookie","copyright","footer","header","homepage","menu","nav","newsletter","popular","privacy","recommended","related","rights","share","sidebar","social","sponsored","subscribe","terms","trending","widget"];class s{constructor(t,e=!1){this.doc=t,this.debug=e}static scoreElement(t){let e=0;const r=t.textContent||"",o=r.split(/\s+/).length;e+=o;e+=10*t.getElementsByTagName("p").length;e-=5*(t.getElementsByTagName("a").length/(o||1));e-=3*(t.getElementsByTagName("img").length/(o||1));try{const r=t.getAttribute("style")||"",n=t.getAttribute("align")||"";(r.includes("float: right")||r.includes("text-align: right")||"right"===n)&&(e+=5)}catch(t){}/\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4}\b/i.test(r)&&(e+=10);/\b(?:by|written by|author:)\s+[A-Za-z\s]+\b/i.test(r)&&(e+=10);const i=t.className.toLowerCase();(i.includes("content")||i.includes("article")||i.includes("post"))&&(e+=15);t.querySelector(n.FOOTNOTE_INLINE_REFERENCES)&&(e+=10);t.querySelector(n.FOOTNOTE_LIST_SELECTORS)&&(e+=10);if(e-=5*t.getElementsByTagName("table").length,"td"===t.tagName.toLowerCase()){const r=t.closest("table");if(r){const n=parseInt(r.getAttribute("width")||"0"),o=r.getAttribute("align")||"",i=r.className.toLowerCase();if(n>400||"center"===o||i.includes("content")||i.includes("article")){const n=Array.from(r.getElementsByTagName("td")),o=n.indexOf(t);o>0&&o<n.length-1&&(e+=10)}}}return e}static findBestElement(t,e=50){let r=null,n=0;return t.forEach((t=>{const e=this.scoreElement(t);e>n&&(n=e,r=t)})),n>e?r:null}static scoreAndRemove(t,e=!1){const r=Date.now();let o=0;const i=new Set;Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(","))).forEach((t=>{if(i.has(t))return;if(s.isLikelyContent(t))return;s.scoreNonContentBlock(t)<0&&(i.add(t),o++)})),i.forEach((t=>t.remove()));const a=Date.now();e&&console.log("Defuddle","Removed non-content blocks:",{count:o,processingTime:`${(a-r).toFixed(2)}ms`})}static isLikelyContent(t){const e=t.getAttribute("role");if(e&&["article","main","contentinfo"].includes(e))return!0;const r=t.className.toLowerCase(),n=t.id.toLowerCase();for(const t of o)if(r.includes(t)||n.includes(t))return!0;const i=(t.textContent||"").split(/\s+/).length,a=t.getElementsByTagName("p").length;return i>50&&a>1||(i>100||i>30&&a>0)}static scoreNonContentBlock(t){if(t.querySelector(n.FOOTNOTE_LIST_SELECTORS))return 0;let e=0;const r=t.textContent||"",o=r.split(/\s+/).length;if(o<3)return 0;for(const t of i)r.toLowerCase().includes(t)&&(e-=10);const s=t.getElementsByTagName("a").length;s/(o||1)>.5&&(e-=15);const l=t.getElementsByTagName("ul").length+t.getElementsByTagName("ol").length;l>0&&s>3*l&&(e-=10);const c=t.className.toLowerCase(),u=t.id.toLowerCase();for(const t of a)(c.includes(t)||u.includes(t))&&(e-=8);return e}}e.ContentScorer=s}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}var n={};return(()=>{var t=n;const e=r(628);t.default=e.Defuddle})(),n=n.default})()));

/***/ }),

/***/ "./node_modules/immer/dist/immer.mjs":
/*!*******************************************!*\
  !*** ./node_modules/immer/dist/immer.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   current: () => (/* binding */ current),
/* harmony export */   isDraft: () => (/* binding */ isDraft),
/* harmony export */   isDraftable: () => (/* binding */ isDraftable),
/* harmony export */   produce: () => (/* binding */ produce)
/* harmony export */ });
/* unused harmony exports Immer, applyPatches, castDraft, castImmutable, createDraft, enableMapSet, enablePatches, finishDraft, freeze, immerable, nothing, original, produceWithPatches, setAutoFreeze, setUseStrictShallowCopy */
// src/utils/env.ts
var NOTHING = Symbol.for("immer-nothing");
var DRAFTABLE = Symbol.for("immer-draftable");
var DRAFT_STATE = Symbol.for("immer-state");

// src/utils/errors.ts
var errors =  true ? [
  // All error codes, starting by 0:
  function(plugin) {
    return `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \`enable${plugin}()\` when initializing your application.`;
  },
  function(thing) {
    return `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`;
  },
  "This object has been frozen and should not be mutated",
  function(data) {
    return "Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? " + data;
  },
  "An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.",
  "Immer forbids circular references",
  "The first or second argument to `produce` must be a function",
  "The third argument to `produce` must be a function or undefined",
  "First argument to `createDraft` must be a plain object, an array, or an immerable object",
  "First argument to `finishDraft` must be a draft returned by `createDraft`",
  function(thing) {
    return `'current' expects a draft, got: ${thing}`;
  },
  "Object.defineProperty() cannot be used on an Immer draft",
  "Object.setPrototypeOf() cannot be used on an Immer draft",
  "Immer only supports deleting array indices",
  "Immer only supports setting array indices and the 'length' property",
  function(thing) {
    return `'original' expects a draft, got: ${thing}`;
  }
  // Note: if more errors are added, the errorOffset in Patches.ts should be increased
  // See Patches.ts for additional errors
] : 0;
function die(error, ...args) {
  if (true) {
    const e = errors[error];
    const msg = typeof e === "function" ? e.apply(null, args) : e;
    throw new Error(`[Immer] ${msg}`);
  }
  // removed by dead control flow
{}
}

// src/utils/common.ts
var getPrototypeOf = Object.getPrototypeOf;
function isDraft(value) {
  return !!value && !!value[DRAFT_STATE];
}
function isDraftable(value) {
  if (!value)
    return false;
  return isPlainObject(value) || Array.isArray(value) || !!value[DRAFTABLE] || !!value.constructor?.[DRAFTABLE] || isMap(value) || isSet(value);
}
var objectCtorString = Object.prototype.constructor.toString();
function isPlainObject(value) {
  if (!value || typeof value !== "object")
    return false;
  const proto = getPrototypeOf(value);
  if (proto === null) {
    return true;
  }
  const Ctor = Object.hasOwnProperty.call(proto, "constructor") && proto.constructor;
  if (Ctor === Object)
    return true;
  return typeof Ctor == "function" && Function.toString.call(Ctor) === objectCtorString;
}
function original(value) {
  if (!isDraft(value))
    die(15, value);
  return value[DRAFT_STATE].base_;
}
function each(obj, iter) {
  if (getArchtype(obj) === 0 /* Object */) {
    Reflect.ownKeys(obj).forEach((key) => {
      iter(key, obj[key], obj);
    });
  } else {
    obj.forEach((entry, index) => iter(index, entry, obj));
  }
}
function getArchtype(thing) {
  const state = thing[DRAFT_STATE];
  return state ? state.type_ : Array.isArray(thing) ? 1 /* Array */ : isMap(thing) ? 2 /* Map */ : isSet(thing) ? 3 /* Set */ : 0 /* Object */;
}
function has(thing, prop) {
  return getArchtype(thing) === 2 /* Map */ ? thing.has(prop) : Object.prototype.hasOwnProperty.call(thing, prop);
}
function get(thing, prop) {
  return getArchtype(thing) === 2 /* Map */ ? thing.get(prop) : thing[prop];
}
function set(thing, propOrOldValue, value) {
  const t = getArchtype(thing);
  if (t === 2 /* Map */)
    thing.set(propOrOldValue, value);
  else if (t === 3 /* Set */) {
    thing.add(value);
  } else
    thing[propOrOldValue] = value;
}
function is(x, y) {
  if (x === y) {
    return x !== 0 || 1 / x === 1 / y;
  } else {
    return x !== x && y !== y;
  }
}
function isMap(target) {
  return target instanceof Map;
}
function isSet(target) {
  return target instanceof Set;
}
function latest(state) {
  return state.copy_ || state.base_;
}
function shallowCopy(base, strict) {
  if (isMap(base)) {
    return new Map(base);
  }
  if (isSet(base)) {
    return new Set(base);
  }
  if (Array.isArray(base))
    return Array.prototype.slice.call(base);
  const isPlain = isPlainObject(base);
  if (strict === true || strict === "class_only" && !isPlain) {
    const descriptors = Object.getOwnPropertyDescriptors(base);
    delete descriptors[DRAFT_STATE];
    let keys = Reflect.ownKeys(descriptors);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const desc = descriptors[key];
      if (desc.writable === false) {
        desc.writable = true;
        desc.configurable = true;
      }
      if (desc.get || desc.set)
        descriptors[key] = {
          configurable: true,
          writable: true,
          // could live with !!desc.set as well here...
          enumerable: desc.enumerable,
          value: base[key]
        };
    }
    return Object.create(getPrototypeOf(base), descriptors);
  } else {
    const proto = getPrototypeOf(base);
    if (proto !== null && isPlain) {
      return { ...base };
    }
    const obj = Object.create(proto);
    return Object.assign(obj, base);
  }
}
function freeze(obj, deep = false) {
  if (isFrozen(obj) || isDraft(obj) || !isDraftable(obj))
    return obj;
  if (getArchtype(obj) > 1) {
    obj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections;
  }
  Object.freeze(obj);
  if (deep)
    Object.entries(obj).forEach(([key, value]) => freeze(value, true));
  return obj;
}
function dontMutateFrozenCollections() {
  die(2);
}
function isFrozen(obj) {
  return Object.isFrozen(obj);
}

// src/utils/plugins.ts
var plugins = {};
function getPlugin(pluginKey) {
  const plugin = plugins[pluginKey];
  if (!plugin) {
    die(0, pluginKey);
  }
  return plugin;
}
function loadPlugin(pluginKey, implementation) {
  if (!plugins[pluginKey])
    plugins[pluginKey] = implementation;
}

// src/core/scope.ts
var currentScope;
function getCurrentScope() {
  return currentScope;
}
function createScope(parent_, immer_) {
  return {
    drafts_: [],
    parent_,
    immer_,
    // Whenever the modified draft contains a draft from another scope, we
    // need to prevent auto-freezing so the unowned draft can be finalized.
    canAutoFreeze_: true,
    unfinalizedDrafts_: 0
  };
}
function usePatchesInScope(scope, patchListener) {
  if (patchListener) {
    getPlugin("Patches");
    scope.patches_ = [];
    scope.inversePatches_ = [];
    scope.patchListener_ = patchListener;
  }
}
function revokeScope(scope) {
  leaveScope(scope);
  scope.drafts_.forEach(revokeDraft);
  scope.drafts_ = null;
}
function leaveScope(scope) {
  if (scope === currentScope) {
    currentScope = scope.parent_;
  }
}
function enterScope(immer2) {
  return currentScope = createScope(currentScope, immer2);
}
function revokeDraft(draft) {
  const state = draft[DRAFT_STATE];
  if (state.type_ === 0 /* Object */ || state.type_ === 1 /* Array */)
    state.revoke_();
  else
    state.revoked_ = true;
}

// src/core/finalize.ts
function processResult(result, scope) {
  scope.unfinalizedDrafts_ = scope.drafts_.length;
  const baseDraft = scope.drafts_[0];
  const isReplaced = result !== void 0 && result !== baseDraft;
  if (isReplaced) {
    if (baseDraft[DRAFT_STATE].modified_) {
      revokeScope(scope);
      die(4);
    }
    if (isDraftable(result)) {
      result = finalize(scope, result);
      if (!scope.parent_)
        maybeFreeze(scope, result);
    }
    if (scope.patches_) {
      getPlugin("Patches").generateReplacementPatches_(
        baseDraft[DRAFT_STATE].base_,
        result,
        scope.patches_,
        scope.inversePatches_
      );
    }
  } else {
    result = finalize(scope, baseDraft, []);
  }
  revokeScope(scope);
  if (scope.patches_) {
    scope.patchListener_(scope.patches_, scope.inversePatches_);
  }
  return result !== NOTHING ? result : void 0;
}
function finalize(rootScope, value, path) {
  if (isFrozen(value))
    return value;
  const state = value[DRAFT_STATE];
  if (!state) {
    each(
      value,
      (key, childValue) => finalizeProperty(rootScope, state, value, key, childValue, path)
    );
    return value;
  }
  if (state.scope_ !== rootScope)
    return value;
  if (!state.modified_) {
    maybeFreeze(rootScope, state.base_, true);
    return state.base_;
  }
  if (!state.finalized_) {
    state.finalized_ = true;
    state.scope_.unfinalizedDrafts_--;
    const result = state.copy_;
    let resultEach = result;
    let isSet2 = false;
    if (state.type_ === 3 /* Set */) {
      resultEach = new Set(result);
      result.clear();
      isSet2 = true;
    }
    each(
      resultEach,
      (key, childValue) => finalizeProperty(rootScope, state, result, key, childValue, path, isSet2)
    );
    maybeFreeze(rootScope, result, false);
    if (path && rootScope.patches_) {
      getPlugin("Patches").generatePatches_(
        state,
        path,
        rootScope.patches_,
        rootScope.inversePatches_
      );
    }
  }
  return state.copy_;
}
function finalizeProperty(rootScope, parentState, targetObject, prop, childValue, rootPath, targetIsSet) {
  if ( true && childValue === targetObject)
    die(5);
  if (isDraft(childValue)) {
    const path = rootPath && parentState && parentState.type_ !== 3 /* Set */ && // Set objects are atomic since they have no keys.
    !has(parentState.assigned_, prop) ? rootPath.concat(prop) : void 0;
    const res = finalize(rootScope, childValue, path);
    set(targetObject, prop, res);
    if (isDraft(res)) {
      rootScope.canAutoFreeze_ = false;
    } else
      return;
  } else if (targetIsSet) {
    targetObject.add(childValue);
  }
  if (isDraftable(childValue) && !isFrozen(childValue)) {
    if (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {
      return;
    }
    finalize(rootScope, childValue);
    if ((!parentState || !parentState.scope_.parent_) && typeof prop !== "symbol" && Object.prototype.propertyIsEnumerable.call(targetObject, prop))
      maybeFreeze(rootScope, childValue);
  }
}
function maybeFreeze(scope, value, deep = false) {
  if (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {
    freeze(value, deep);
  }
}

// src/core/proxy.ts
function createProxyProxy(base, parent) {
  const isArray = Array.isArray(base);
  const state = {
    type_: isArray ? 1 /* Array */ : 0 /* Object */,
    // Track which produce call this is associated with.
    scope_: parent ? parent.scope_ : getCurrentScope(),
    // True for both shallow and deep changes.
    modified_: false,
    // Used during finalization.
    finalized_: false,
    // Track which properties have been assigned (true) or deleted (false).
    assigned_: {},
    // The parent draft state.
    parent_: parent,
    // The base state.
    base_: base,
    // The base proxy.
    draft_: null,
    // set below
    // The base copy with any updated values.
    copy_: null,
    // Called by the `produce` function.
    revoke_: null,
    isManual_: false
  };
  let target = state;
  let traps = objectTraps;
  if (isArray) {
    target = [state];
    traps = arrayTraps;
  }
  const { revoke, proxy } = Proxy.revocable(target, traps);
  state.draft_ = proxy;
  state.revoke_ = revoke;
  return proxy;
}
var objectTraps = {
  get(state, prop) {
    if (prop === DRAFT_STATE)
      return state;
    const source = latest(state);
    if (!has(source, prop)) {
      return readPropFromProto(state, source, prop);
    }
    const value = source[prop];
    if (state.finalized_ || !isDraftable(value)) {
      return value;
    }
    if (value === peek(state.base_, prop)) {
      prepareCopy(state);
      return state.copy_[prop] = createProxy(value, state);
    }
    return value;
  },
  has(state, prop) {
    return prop in latest(state);
  },
  ownKeys(state) {
    return Reflect.ownKeys(latest(state));
  },
  set(state, prop, value) {
    const desc = getDescriptorFromProto(latest(state), prop);
    if (desc?.set) {
      desc.set.call(state.draft_, value);
      return true;
    }
    if (!state.modified_) {
      const current2 = peek(latest(state), prop);
      const currentState = current2?.[DRAFT_STATE];
      if (currentState && currentState.base_ === value) {
        state.copy_[prop] = value;
        state.assigned_[prop] = false;
        return true;
      }
      if (is(value, current2) && (value !== void 0 || has(state.base_, prop)))
        return true;
      prepareCopy(state);
      markChanged(state);
    }
    if (state.copy_[prop] === value && // special case: handle new props with value 'undefined'
    (value !== void 0 || prop in state.copy_) || // special case: NaN
    Number.isNaN(value) && Number.isNaN(state.copy_[prop]))
      return true;
    state.copy_[prop] = value;
    state.assigned_[prop] = true;
    return true;
  },
  deleteProperty(state, prop) {
    if (peek(state.base_, prop) !== void 0 || prop in state.base_) {
      state.assigned_[prop] = false;
      prepareCopy(state);
      markChanged(state);
    } else {
      delete state.assigned_[prop];
    }
    if (state.copy_) {
      delete state.copy_[prop];
    }
    return true;
  },
  // Note: We never coerce `desc.value` into an Immer draft, because we can't make
  // the same guarantee in ES5 mode.
  getOwnPropertyDescriptor(state, prop) {
    const owner = latest(state);
    const desc = Reflect.getOwnPropertyDescriptor(owner, prop);
    if (!desc)
      return desc;
    return {
      writable: true,
      configurable: state.type_ !== 1 /* Array */ || prop !== "length",
      enumerable: desc.enumerable,
      value: owner[prop]
    };
  },
  defineProperty() {
    die(11);
  },
  getPrototypeOf(state) {
    return getPrototypeOf(state.base_);
  },
  setPrototypeOf() {
    die(12);
  }
};
var arrayTraps = {};
each(objectTraps, (key, fn) => {
  arrayTraps[key] = function() {
    arguments[0] = arguments[0][0];
    return fn.apply(this, arguments);
  };
});
arrayTraps.deleteProperty = function(state, prop) {
  if ( true && isNaN(parseInt(prop)))
    die(13);
  return arrayTraps.set.call(this, state, prop, void 0);
};
arrayTraps.set = function(state, prop, value) {
  if ( true && prop !== "length" && isNaN(parseInt(prop)))
    die(14);
  return objectTraps.set.call(this, state[0], prop, value, state[0]);
};
function peek(draft, prop) {
  const state = draft[DRAFT_STATE];
  const source = state ? latest(state) : draft;
  return source[prop];
}
function readPropFromProto(state, source, prop) {
  const desc = getDescriptorFromProto(source, prop);
  return desc ? `value` in desc ? desc.value : (
    // This is a very special case, if the prop is a getter defined by the
    // prototype, we should invoke it with the draft as context!
    desc.get?.call(state.draft_)
  ) : void 0;
}
function getDescriptorFromProto(source, prop) {
  if (!(prop in source))
    return void 0;
  let proto = getPrototypeOf(source);
  while (proto) {
    const desc = Object.getOwnPropertyDescriptor(proto, prop);
    if (desc)
      return desc;
    proto = getPrototypeOf(proto);
  }
  return void 0;
}
function markChanged(state) {
  if (!state.modified_) {
    state.modified_ = true;
    if (state.parent_) {
      markChanged(state.parent_);
    }
  }
}
function prepareCopy(state) {
  if (!state.copy_) {
    state.copy_ = shallowCopy(
      state.base_,
      state.scope_.immer_.useStrictShallowCopy_
    );
  }
}

// src/core/immerClass.ts
var Immer2 = class {
  constructor(config) {
    this.autoFreeze_ = true;
    this.useStrictShallowCopy_ = false;
    /**
     * The `produce` function takes a value and a "recipe function" (whose
     * return value often depends on the base state). The recipe function is
     * free to mutate its first argument however it wants. All mutations are
     * only ever applied to a __copy__ of the base state.
     *
     * Pass only a function to create a "curried producer" which relieves you
     * from passing the recipe function every time.
     *
     * Only plain objects and arrays are made mutable. All other objects are
     * considered uncopyable.
     *
     * Note: This function is __bound__ to its `Immer` instance.
     *
     * @param {any} base - the initial state
     * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified
     * @param {Function} patchListener - optional function that will be called with all the patches produced here
     * @returns {any} a new state, or the initial state if nothing was modified
     */
    this.produce = (base, recipe, patchListener) => {
      if (typeof base === "function" && typeof recipe !== "function") {
        const defaultBase = recipe;
        recipe = base;
        const self = this;
        return function curriedProduce(base2 = defaultBase, ...args) {
          return self.produce(base2, (draft) => recipe.call(this, draft, ...args));
        };
      }
      if (typeof recipe !== "function")
        die(6);
      if (patchListener !== void 0 && typeof patchListener !== "function")
        die(7);
      let result;
      if (isDraftable(base)) {
        const scope = enterScope(this);
        const proxy = createProxy(base, void 0);
        let hasError = true;
        try {
          result = recipe(proxy);
          hasError = false;
        } finally {
          if (hasError)
            revokeScope(scope);
          else
            leaveScope(scope);
        }
        usePatchesInScope(scope, patchListener);
        return processResult(result, scope);
      } else if (!base || typeof base !== "object") {
        result = recipe(base);
        if (result === void 0)
          result = base;
        if (result === NOTHING)
          result = void 0;
        if (this.autoFreeze_)
          freeze(result, true);
        if (patchListener) {
          const p = [];
          const ip = [];
          getPlugin("Patches").generateReplacementPatches_(base, result, p, ip);
          patchListener(p, ip);
        }
        return result;
      } else
        die(1, base);
    };
    this.produceWithPatches = (base, recipe) => {
      if (typeof base === "function") {
        return (state, ...args) => this.produceWithPatches(state, (draft) => base(draft, ...args));
      }
      let patches, inversePatches;
      const result = this.produce(base, recipe, (p, ip) => {
        patches = p;
        inversePatches = ip;
      });
      return [result, patches, inversePatches];
    };
    if (typeof config?.autoFreeze === "boolean")
      this.setAutoFreeze(config.autoFreeze);
    if (typeof config?.useStrictShallowCopy === "boolean")
      this.setUseStrictShallowCopy(config.useStrictShallowCopy);
  }
  createDraft(base) {
    if (!isDraftable(base))
      die(8);
    if (isDraft(base))
      base = current(base);
    const scope = enterScope(this);
    const proxy = createProxy(base, void 0);
    proxy[DRAFT_STATE].isManual_ = true;
    leaveScope(scope);
    return proxy;
  }
  finishDraft(draft, patchListener) {
    const state = draft && draft[DRAFT_STATE];
    if (!state || !state.isManual_)
      die(9);
    const { scope_: scope } = state;
    usePatchesInScope(scope, patchListener);
    return processResult(void 0, scope);
  }
  /**
   * Pass true to automatically freeze all copies created by Immer.
   *
   * By default, auto-freezing is enabled.
   */
  setAutoFreeze(value) {
    this.autoFreeze_ = value;
  }
  /**
   * Pass true to enable strict shallow copy.
   *
   * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.
   */
  setUseStrictShallowCopy(value) {
    this.useStrictShallowCopy_ = value;
  }
  applyPatches(base, patches) {
    let i;
    for (i = patches.length - 1; i >= 0; i--) {
      const patch = patches[i];
      if (patch.path.length === 0 && patch.op === "replace") {
        base = patch.value;
        break;
      }
    }
    if (i > -1) {
      patches = patches.slice(i + 1);
    }
    const applyPatchesImpl = getPlugin("Patches").applyPatches_;
    if (isDraft(base)) {
      return applyPatchesImpl(base, patches);
    }
    return this.produce(
      base,
      (draft) => applyPatchesImpl(draft, patches)
    );
  }
};
function createProxy(value, parent) {
  const draft = isMap(value) ? getPlugin("MapSet").proxyMap_(value, parent) : isSet(value) ? getPlugin("MapSet").proxySet_(value, parent) : createProxyProxy(value, parent);
  const scope = parent ? parent.scope_ : getCurrentScope();
  scope.drafts_.push(draft);
  return draft;
}

// src/core/current.ts
function current(value) {
  if (!isDraft(value))
    die(10, value);
  return currentImpl(value);
}
function currentImpl(value) {
  if (!isDraftable(value) || isFrozen(value))
    return value;
  const state = value[DRAFT_STATE];
  let copy;
  if (state) {
    if (!state.modified_)
      return state.base_;
    state.finalized_ = true;
    copy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_);
  } else {
    copy = shallowCopy(value, true);
  }
  each(copy, (key, childValue) => {
    set(copy, key, currentImpl(childValue));
  });
  if (state) {
    state.finalized_ = false;
  }
  return copy;
}

// src/plugins/patches.ts
function enablePatches() {
  const errorOffset = 16;
  if (true) {
    errors.push(
      'Sets cannot have "replace" patches.',
      function(op) {
        return "Unsupported patch operation: " + op;
      },
      function(path) {
        return "Cannot apply patch, path doesn't resolve: " + path;
      },
      "Patching reserved attributes like __proto__, prototype and constructor is not allowed"
    );
  }
  const REPLACE = "replace";
  const ADD = "add";
  const REMOVE = "remove";
  function generatePatches_(state, basePath, patches, inversePatches) {
    switch (state.type_) {
      case 0 /* Object */:
      case 2 /* Map */:
        return generatePatchesFromAssigned(
          state,
          basePath,
          patches,
          inversePatches
        );
      case 1 /* Array */:
        return generateArrayPatches(state, basePath, patches, inversePatches);
      case 3 /* Set */:
        return generateSetPatches(
          state,
          basePath,
          patches,
          inversePatches
        );
    }
  }
  function generateArrayPatches(state, basePath, patches, inversePatches) {
    let { base_, assigned_ } = state;
    let copy_ = state.copy_;
    if (copy_.length < base_.length) {
      ;
      [base_, copy_] = [copy_, base_];
      [patches, inversePatches] = [inversePatches, patches];
    }
    for (let i = 0; i < base_.length; i++) {
      if (assigned_[i] && copy_[i] !== base_[i]) {
        const path = basePath.concat([i]);
        patches.push({
          op: REPLACE,
          path,
          // Need to maybe clone it, as it can in fact be the original value
          // due to the base/copy inversion at the start of this function
          value: clonePatchValueIfNeeded(copy_[i])
        });
        inversePatches.push({
          op: REPLACE,
          path,
          value: clonePatchValueIfNeeded(base_[i])
        });
      }
    }
    for (let i = base_.length; i < copy_.length; i++) {
      const path = basePath.concat([i]);
      patches.push({
        op: ADD,
        path,
        // Need to maybe clone it, as it can in fact be the original value
        // due to the base/copy inversion at the start of this function
        value: clonePatchValueIfNeeded(copy_[i])
      });
    }
    for (let i = copy_.length - 1; base_.length <= i; --i) {
      const path = basePath.concat([i]);
      inversePatches.push({
        op: REMOVE,
        path
      });
    }
  }
  function generatePatchesFromAssigned(state, basePath, patches, inversePatches) {
    const { base_, copy_ } = state;
    each(state.assigned_, (key, assignedValue) => {
      const origValue = get(base_, key);
      const value = get(copy_, key);
      const op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD;
      if (origValue === value && op === REPLACE)
        return;
      const path = basePath.concat(key);
      patches.push(op === REMOVE ? { op, path } : { op, path, value });
      inversePatches.push(
        op === ADD ? { op: REMOVE, path } : op === REMOVE ? { op: ADD, path, value: clonePatchValueIfNeeded(origValue) } : { op: REPLACE, path, value: clonePatchValueIfNeeded(origValue) }
      );
    });
  }
  function generateSetPatches(state, basePath, patches, inversePatches) {
    let { base_, copy_ } = state;
    let i = 0;
    base_.forEach((value) => {
      if (!copy_.has(value)) {
        const path = basePath.concat([i]);
        patches.push({
          op: REMOVE,
          path,
          value
        });
        inversePatches.unshift({
          op: ADD,
          path,
          value
        });
      }
      i++;
    });
    i = 0;
    copy_.forEach((value) => {
      if (!base_.has(value)) {
        const path = basePath.concat([i]);
        patches.push({
          op: ADD,
          path,
          value
        });
        inversePatches.unshift({
          op: REMOVE,
          path,
          value
        });
      }
      i++;
    });
  }
  function generateReplacementPatches_(baseValue, replacement, patches, inversePatches) {
    patches.push({
      op: REPLACE,
      path: [],
      value: replacement === NOTHING ? void 0 : replacement
    });
    inversePatches.push({
      op: REPLACE,
      path: [],
      value: baseValue
    });
  }
  function applyPatches_(draft, patches) {
    patches.forEach((patch) => {
      const { path, op } = patch;
      let base = draft;
      for (let i = 0; i < path.length - 1; i++) {
        const parentType = getArchtype(base);
        let p = path[i];
        if (typeof p !== "string" && typeof p !== "number") {
          p = "" + p;
        }
        if ((parentType === 0 /* Object */ || parentType === 1 /* Array */) && (p === "__proto__" || p === "constructor"))
          die(errorOffset + 3);
        if (typeof base === "function" && p === "prototype")
          die(errorOffset + 3);
        base = get(base, p);
        if (typeof base !== "object")
          die(errorOffset + 2, path.join("/"));
      }
      const type = getArchtype(base);
      const value = deepClonePatchValue(patch.value);
      const key = path[path.length - 1];
      switch (op) {
        case REPLACE:
          switch (type) {
            case 2 /* Map */:
              return base.set(key, value);
            case 3 /* Set */:
              die(errorOffset);
            default:
              return base[key] = value;
          }
        case ADD:
          switch (type) {
            case 1 /* Array */:
              return key === "-" ? base.push(value) : base.splice(key, 0, value);
            case 2 /* Map */:
              return base.set(key, value);
            case 3 /* Set */:
              return base.add(value);
            default:
              return base[key] = value;
          }
        case REMOVE:
          switch (type) {
            case 1 /* Array */:
              return base.splice(key, 1);
            case 2 /* Map */:
              return base.delete(key);
            case 3 /* Set */:
              return base.delete(patch.value);
            default:
              return delete base[key];
          }
        default:
          die(errorOffset + 1, op);
      }
    });
    return draft;
  }
  function deepClonePatchValue(obj) {
    if (!isDraftable(obj))
      return obj;
    if (Array.isArray(obj))
      return obj.map(deepClonePatchValue);
    if (isMap(obj))
      return new Map(
        Array.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])
      );
    if (isSet(obj))
      return new Set(Array.from(obj).map(deepClonePatchValue));
    const cloned = Object.create(getPrototypeOf(obj));
    for (const key in obj)
      cloned[key] = deepClonePatchValue(obj[key]);
    if (has(obj, DRAFTABLE))
      cloned[DRAFTABLE] = obj[DRAFTABLE];
    return cloned;
  }
  function clonePatchValueIfNeeded(obj) {
    if (isDraft(obj)) {
      return deepClonePatchValue(obj);
    } else
      return obj;
  }
  loadPlugin("Patches", {
    applyPatches_,
    generatePatches_,
    generateReplacementPatches_
  });
}

// src/plugins/mapset.ts
function enableMapSet() {
  class DraftMap extends Map {
    constructor(target, parent) {
      super();
      this[DRAFT_STATE] = {
        type_: 2 /* Map */,
        parent_: parent,
        scope_: parent ? parent.scope_ : getCurrentScope(),
        modified_: false,
        finalized_: false,
        copy_: void 0,
        assigned_: void 0,
        base_: target,
        draft_: this,
        isManual_: false,
        revoked_: false
      };
    }
    get size() {
      return latest(this[DRAFT_STATE]).size;
    }
    has(key) {
      return latest(this[DRAFT_STATE]).has(key);
    }
    set(key, value) {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      if (!latest(state).has(key) || latest(state).get(key) !== value) {
        prepareMapCopy(state);
        markChanged(state);
        state.assigned_.set(key, true);
        state.copy_.set(key, value);
        state.assigned_.set(key, true);
      }
      return this;
    }
    delete(key) {
      if (!this.has(key)) {
        return false;
      }
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      prepareMapCopy(state);
      markChanged(state);
      if (state.base_.has(key)) {
        state.assigned_.set(key, false);
      } else {
        state.assigned_.delete(key);
      }
      state.copy_.delete(key);
      return true;
    }
    clear() {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      if (latest(state).size) {
        prepareMapCopy(state);
        markChanged(state);
        state.assigned_ = /* @__PURE__ */ new Map();
        each(state.base_, (key) => {
          state.assigned_.set(key, false);
        });
        state.copy_.clear();
      }
    }
    forEach(cb, thisArg) {
      const state = this[DRAFT_STATE];
      latest(state).forEach((_value, key, _map) => {
        cb.call(thisArg, this.get(key), key, this);
      });
    }
    get(key) {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      const value = latest(state).get(key);
      if (state.finalized_ || !isDraftable(value)) {
        return value;
      }
      if (value !== state.base_.get(key)) {
        return value;
      }
      const draft = createProxy(value, state);
      prepareMapCopy(state);
      state.copy_.set(key, draft);
      return draft;
    }
    keys() {
      return latest(this[DRAFT_STATE]).keys();
    }
    values() {
      const iterator = this.keys();
      return {
        [Symbol.iterator]: () => this.values(),
        next: () => {
          const r = iterator.next();
          if (r.done)
            return r;
          const value = this.get(r.value);
          return {
            done: false,
            value
          };
        }
      };
    }
    entries() {
      const iterator = this.keys();
      return {
        [Symbol.iterator]: () => this.entries(),
        next: () => {
          const r = iterator.next();
          if (r.done)
            return r;
          const value = this.get(r.value);
          return {
            done: false,
            value: [r.value, value]
          };
        }
      };
    }
    [(DRAFT_STATE, Symbol.iterator)]() {
      return this.entries();
    }
  }
  function proxyMap_(target, parent) {
    return new DraftMap(target, parent);
  }
  function prepareMapCopy(state) {
    if (!state.copy_) {
      state.assigned_ = /* @__PURE__ */ new Map();
      state.copy_ = new Map(state.base_);
    }
  }
  class DraftSet extends Set {
    constructor(target, parent) {
      super();
      this[DRAFT_STATE] = {
        type_: 3 /* Set */,
        parent_: parent,
        scope_: parent ? parent.scope_ : getCurrentScope(),
        modified_: false,
        finalized_: false,
        copy_: void 0,
        base_: target,
        draft_: this,
        drafts_: /* @__PURE__ */ new Map(),
        revoked_: false,
        isManual_: false
      };
    }
    get size() {
      return latest(this[DRAFT_STATE]).size;
    }
    has(value) {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      if (!state.copy_) {
        return state.base_.has(value);
      }
      if (state.copy_.has(value))
        return true;
      if (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))
        return true;
      return false;
    }
    add(value) {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      if (!this.has(value)) {
        prepareSetCopy(state);
        markChanged(state);
        state.copy_.add(value);
      }
      return this;
    }
    delete(value) {
      if (!this.has(value)) {
        return false;
      }
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      prepareSetCopy(state);
      markChanged(state);
      return state.copy_.delete(value) || (state.drafts_.has(value) ? state.copy_.delete(state.drafts_.get(value)) : (
        /* istanbul ignore next */
        false
      ));
    }
    clear() {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      if (latest(state).size) {
        prepareSetCopy(state);
        markChanged(state);
        state.copy_.clear();
      }
    }
    values() {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      prepareSetCopy(state);
      return state.copy_.values();
    }
    entries() {
      const state = this[DRAFT_STATE];
      assertUnrevoked(state);
      prepareSetCopy(state);
      return state.copy_.entries();
    }
    keys() {
      return this.values();
    }
    [(DRAFT_STATE, Symbol.iterator)]() {
      return this.values();
    }
    forEach(cb, thisArg) {
      const iterator = this.values();
      let result = iterator.next();
      while (!result.done) {
        cb.call(thisArg, result.value, result.value, this);
        result = iterator.next();
      }
    }
  }
  function proxySet_(target, parent) {
    return new DraftSet(target, parent);
  }
  function prepareSetCopy(state) {
    if (!state.copy_) {
      state.copy_ = /* @__PURE__ */ new Set();
      state.base_.forEach((value) => {
        if (isDraftable(value)) {
          const draft = createProxy(value, state);
          state.drafts_.set(value, draft);
          state.copy_.add(draft);
        } else {
          state.copy_.add(value);
        }
      });
    }
  }
  function assertUnrevoked(state) {
    if (state.revoked_)
      die(3, JSON.stringify(latest(state)));
  }
  loadPlugin("MapSet", { proxyMap_, proxySet_ });
}

// src/immer.ts
var immer = new Immer2();
var produce = immer.produce;
var produceWithPatches = immer.produceWithPatches.bind(
  immer
);
var setAutoFreeze = immer.setAutoFreeze.bind(immer);
var setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer);
var applyPatches = immer.applyPatches.bind(immer);
var createDraft = immer.createDraft.bind(immer);
var finishDraft = immer.finishDraft.bind(immer);
function castDraft(value) {
  return value;
}
function castImmutable(value) {
  return value;
}

//# sourceMappingURL=immer.mjs.map

/***/ }),

/***/ "./node_modules/lodash.assignin/index.js":
/*!***********************************************!*\
  !*** ./node_modules/lodash.assignin/index.js ***!
  \***********************************************/
/***/ ((module) => {

/**
 * lodash (Custom Build) <https://lodash.com/>
 * Build: `lodash modularize exports="npm" -o ./`
 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */

/** Used as references for various `Number` constants. */
var MAX_SAFE_INTEGER = 9007199254740991;

/** `Object#toString` result references. */
var argsTag = '[object Arguments]',
    funcTag = '[object Function]',
    genTag = '[object GeneratorFunction]';

/** Used to detect unsigned integer values. */
var reIsUint = /^(?:0|[1-9]\d*)$/;

/**
 * A faster alternative to `Function#apply`, this function invokes `func`
 * with the `this` binding of `thisArg` and the arguments of `args`.
 *
 * @private
 * @param {Function} func The function to invoke.
 * @param {*} thisArg The `this` binding of `func`.
 * @param {Array} args The arguments to invoke `func` with.
 * @returns {*} Returns the result of `func`.
 */
function apply(func, thisArg, args) {
  switch (args.length) {
    case 0: return func.call(thisArg);
    case 1: return func.call(thisArg, args[0]);
    case 2: return func.call(thisArg, args[0], args[1]);
    case 3: return func.call(thisArg, args[0], args[1], args[2]);
  }
  return func.apply(thisArg, args);
}

/**
 * The base implementation of `_.times` without support for iteratee shorthands
 * or max array length checks.
 *
 * @private
 * @param {number} n The number of times to invoke `iteratee`.
 * @param {Function} iteratee The function invoked per iteration.
 * @returns {Array} Returns the array of results.
 */
function baseTimes(n, iteratee) {
  var index = -1,
      result = Array(n);

  while (++index < n) {
    result[index] = iteratee(index);
  }
  return result;
}

/** Used for built-in method references. */
var objectProto = Object.prototype;

/** Used to check objects for own properties. */
var hasOwnProperty = objectProto.hasOwnProperty;

/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */
var objectToString = objectProto.toString;

/** Built-in value references. */
var propertyIsEnumerable = objectProto.propertyIsEnumerable;

/* Built-in method references for those with the same name as other `lodash` methods. */
var nativeMax = Math.max;

/**
 * Creates an array of the enumerable property names of the array-like `value`.
 *
 * @private
 * @param {*} value The value to query.
 * @param {boolean} inherited Specify returning inherited property names.
 * @returns {Array} Returns the array of property names.
 */
function arrayLikeKeys(value, inherited) {
  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.
  // Safari 9 makes `arguments.length` enumerable in strict mode.
  var result = (isArray(value) || isArguments(value))
    ? baseTimes(value.length, String)
    : [];

  var length = result.length,
      skipIndexes = !!length;

  for (var key in value) {
    if ((inherited || hasOwnProperty.call(value, key)) &&
        !(skipIndexes && (key == 'length' || isIndex(key, length)))) {
      result.push(key);
    }
  }
  return result;
}

/**
 * Assigns `value` to `key` of `object` if the existing value is not equivalent
 * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * for equality comparisons.
 *
 * @private
 * @param {Object} object The object to modify.
 * @param {string} key The key of the property to assign.
 * @param {*} value The value to assign.
 */
function assignValue(object, key, value) {
  var objValue = object[key];
  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||
      (value === undefined && !(key in object))) {
    object[key] = value;
  }
}

/**
 * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.
 *
 * @private
 * @param {Object} object The object to query.
 * @returns {Array} Returns the array of property names.
 */
function baseKeysIn(object) {
  if (!isObject(object)) {
    return nativeKeysIn(object);
  }
  var isProto = isPrototype(object),
      result = [];

  for (var key in object) {
    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {
      result.push(key);
    }
  }
  return result;
}

/**
 * The base implementation of `_.rest` which doesn't validate or coerce arguments.
 *
 * @private
 * @param {Function} func The function to apply a rest parameter to.
 * @param {number} [start=func.length-1] The start position of the rest parameter.
 * @returns {Function} Returns the new function.
 */
function baseRest(func, start) {
  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);
  return function() {
    var args = arguments,
        index = -1,
        length = nativeMax(args.length - start, 0),
        array = Array(length);

    while (++index < length) {
      array[index] = args[start + index];
    }
    index = -1;
    var otherArgs = Array(start + 1);
    while (++index < start) {
      otherArgs[index] = args[index];
    }
    otherArgs[start] = array;
    return apply(func, this, otherArgs);
  };
}

/**
 * Copies properties of `source` to `object`.
 *
 * @private
 * @param {Object} source The object to copy properties from.
 * @param {Array} props The property identifiers to copy.
 * @param {Object} [object={}] The object to copy properties to.
 * @param {Function} [customizer] The function to customize copied values.
 * @returns {Object} Returns `object`.
 */
function copyObject(source, props, object, customizer) {
  object || (object = {});

  var index = -1,
      length = props.length;

  while (++index < length) {
    var key = props[index];

    var newValue = customizer
      ? customizer(object[key], source[key], key, object, source)
      : undefined;

    assignValue(object, key, newValue === undefined ? source[key] : newValue);
  }
  return object;
}

/**
 * Creates a function like `_.assign`.
 *
 * @private
 * @param {Function} assigner The function to assign values.
 * @returns {Function} Returns the new assigner function.
 */
function createAssigner(assigner) {
  return baseRest(function(object, sources) {
    var index = -1,
        length = sources.length,
        customizer = length > 1 ? sources[length - 1] : undefined,
        guard = length > 2 ? sources[2] : undefined;

    customizer = (assigner.length > 3 && typeof customizer == 'function')
      ? (length--, customizer)
      : undefined;

    if (guard && isIterateeCall(sources[0], sources[1], guard)) {
      customizer = length < 3 ? undefined : customizer;
      length = 1;
    }
    object = Object(object);
    while (++index < length) {
      var source = sources[index];
      if (source) {
        assigner(object, source, index, customizer);
      }
    }
    return object;
  });
}

/**
 * Checks if `value` is a valid array-like index.
 *
 * @private
 * @param {*} value The value to check.
 * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.
 * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.
 */
function isIndex(value, length) {
  length = length == null ? MAX_SAFE_INTEGER : length;
  return !!length &&
    (typeof value == 'number' || reIsUint.test(value)) &&
    (value > -1 && value % 1 == 0 && value < length);
}

/**
 * Checks if the given arguments are from an iteratee call.
 *
 * @private
 * @param {*} value The potential iteratee value argument.
 * @param {*} index The potential iteratee index or key argument.
 * @param {*} object The potential iteratee object argument.
 * @returns {boolean} Returns `true` if the arguments are from an iteratee call,
 *  else `false`.
 */
function isIterateeCall(value, index, object) {
  if (!isObject(object)) {
    return false;
  }
  var type = typeof index;
  if (type == 'number'
        ? (isArrayLike(object) && isIndex(index, object.length))
        : (type == 'string' && index in object)
      ) {
    return eq(object[index], value);
  }
  return false;
}

/**
 * Checks if `value` is likely a prototype object.
 *
 * @private
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.
 */
function isPrototype(value) {
  var Ctor = value && value.constructor,
      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;

  return value === proto;
}

/**
 * This function is like
 * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)
 * except that it includes inherited enumerable properties.
 *
 * @private
 * @param {Object} object The object to query.
 * @returns {Array} Returns the array of property names.
 */
function nativeKeysIn(object) {
  var result = [];
  if (object != null) {
    for (var key in Object(object)) {
      result.push(key);
    }
  }
  return result;
}

/**
 * Performs a
 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
 * comparison between two values to determine if they are equivalent.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to compare.
 * @param {*} other The other value to compare.
 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
 * @example
 *
 * var object = { 'a': 1 };
 * var other = { 'a': 1 };
 *
 * _.eq(object, object);
 * // => true
 *
 * _.eq(object, other);
 * // => false
 *
 * _.eq('a', 'a');
 * // => true
 *
 * _.eq('a', Object('a'));
 * // => false
 *
 * _.eq(NaN, NaN);
 * // => true
 */
function eq(value, other) {
  return value === other || (value !== value && other !== other);
}

/**
 * Checks if `value` is likely an `arguments` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an `arguments` object,
 *  else `false`.
 * @example
 *
 * _.isArguments(function() { return arguments; }());
 * // => true
 *
 * _.isArguments([1, 2, 3]);
 * // => false
 */
function isArguments(value) {
  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.
  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') &&
    (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);
}

/**
 * Checks if `value` is classified as an `Array` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array, else `false`.
 * @example
 *
 * _.isArray([1, 2, 3]);
 * // => true
 *
 * _.isArray(document.body.children);
 * // => false
 *
 * _.isArray('abc');
 * // => false
 *
 * _.isArray(_.noop);
 * // => false
 */
var isArray = Array.isArray;

/**
 * Checks if `value` is array-like. A value is considered array-like if it's
 * not a function and has a `value.length` that's an integer greater than or
 * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is array-like, else `false`.
 * @example
 *
 * _.isArrayLike([1, 2, 3]);
 * // => true
 *
 * _.isArrayLike(document.body.children);
 * // => true
 *
 * _.isArrayLike('abc');
 * // => true
 *
 * _.isArrayLike(_.noop);
 * // => false
 */
function isArrayLike(value) {
  return value != null && isLength(value.length) && !isFunction(value);
}

/**
 * This method is like `_.isArrayLike` except that it also checks if `value`
 * is an object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an array-like object,
 *  else `false`.
 * @example
 *
 * _.isArrayLikeObject([1, 2, 3]);
 * // => true
 *
 * _.isArrayLikeObject(document.body.children);
 * // => true
 *
 * _.isArrayLikeObject('abc');
 * // => false
 *
 * _.isArrayLikeObject(_.noop);
 * // => false
 */
function isArrayLikeObject(value) {
  return isObjectLike(value) && isArrayLike(value);
}

/**
 * Checks if `value` is classified as a `Function` object.
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a function, else `false`.
 * @example
 *
 * _.isFunction(_);
 * // => true
 *
 * _.isFunction(/abc/);
 * // => false
 */
function isFunction(value) {
  // The use of `Object#toString` avoids issues with the `typeof` operator
  // in Safari 8-9 which returns 'object' for typed array and other constructors.
  var tag = isObject(value) ? objectToString.call(value) : '';
  return tag == funcTag || tag == genTag;
}

/**
 * Checks if `value` is a valid array-like length.
 *
 * **Note:** This method is loosely based on
 * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.
 * @example
 *
 * _.isLength(3);
 * // => true
 *
 * _.isLength(Number.MIN_VALUE);
 * // => false
 *
 * _.isLength(Infinity);
 * // => false
 *
 * _.isLength('3');
 * // => false
 */
function isLength(value) {
  return typeof value == 'number' &&
    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;
}

/**
 * Checks if `value` is the
 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
 *
 * @static
 * @memberOf _
 * @since 0.1.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
 * @example
 *
 * _.isObject({});
 * // => true
 *
 * _.isObject([1, 2, 3]);
 * // => true
 *
 * _.isObject(_.noop);
 * // => true
 *
 * _.isObject(null);
 * // => false
 */
function isObject(value) {
  var type = typeof value;
  return !!value && (type == 'object' || type == 'function');
}

/**
 * Checks if `value` is object-like. A value is object-like if it's not `null`
 * and has a `typeof` result of "object".
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
 * @example
 *
 * _.isObjectLike({});
 * // => true
 *
 * _.isObjectLike([1, 2, 3]);
 * // => true
 *
 * _.isObjectLike(_.noop);
 * // => false
 *
 * _.isObjectLike(null);
 * // => false
 */
function isObjectLike(value) {
  return !!value && typeof value == 'object';
}

/**
 * This method is like `_.assign` except that it iterates over own and
 * inherited source properties.
 *
 * **Note:** This method mutates `object`.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @alias extend
 * @category Object
 * @param {Object} object The destination object.
 * @param {...Object} [sources] The source objects.
 * @returns {Object} Returns `object`.
 * @see _.assign
 * @example
 *
 * function Foo() {
 *   this.a = 1;
 * }
 *
 * function Bar() {
 *   this.c = 3;
 * }
 *
 * Foo.prototype.b = 2;
 * Bar.prototype.d = 4;
 *
 * _.assignIn({ 'a': 0 }, new Foo, new Bar);
 * // => { 'a': 1, 'b': 2, 'c': 3, 'd': 4 }
 */
var assignIn = createAssigner(function(object, source) {
  copyObject(source, keysIn(source), object);
});

/**
 * Creates an array of the own and inherited enumerable property names of `object`.
 *
 * **Note:** Non-object values are coerced to objects.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category Object
 * @param {Object} object The object to query.
 * @returns {Array} Returns the array of property names.
 * @example
 *
 * function Foo() {
 *   this.a = 1;
 *   this.b = 2;
 * }
 *
 * Foo.prototype.c = 3;
 *
 * _.keysIn(new Foo);
 * // => ['a', 'b', 'c'] (iteration order is not guaranteed)
 */
function keysIn(object) {
  return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);
}

module.exports = assignIn;


/***/ }),

/***/ "./node_modules/redux-logger/dist/redux-logger.js":
/*!********************************************************!*\
  !*** ./node_modules/redux-logger/dist/redux-logger.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

!function(e,t){ true?t(exports):0}(this,function(e){"use strict";function t(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function r(e,t){Object.defineProperty(this,"kind",{value:e,enumerable:!0}),t&&t.length&&Object.defineProperty(this,"path",{value:t,enumerable:!0})}function n(e,t,r){n.super_.call(this,"E",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0}),Object.defineProperty(this,"rhs",{value:r,enumerable:!0})}function o(e,t){o.super_.call(this,"N",e),Object.defineProperty(this,"rhs",{value:t,enumerable:!0})}function i(e,t){i.super_.call(this,"D",e),Object.defineProperty(this,"lhs",{value:t,enumerable:!0})}function a(e,t,r){a.super_.call(this,"A",e),Object.defineProperty(this,"index",{value:t,enumerable:!0}),Object.defineProperty(this,"item",{value:r,enumerable:!0})}function f(e,t,r){var n=e.slice((r||t)+1||e.length);return e.length=t<0?e.length+t:t,e.push.apply(e,n),e}function u(e){var t="undefined"==typeof e?"undefined":N(e);return"object"!==t?t:e===Math?"math":null===e?"null":Array.isArray(e)?"array":"[object Date]"===Object.prototype.toString.call(e)?"date":"function"==typeof e.toString&&/^\/.*\//.test(e.toString())?"regexp":"object"}function l(e,t,r,c,s,d,p){s=s||[],p=p||[];var g=s.slice(0);if("undefined"!=typeof d){if(c){if("function"==typeof c&&c(g,d))return;if("object"===("undefined"==typeof c?"undefined":N(c))){if(c.prefilter&&c.prefilter(g,d))return;if(c.normalize){var h=c.normalize(g,d,e,t);h&&(e=h[0],t=h[1])}}}g.push(d)}"regexp"===u(e)&&"regexp"===u(t)&&(e=e.toString(),t=t.toString());var y="undefined"==typeof e?"undefined":N(e),v="undefined"==typeof t?"undefined":N(t),b="undefined"!==y||p&&p[p.length-1].lhs&&p[p.length-1].lhs.hasOwnProperty(d),m="undefined"!==v||p&&p[p.length-1].rhs&&p[p.length-1].rhs.hasOwnProperty(d);if(!b&&m)r(new o(g,t));else if(!m&&b)r(new i(g,e));else if(u(e)!==u(t))r(new n(g,e,t));else if("date"===u(e)&&e-t!==0)r(new n(g,e,t));else if("object"===y&&null!==e&&null!==t)if(p.filter(function(t){return t.lhs===e}).length)e!==t&&r(new n(g,e,t));else{if(p.push({lhs:e,rhs:t}),Array.isArray(e)){var w;e.length;for(w=0;w<e.length;w++)w>=t.length?r(new a(g,w,new i(void 0,e[w]))):l(e[w],t[w],r,c,g,w,p);for(;w<t.length;)r(new a(g,w,new o(void 0,t[w++])))}else{var x=Object.keys(e),S=Object.keys(t);x.forEach(function(n,o){var i=S.indexOf(n);i>=0?(l(e[n],t[n],r,c,g,n,p),S=f(S,i)):l(e[n],void 0,r,c,g,n,p)}),S.forEach(function(e){l(void 0,t[e],r,c,g,e,p)})}p.length=p.length-1}else e!==t&&("number"===y&&isNaN(e)&&isNaN(t)||r(new n(g,e,t)))}function c(e,t,r,n){return n=n||[],l(e,t,function(e){e&&n.push(e)},r),n.length?n:void 0}function s(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":s(o[r.path[n]],r.index,r.item);break;case"D":delete o[r.path[n]];break;case"E":case"N":o[r.path[n]]=r.rhs}}else switch(r.kind){case"A":s(e[t],r.index,r.item);break;case"D":e=f(e,t);break;case"E":case"N":e[t]=r.rhs}return e}function d(e,t,r){if(e&&t&&r&&r.kind){for(var n=e,o=-1,i=r.path?r.path.length-1:0;++o<i;)"undefined"==typeof n[r.path[o]]&&(n[r.path[o]]="number"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case"A":s(r.path?n[r.path[o]]:n,r.index,r.item);break;case"D":delete n[r.path[o]];break;case"E":case"N":n[r.path[o]]=r.rhs}}}function p(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case"A":p(o[r.path[n]],r.index,r.item);break;case"D":o[r.path[n]]=r.lhs;break;case"E":o[r.path[n]]=r.lhs;break;case"N":delete o[r.path[n]]}}else switch(r.kind){case"A":p(e[t],r.index,r.item);break;case"D":e[t]=r.lhs;break;case"E":e[t]=r.lhs;break;case"N":e=f(e,t)}return e}function g(e,t,r){if(e&&t&&r&&r.kind){var n,o,i=e;for(o=r.path.length-1,n=0;n<o;n++)"undefined"==typeof i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case"A":p(i[r.path[n]],r.index,r.item);break;case"D":i[r.path[n]]=r.lhs;break;case"E":i[r.path[n]]=r.lhs;break;case"N":delete i[r.path[n]]}}}function h(e,t,r){if(e&&t){var n=function(n){r&&!r(e,t,n)||d(e,t,n)};l(e,t,n)}}function y(e){return"color: "+F[e].color+"; font-weight: bold"}function v(e){var t=e.kind,r=e.path,n=e.lhs,o=e.rhs,i=e.index,a=e.item;switch(t){case"E":return[r.join("."),n,"→",o];case"N":return[r.join("."),o];case"D":return[r.join(".")];case"A":return[r.join(".")+"["+i+"]",a];default:return[]}}function b(e,t,r,n){var o=c(e,t);try{n?r.groupCollapsed("diff"):r.group("diff")}catch(e){r.log("diff")}o?o.forEach(function(e){var t=e.kind,n=v(e);r.log.apply(r,["%c "+F[t].text,y(t)].concat(P(n)))}):r.log("—— no diff ——");try{r.groupEnd()}catch(e){r.log("—— diff end —— ")}}function m(e,t,r,n){switch("undefined"==typeof e?"undefined":N(e)){case"object":return"function"==typeof e[n]?e[n].apply(e,P(r)):e[n];case"function":return e(t);default:return e}}function w(e){var t=e.timestamp,r=e.duration;return function(e,n,o){var i=["action"];return i.push("%c"+String(e.type)),t&&i.push("%c@ "+n),r&&i.push("%c(in "+o.toFixed(2)+" ms)"),i.join(" ")}}function x(e,t){var r=t.logger,n=t.actionTransformer,o=t.titleFormatter,i=void 0===o?w(t):o,a=t.collapsed,f=t.colors,u=t.level,l=t.diff,c="undefined"==typeof t.titleFormatter;e.forEach(function(o,s){var d=o.started,p=o.startedTime,g=o.action,h=o.prevState,y=o.error,v=o.took,w=o.nextState,x=e[s+1];x&&(w=x.prevState,v=x.started-d);var S=n(g),k="function"==typeof a?a(function(){return w},g,o):a,j=D(p),E=f.title?"color: "+f.title(S)+";":"",A=["color: gray; font-weight: lighter;"];A.push(E),t.timestamp&&A.push("color: gray; font-weight: lighter;"),t.duration&&A.push("color: gray; font-weight: lighter;");var O=i(S,j,v);try{k?f.title&&c?r.groupCollapsed.apply(r,["%c "+O].concat(A)):r.groupCollapsed(O):f.title&&c?r.group.apply(r,["%c "+O].concat(A)):r.group(O)}catch(e){r.log(O)}var N=m(u,S,[h],"prevState"),P=m(u,S,[S],"action"),C=m(u,S,[y,h],"error"),F=m(u,S,[w],"nextState");if(N)if(f.prevState){var L="color: "+f.prevState(h)+"; font-weight: bold";r[N]("%c prev state",L,h)}else r[N]("prev state",h);if(P)if(f.action){var T="color: "+f.action(S)+"; font-weight: bold";r[P]("%c action    ",T,S)}else r[P]("action    ",S);if(y&&C)if(f.error){var M="color: "+f.error(y,h)+"; font-weight: bold;";r[C]("%c error     ",M,y)}else r[C]("error     ",y);if(F)if(f.nextState){var _="color: "+f.nextState(w)+"; font-weight: bold";r[F]("%c next state",_,w)}else r[F]("next state",w);l&&b(h,w,r,k);try{r.groupEnd()}catch(e){r.log("—— log end ——")}})}function S(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},L,e),r=t.logger,n=t.stateTransformer,o=t.errorTransformer,i=t.predicate,a=t.logErrors,f=t.diffPredicate;if("undefined"==typeof r)return function(){return function(e){return function(t){return e(t)}}};if(e.getState&&e.dispatch)return console.error("[redux-logger] redux-logger not installed. Make sure to pass logger instance as middleware:\n// Logger with default options\nimport { logger } from 'redux-logger'\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n// Or you can create your own logger with custom options http://bit.ly/redux-logger-options\nimport createLogger from 'redux-logger'\nconst logger = createLogger({\n  // ...options\n});\nconst store = createStore(\n  reducer,\n  applyMiddleware(logger)\n)\n"),function(){return function(e){return function(t){return e(t)}}};var u=[];return function(e){var r=e.getState;return function(e){return function(l){if("function"==typeof i&&!i(r,l))return e(l);var c={};u.push(c),c.started=O.now(),c.startedTime=new Date,c.prevState=n(r()),c.action=l;var s=void 0;if(a)try{s=e(l)}catch(e){c.error=o(e)}else s=e(l);c.took=O.now()-c.started,c.nextState=n(r());var d=t.diff&&"function"==typeof f?f(r,l):t.diff;if(x(u,Object.assign({},t,{diff:d})),u.length=0,c.error)throw c.error;return s}}}}var k,j,E=function(e,t){return new Array(t+1).join(e)},A=function(e,t){return E("0",t-e.toString().length)+e},D=function(e){return A(e.getHours(),2)+":"+A(e.getMinutes(),2)+":"+A(e.getSeconds(),2)+"."+A(e.getMilliseconds(),3)},O="undefined"!=typeof performance&&null!==performance&&"function"==typeof performance.now?performance:Date,N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},C=[];k="object"===("undefined"==typeof __webpack_require__.g?"undefined":N(__webpack_require__.g))&&__webpack_require__.g?__webpack_require__.g:"undefined"!=typeof window?window:{},j=k.DeepDiff,j&&C.push(function(){"undefined"!=typeof j&&k.DeepDiff===c&&(k.DeepDiff=j,j=void 0)}),t(n,r),t(o,r),t(i,r),t(a,r),Object.defineProperties(c,{diff:{value:c,enumerable:!0},observableDiff:{value:l,enumerable:!0},applyDiff:{value:h,enumerable:!0},applyChange:{value:d,enumerable:!0},revertChange:{value:g,enumerable:!0},isConflict:{value:function(){return"undefined"!=typeof j},enumerable:!0},noConflict:{value:function(){return C&&(C.forEach(function(e){e()}),C=null),c},enumerable:!0}});var F={E:{color:"#2196F3",text:"CHANGED:"},N:{color:"#4CAF50",text:"ADDED:"},D:{color:"#F44336",text:"DELETED:"},A:{color:"#2196F3",text:"ARRAY:"}},L={level:"log",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(e){return e},actionTransformer:function(e){return e},errorTransformer:function(e){return e},colors:{title:function(){return"inherit"},prevState:function(){return"#9E9E9E"},action:function(){return"#03A9F4"},nextState:function(){return"#4CAF50"},error:function(){return"#F20404"}},diff:!1,diffPredicate:void 0,transformer:void 0},T=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dispatch,r=e.getState;return"function"==typeof t||"function"==typeof r?S()({dispatch:t,getState:r}):void console.error("\n[redux-logger v3] BREAKING CHANGE\n[redux-logger v3] Since 3.0.0 redux-logger exports by default logger with default settings.\n[redux-logger v3] Change\n[redux-logger v3] import createLogger from 'redux-logger'\n[redux-logger v3] to\n[redux-logger v3] import { createLogger } from 'redux-logger'\n")};e.defaults=L,e.createLogger=S,e.logger=T,e.default=T,Object.defineProperty(e,"__esModule",{value:!0})});


/***/ }),

/***/ "./node_modules/redux-thunk/dist/redux-thunk.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/redux-thunk/dist/redux-thunk.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   thunk: () => (/* binding */ thunk),
/* harmony export */   withExtraArgument: () => (/* binding */ withExtraArgument)
/* harmony export */ });
// src/index.ts
function createThunkMiddleware(extraArgument) {
  const middleware = ({ dispatch, getState }) => (next) => (action) => {
    if (typeof action === "function") {
      return action(dispatch, getState, extraArgument);
    }
    return next(action);
  };
  return middleware;
}
var thunk = createThunkMiddleware();
var withExtraArgument = createThunkMiddleware;



/***/ }),

/***/ "./node_modules/redux/dist/redux.mjs":
/*!*******************************************!*\
  !*** ./node_modules/redux/dist/redux.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   applyMiddleware: () => (/* binding */ applyMiddleware),
/* harmony export */   combineReducers: () => (/* binding */ combineReducers),
/* harmony export */   compose: () => (/* binding */ compose),
/* harmony export */   createStore: () => (/* binding */ createStore),
/* harmony export */   isAction: () => (/* binding */ isAction),
/* harmony export */   isPlainObject: () => (/* binding */ isPlainObject)
/* harmony export */ });
/* unused harmony exports __DO_NOT_USE__ActionTypes, bindActionCreators, legacy_createStore */
// src/utils/formatProdErrorMessage.ts
function formatProdErrorMessage(code) {
  return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;
}

// src/utils/symbol-observable.ts
var $$observable = /* @__PURE__ */ (() => typeof Symbol === "function" && Symbol.observable || "@@observable")();
var symbol_observable_default = $$observable;

// src/utils/actionTypes.ts
var randomString = () => Math.random().toString(36).substring(7).split("").join(".");
var ActionTypes = {
  INIT: `@@redux/INIT${/* @__PURE__ */ randomString()}`,
  REPLACE: `@@redux/REPLACE${/* @__PURE__ */ randomString()}`,
  PROBE_UNKNOWN_ACTION: () => `@@redux/PROBE_UNKNOWN_ACTION${randomString()}`
};
var actionTypes_default = ActionTypes;

// src/utils/isPlainObject.ts
function isPlainObject(obj) {
  if (typeof obj !== "object" || obj === null)
    return false;
  let proto = obj;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;
}

// src/utils/kindOf.ts
function miniKindOf(val) {
  if (val === void 0)
    return "undefined";
  if (val === null)
    return "null";
  const type = typeof val;
  switch (type) {
    case "boolean":
    case "string":
    case "number":
    case "symbol":
    case "function": {
      return type;
    }
  }
  if (Array.isArray(val))
    return "array";
  if (isDate(val))
    return "date";
  if (isError(val))
    return "error";
  const constructorName = ctorName(val);
  switch (constructorName) {
    case "Symbol":
    case "Promise":
    case "WeakMap":
    case "WeakSet":
    case "Map":
    case "Set":
      return constructorName;
  }
  return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\s/g, "");
}
function ctorName(val) {
  return typeof val.constructor === "function" ? val.constructor.name : null;
}
function isError(val) {
  return val instanceof Error || typeof val.message === "string" && val.constructor && typeof val.constructor.stackTraceLimit === "number";
}
function isDate(val) {
  if (val instanceof Date)
    return true;
  return typeof val.toDateString === "function" && typeof val.getDate === "function" && typeof val.setDate === "function";
}
function kindOf(val) {
  let typeOfVal = typeof val;
  if (true) {
    typeOfVal = miniKindOf(val);
  }
  return typeOfVal;
}

// src/createStore.ts
function createStore(reducer, preloadedState, enhancer) {
  if (typeof reducer !== "function") {
    throw new Error( false ? 0 : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);
  }
  if (typeof preloadedState === "function" && typeof enhancer === "function" || typeof enhancer === "function" && typeof arguments[3] === "function") {
    throw new Error( false ? 0 : "It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.");
  }
  if (typeof preloadedState === "function" && typeof enhancer === "undefined") {
    enhancer = preloadedState;
    preloadedState = void 0;
  }
  if (typeof enhancer !== "undefined") {
    if (typeof enhancer !== "function") {
      throw new Error( false ? 0 : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);
    }
    return enhancer(createStore)(reducer, preloadedState);
  }
  let currentReducer = reducer;
  let currentState = preloadedState;
  let currentListeners = /* @__PURE__ */ new Map();
  let nextListeners = currentListeners;
  let listenerIdCounter = 0;
  let isDispatching = false;
  function ensureCanMutateNextListeners() {
    if (nextListeners === currentListeners) {
      nextListeners = /* @__PURE__ */ new Map();
      currentListeners.forEach((listener, key) => {
        nextListeners.set(key, listener);
      });
    }
  }
  function getState() {
    if (isDispatching) {
      throw new Error( false ? 0 : "You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.");
    }
    return currentState;
  }
  function subscribe(listener) {
    if (typeof listener !== "function") {
      throw new Error( false ? 0 : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);
    }
    if (isDispatching) {
      throw new Error( false ? 0 : "You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.");
    }
    let isSubscribed = true;
    ensureCanMutateNextListeners();
    const listenerId = listenerIdCounter++;
    nextListeners.set(listenerId, listener);
    return function unsubscribe() {
      if (!isSubscribed) {
        return;
      }
      if (isDispatching) {
        throw new Error( false ? 0 : "You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.");
      }
      isSubscribed = false;
      ensureCanMutateNextListeners();
      nextListeners.delete(listenerId);
      currentListeners = null;
    };
  }
  function dispatch(action) {
    if (!isPlainObject(action)) {
      throw new Error( false ? 0 : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);
    }
    if (typeof action.type === "undefined") {
      throw new Error( false ? 0 : 'Actions may not have an undefined "type" property. You may have misspelled an action type string constant.');
    }
    if (typeof action.type !== "string") {
      throw new Error( false ? 0 : `Action "type" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);
    }
    if (isDispatching) {
      throw new Error( false ? 0 : "Reducers may not dispatch actions.");
    }
    try {
      isDispatching = true;
      currentState = currentReducer(currentState, action);
    } finally {
      isDispatching = false;
    }
    const listeners = currentListeners = nextListeners;
    listeners.forEach((listener) => {
      listener();
    });
    return action;
  }
  function replaceReducer(nextReducer) {
    if (typeof nextReducer !== "function") {
      throw new Error( false ? 0 : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);
    }
    currentReducer = nextReducer;
    dispatch({
      type: actionTypes_default.REPLACE
    });
  }
  function observable() {
    const outerSubscribe = subscribe;
    return {
      /**
       * The minimal observable subscription method.
       * @param observer Any object that can be used as an observer.
       * The observer object should have a `next` method.
       * @returns An object with an `unsubscribe` method that can
       * be used to unsubscribe the observable from the store, and prevent further
       * emission of values from the observable.
       */
      subscribe(observer) {
        if (typeof observer !== "object" || observer === null) {
          throw new Error( false ? 0 : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);
        }
        function observeState() {
          const observerAsObserver = observer;
          if (observerAsObserver.next) {
            observerAsObserver.next(getState());
          }
        }
        observeState();
        const unsubscribe = outerSubscribe(observeState);
        return {
          unsubscribe
        };
      },
      [symbol_observable_default]() {
        return this;
      }
    };
  }
  dispatch({
    type: actionTypes_default.INIT
  });
  const store = {
    dispatch,
    subscribe,
    getState,
    replaceReducer,
    [symbol_observable_default]: observable
  };
  return store;
}
function legacy_createStore(reducer, preloadedState, enhancer) {
  return createStore(reducer, preloadedState, enhancer);
}

// src/utils/warning.ts
function warning(message) {
  if (typeof console !== "undefined" && typeof console.error === "function") {
    console.error(message);
  }
  try {
    throw new Error(message);
  } catch (e) {
  }
}

// src/combineReducers.ts
function getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {
  const reducerKeys = Object.keys(reducers);
  const argumentName = action && action.type === actionTypes_default.INIT ? "preloadedState argument passed to createStore" : "previous state received by the reducer";
  if (reducerKeys.length === 0) {
    return "Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.";
  }
  if (!isPlainObject(inputState)) {
    return `The ${argumentName} has unexpected type of "${kindOf(inputState)}". Expected argument to be an object with the following keys: "${reducerKeys.join('", "')}"`;
  }
  const unexpectedKeys = Object.keys(inputState).filter((key) => !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);
  unexpectedKeys.forEach((key) => {
    unexpectedKeyCache[key] = true;
  });
  if (action && action.type === actionTypes_default.REPLACE)
    return;
  if (unexpectedKeys.length > 0) {
    return `Unexpected ${unexpectedKeys.length > 1 ? "keys" : "key"} "${unexpectedKeys.join('", "')}" found in ${argumentName}. Expected to find one of the known reducer keys instead: "${reducerKeys.join('", "')}". Unexpected keys will be ignored.`;
  }
}
function assertReducerShape(reducers) {
  Object.keys(reducers).forEach((key) => {
    const reducer = reducers[key];
    const initialState = reducer(void 0, {
      type: actionTypes_default.INIT
    });
    if (typeof initialState === "undefined") {
      throw new Error( false ? 0 : `The slice reducer for key "${key}" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);
    }
    if (typeof reducer(void 0, {
      type: actionTypes_default.PROBE_UNKNOWN_ACTION()
    }) === "undefined") {
      throw new Error( false ? 0 : `The slice reducer for key "${key}" returned undefined when probed with a random type. Don't try to handle '${actionTypes_default.INIT}' or other actions in "redux/*" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.`);
    }
  });
}
function combineReducers(reducers) {
  const reducerKeys = Object.keys(reducers);
  const finalReducers = {};
  for (let i = 0; i < reducerKeys.length; i++) {
    const key = reducerKeys[i];
    if (true) {
      if (typeof reducers[key] === "undefined") {
        warning(`No reducer provided for key "${key}"`);
      }
    }
    if (typeof reducers[key] === "function") {
      finalReducers[key] = reducers[key];
    }
  }
  const finalReducerKeys = Object.keys(finalReducers);
  let unexpectedKeyCache;
  if (true) {
    unexpectedKeyCache = {};
  }
  let shapeAssertionError;
  try {
    assertReducerShape(finalReducers);
  } catch (e) {
    shapeAssertionError = e;
  }
  return function combination(state = {}, action) {
    if (shapeAssertionError) {
      throw shapeAssertionError;
    }
    if (true) {
      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);
      if (warningMessage) {
        warning(warningMessage);
      }
    }
    let hasChanged = false;
    const nextState = {};
    for (let i = 0; i < finalReducerKeys.length; i++) {
      const key = finalReducerKeys[i];
      const reducer = finalReducers[key];
      const previousStateForKey = state[key];
      const nextStateForKey = reducer(previousStateForKey, action);
      if (typeof nextStateForKey === "undefined") {
        const actionType = action && action.type;
        throw new Error( false ? 0 : `When called with an action of type ${actionType ? `"${String(actionType)}"` : "(unknown type)"}, the slice reducer for key "${key}" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.`);
      }
      nextState[key] = nextStateForKey;
      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;
    }
    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;
    return hasChanged ? nextState : state;
  };
}

// src/bindActionCreators.ts
function bindActionCreator(actionCreator, dispatch) {
  return function(...args) {
    return dispatch(actionCreator.apply(this, args));
  };
}
function bindActionCreators(actionCreators, dispatch) {
  if (typeof actionCreators === "function") {
    return bindActionCreator(actionCreators, dispatch);
  }
  if (typeof actionCreators !== "object" || actionCreators === null) {
    throw new Error( false ? 0 : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. Did you write "import ActionCreators from" instead of "import * as ActionCreators from"?`);
  }
  const boundActionCreators = {};
  for (const key in actionCreators) {
    const actionCreator = actionCreators[key];
    if (typeof actionCreator === "function") {
      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);
    }
  }
  return boundActionCreators;
}

// src/compose.ts
function compose(...funcs) {
  if (funcs.length === 0) {
    return (arg) => arg;
  }
  if (funcs.length === 1) {
    return funcs[0];
  }
  return funcs.reduce((a, b) => (...args) => a(b(...args)));
}

// src/applyMiddleware.ts
function applyMiddleware(...middlewares) {
  return (createStore2) => (reducer, preloadedState) => {
    const store = createStore2(reducer, preloadedState);
    let dispatch = () => {
      throw new Error( false ? 0 : "Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.");
    };
    const middlewareAPI = {
      getState: store.getState,
      dispatch: (action, ...args) => dispatch(action, ...args)
    };
    const chain = middlewares.map((middleware) => middleware(middlewareAPI));
    dispatch = compose(...chain)(store.dispatch);
    return {
      ...store,
      dispatch
    };
  };
}

// src/utils/isAction.ts
function isAction(action) {
  return isPlainObject(action) && "type" in action && typeof action.type === "string";
}

//# sourceMappingURL=redux.mjs.map

/***/ }),

/***/ "./node_modules/reselect/dist/reselect.mjs":
/*!*************************************************!*\
  !*** ./node_modules/reselect/dist/reselect.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   createSelectorCreator: () => (/* binding */ createSelectorCreator),
/* harmony export */   weakMapMemoize: () => (/* binding */ weakMapMemoize)
/* harmony export */ });
/* unused harmony exports createSelector, createStructuredSelector, lruMemoize, referenceEqualityCheck, setGlobalDevModeChecks, unstable_autotrackMemoize */
// src/devModeChecks/identityFunctionCheck.ts
var runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult) => {
  if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {
    let isInputSameAsOutput = false;
    try {
      const emptyObject = {};
      if (resultFunc(emptyObject) === emptyObject)
        isInputSameAsOutput = true;
    } catch {
    }
    if (isInputSameAsOutput) {
      let stack = void 0;
      try {
        throw new Error();
      } catch (e) {
        ;
        ({ stack } = e);
      }
      console.warn(
        "The result function returned its own inputs without modification. e.g\n`createSelector([state => state.todos], todos => todos)`\nThis could lead to inefficient memoization and unnecessary re-renders.\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.",
        { stack }
      );
    }
  }
};

// src/devModeChecks/inputStabilityCheck.ts
var runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs) => {
  const { memoize, memoizeOptions } = options;
  const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;
  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions);
  const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);
  if (!areInputSelectorResultsEqual) {
    let stack = void 0;
    try {
      throw new Error();
    } catch (e) {
      ;
      ({ stack } = e);
    }
    console.warn(
      "An input selector returned a different result when passed same arguments.\nThis means your output selector will likely run more frequently than intended.\nAvoid returning a new reference inside your input selector, e.g.\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`",
      {
        arguments: inputSelectorArgs,
        firstInputs: inputSelectorResults,
        secondInputs: inputSelectorResultsCopy,
        stack
      }
    );
  }
};

// src/devModeChecks/setGlobalDevModeChecks.ts
var globalDevModeChecks = {
  inputStabilityCheck: "once",
  identityFunctionCheck: "once"
};
var setGlobalDevModeChecks = (devModeChecks) => {
  Object.assign(globalDevModeChecks, devModeChecks);
};

// src/utils.ts
var NOT_FOUND = /* @__PURE__ */ Symbol("NOT_FOUND");
function assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {
  if (typeof func !== "function") {
    throw new TypeError(errorMessage);
  }
}
function assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {
  if (typeof object !== "object") {
    throw new TypeError(errorMessage);
  }
}
function assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {
  if (!array.every((item) => typeof item === "function")) {
    const itemTypes = array.map(
      (item) => typeof item === "function" ? `function ${item.name || "unnamed"}()` : typeof item
    ).join(", ");
    throw new TypeError(`${errorMessage}[${itemTypes}]`);
  }
}
var ensureIsArray = (item) => {
  return Array.isArray(item) ? item : [item];
};
function getDependencies(createSelectorArgs) {
  const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;
  assertIsArrayOfFunctions(
    dependencies,
    `createSelector expects all input-selectors to be functions, but received the following types: `
  );
  return dependencies;
}
function collectInputSelectorResults(dependencies, inputSelectorArgs) {
  const inputSelectorResults = [];
  const { length } = dependencies;
  for (let i = 0; i < length; i++) {
    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));
  }
  return inputSelectorResults;
}
var getDevModeChecksExecutionInfo = (firstRun, devModeChecks) => {
  const { identityFunctionCheck, inputStabilityCheck } = {
    ...globalDevModeChecks,
    ...devModeChecks
  };
  return {
    identityFunctionCheck: {
      shouldRun: identityFunctionCheck === "always" || identityFunctionCheck === "once" && firstRun,
      run: runIdentityFunctionCheck
    },
    inputStabilityCheck: {
      shouldRun: inputStabilityCheck === "always" || inputStabilityCheck === "once" && firstRun,
      run: runInputStabilityCheck
    }
  };
};

// src/autotrackMemoize/autotracking.ts
var $REVISION = 0;
var CURRENT_TRACKER = null;
var Cell = class {
  revision = $REVISION;
  _value;
  _lastValue;
  _isEqual = tripleEq;
  constructor(initialValue, isEqual = tripleEq) {
    this._value = this._lastValue = initialValue;
    this._isEqual = isEqual;
  }
  // Whenever a storage value is read, it'll add itself to the current tracker if
  // one exists, entangling its state with that cache.
  get value() {
    CURRENT_TRACKER?.add(this);
    return this._value;
  }
  // Whenever a storage value is updated, we bump the global revision clock,
  // assign the revision for this storage to the new value, _and_ we schedule a
  // rerender. This is important, and it's what makes autotracking  _pull_
  // based. We don't actively tell the caches which depend on the storage that
  // anything has happened. Instead, we recompute the caches when needed.
  set value(newValue) {
    if (this.value === newValue)
      return;
    this._value = newValue;
    this.revision = ++$REVISION;
  }
};
function tripleEq(a, b) {
  return a === b;
}
var TrackingCache = class {
  _cachedValue;
  _cachedRevision = -1;
  _deps = [];
  hits = 0;
  fn;
  constructor(fn) {
    this.fn = fn;
  }
  clear() {
    this._cachedValue = void 0;
    this._cachedRevision = -1;
    this._deps = [];
    this.hits = 0;
  }
  get value() {
    if (this.revision > this._cachedRevision) {
      const { fn } = this;
      const currentTracker = /* @__PURE__ */ new Set();
      const prevTracker = CURRENT_TRACKER;
      CURRENT_TRACKER = currentTracker;
      this._cachedValue = fn();
      CURRENT_TRACKER = prevTracker;
      this.hits++;
      this._deps = Array.from(currentTracker);
      this._cachedRevision = this.revision;
    }
    CURRENT_TRACKER?.add(this);
    return this._cachedValue;
  }
  get revision() {
    return Math.max(...this._deps.map((d) => d.revision), 0);
  }
};
function getValue(cell) {
  if (!(cell instanceof Cell)) {
    console.warn("Not a valid cell! ", cell);
  }
  return cell.value;
}
function setValue(storage, value) {
  if (!(storage instanceof Cell)) {
    throw new TypeError(
      "setValue must be passed a tracked store created with `createStorage`."
    );
  }
  storage.value = storage._lastValue = value;
}
function createCell(initialValue, isEqual = tripleEq) {
  return new Cell(initialValue, isEqual);
}
function createCache(fn) {
  assertIsFunction(
    fn,
    "the first parameter to `createCache` must be a function"
  );
  return new TrackingCache(fn);
}

// src/autotrackMemoize/tracking.ts
var neverEq = (a, b) => false;
function createTag() {
  return createCell(null, neverEq);
}
function dirtyTag(tag, value) {
  setValue(tag, value);
}
var consumeCollection = (node) => {
  let tag = node.collectionTag;
  if (tag === null) {
    tag = node.collectionTag = createTag();
  }
  getValue(tag);
};
var dirtyCollection = (node) => {
  const tag = node.collectionTag;
  if (tag !== null) {
    dirtyTag(tag, null);
  }
};

// src/autotrackMemoize/proxy.ts
var REDUX_PROXY_LABEL = Symbol();
var nextId = 0;
var proto = Object.getPrototypeOf({});
var ObjectTreeNode = class {
  constructor(value) {
    this.value = value;
    this.value = value;
    this.tag.value = value;
  }
  proxy = new Proxy(this, objectProxyHandler);
  tag = createTag();
  tags = {};
  children = {};
  collectionTag = null;
  id = nextId++;
};
var objectProxyHandler = {
  get(node, key) {
    function calculateResult() {
      const { value } = node;
      const childValue = Reflect.get(value, key);
      if (typeof key === "symbol") {
        return childValue;
      }
      if (key in proto) {
        return childValue;
      }
      if (typeof childValue === "object" && childValue !== null) {
        let childNode = node.children[key];
        if (childNode === void 0) {
          childNode = node.children[key] = createNode(childValue);
        }
        if (childNode.tag) {
          getValue(childNode.tag);
        }
        return childNode.proxy;
      } else {
        let tag = node.tags[key];
        if (tag === void 0) {
          tag = node.tags[key] = createTag();
          tag.value = childValue;
        }
        getValue(tag);
        return childValue;
      }
    }
    const res = calculateResult();
    return res;
  },
  ownKeys(node) {
    consumeCollection(node);
    return Reflect.ownKeys(node.value);
  },
  getOwnPropertyDescriptor(node, prop) {
    return Reflect.getOwnPropertyDescriptor(node.value, prop);
  },
  has(node, prop) {
    return Reflect.has(node.value, prop);
  }
};
var ArrayTreeNode = class {
  constructor(value) {
    this.value = value;
    this.value = value;
    this.tag.value = value;
  }
  proxy = new Proxy([this], arrayProxyHandler);
  tag = createTag();
  tags = {};
  children = {};
  collectionTag = null;
  id = nextId++;
};
var arrayProxyHandler = {
  get([node], key) {
    if (key === "length") {
      consumeCollection(node);
    }
    return objectProxyHandler.get(node, key);
  },
  ownKeys([node]) {
    return objectProxyHandler.ownKeys(node);
  },
  getOwnPropertyDescriptor([node], prop) {
    return objectProxyHandler.getOwnPropertyDescriptor(node, prop);
  },
  has([node], prop) {
    return objectProxyHandler.has(node, prop);
  }
};
function createNode(value) {
  if (Array.isArray(value)) {
    return new ArrayTreeNode(value);
  }
  return new ObjectTreeNode(value);
}
function updateNode(node, newValue) {
  const { value, tags, children } = node;
  node.value = newValue;
  if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {
    dirtyCollection(node);
  } else {
    if (value !== newValue) {
      let oldKeysSize = 0;
      let newKeysSize = 0;
      let anyKeysAdded = false;
      for (const _key in value) {
        oldKeysSize++;
      }
      for (const key in newValue) {
        newKeysSize++;
        if (!(key in value)) {
          anyKeysAdded = true;
          break;
        }
      }
      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;
      if (isDifferent) {
        dirtyCollection(node);
      }
    }
  }
  for (const key in tags) {
    const childValue = value[key];
    const newChildValue = newValue[key];
    if (childValue !== newChildValue) {
      dirtyCollection(node);
      dirtyTag(tags[key], newChildValue);
    }
    if (typeof newChildValue === "object" && newChildValue !== null) {
      delete tags[key];
    }
  }
  for (const key in children) {
    const childNode = children[key];
    const newChildValue = newValue[key];
    const childValue = childNode.value;
    if (childValue === newChildValue) {
      continue;
    } else if (typeof newChildValue === "object" && newChildValue !== null) {
      updateNode(childNode, newChildValue);
    } else {
      deleteNode(childNode);
      delete children[key];
    }
  }
}
function deleteNode(node) {
  if (node.tag) {
    dirtyTag(node.tag, null);
  }
  dirtyCollection(node);
  for (const key in node.tags) {
    dirtyTag(node.tags[key], null);
  }
  for (const key in node.children) {
    deleteNode(node.children[key]);
  }
}

// src/lruMemoize.ts
function createSingletonCache(equals) {
  let entry;
  return {
    get(key) {
      if (entry && equals(entry.key, key)) {
        return entry.value;
      }
      return NOT_FOUND;
    },
    put(key, value) {
      entry = { key, value };
    },
    getEntries() {
      return entry ? [entry] : [];
    },
    clear() {
      entry = void 0;
    }
  };
}
function createLruCache(maxSize, equals) {
  let entries = [];
  function get(key) {
    const cacheIndex = entries.findIndex((entry) => equals(key, entry.key));
    if (cacheIndex > -1) {
      const entry = entries[cacheIndex];
      if (cacheIndex > 0) {
        entries.splice(cacheIndex, 1);
        entries.unshift(entry);
      }
      return entry.value;
    }
    return NOT_FOUND;
  }
  function put(key, value) {
    if (get(key) === NOT_FOUND) {
      entries.unshift({ key, value });
      if (entries.length > maxSize) {
        entries.pop();
      }
    }
  }
  function getEntries() {
    return entries;
  }
  function clear() {
    entries = [];
  }
  return { get, put, getEntries, clear };
}
var referenceEqualityCheck = (a, b) => a === b;
function createCacheKeyComparator(equalityCheck) {
  return function areArgumentsShallowlyEqual(prev, next) {
    if (prev === null || next === null || prev.length !== next.length) {
      return false;
    }
    const { length } = prev;
    for (let i = 0; i < length; i++) {
      if (!equalityCheck(prev[i], next[i])) {
        return false;
      }
    }
    return true;
  };
}
function lruMemoize(func, equalityCheckOrOptions) {
  const providedOptions = typeof equalityCheckOrOptions === "object" ? equalityCheckOrOptions : { equalityCheck: equalityCheckOrOptions };
  const {
    equalityCheck = referenceEqualityCheck,
    maxSize = 1,
    resultEqualityCheck
  } = providedOptions;
  const comparator = createCacheKeyComparator(equalityCheck);
  let resultsCount = 0;
  const cache = maxSize <= 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);
  function memoized() {
    let value = cache.get(arguments);
    if (value === NOT_FOUND) {
      value = func.apply(null, arguments);
      resultsCount++;
      if (resultEqualityCheck) {
        const entries = cache.getEntries();
        const matchingEntry = entries.find(
          (entry) => resultEqualityCheck(entry.value, value)
        );
        if (matchingEntry) {
          value = matchingEntry.value;
          resultsCount !== 0 && resultsCount--;
        }
      }
      cache.put(arguments, value);
    }
    return value;
  }
  memoized.clearCache = () => {
    cache.clear();
    memoized.resetResultsCount();
  };
  memoized.resultsCount = () => resultsCount;
  memoized.resetResultsCount = () => {
    resultsCount = 0;
  };
  return memoized;
}

// src/autotrackMemoize/autotrackMemoize.ts
function autotrackMemoize(func) {
  const node = createNode(
    []
  );
  let lastArgs = null;
  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);
  const cache = createCache(() => {
    const res = func.apply(null, node.proxy);
    return res;
  });
  function memoized() {
    if (!shallowEqual(lastArgs, arguments)) {
      updateNode(node, arguments);
      lastArgs = arguments;
    }
    return cache.value;
  }
  memoized.clearCache = () => {
    return cache.clear();
  };
  return memoized;
}

// src/weakMapMemoize.ts
var StrongRef = class {
  constructor(value) {
    this.value = value;
  }
  deref() {
    return this.value;
  }
};
var Ref = typeof WeakRef !== "undefined" ? WeakRef : StrongRef;
var UNTERMINATED = 0;
var TERMINATED = 1;
function createCacheNode() {
  return {
    s: UNTERMINATED,
    v: void 0,
    o: null,
    p: null
  };
}
function weakMapMemoize(func, options = {}) {
  let fnNode = createCacheNode();
  const { resultEqualityCheck } = options;
  let lastResult;
  let resultsCount = 0;
  function memoized() {
    let cacheNode = fnNode;
    const { length } = arguments;
    for (let i = 0, l = length; i < l; i++) {
      const arg = arguments[i];
      if (typeof arg === "function" || typeof arg === "object" && arg !== null) {
        let objectCache = cacheNode.o;
        if (objectCache === null) {
          cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();
        }
        const objectNode = objectCache.get(arg);
        if (objectNode === void 0) {
          cacheNode = createCacheNode();
          objectCache.set(arg, cacheNode);
        } else {
          cacheNode = objectNode;
        }
      } else {
        let primitiveCache = cacheNode.p;
        if (primitiveCache === null) {
          cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();
        }
        const primitiveNode = primitiveCache.get(arg);
        if (primitiveNode === void 0) {
          cacheNode = createCacheNode();
          primitiveCache.set(arg, cacheNode);
        } else {
          cacheNode = primitiveNode;
        }
      }
    }
    const terminatedNode = cacheNode;
    let result;
    if (cacheNode.s === TERMINATED) {
      result = cacheNode.v;
    } else {
      result = func.apply(null, arguments);
      resultsCount++;
      if (resultEqualityCheck) {
        const lastResultValue = lastResult?.deref?.() ?? lastResult;
        if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {
          result = lastResultValue;
          resultsCount !== 0 && resultsCount--;
        }
        const needsWeakRef = typeof result === "object" && result !== null || typeof result === "function";
        lastResult = needsWeakRef ? new Ref(result) : result;
      }
    }
    terminatedNode.s = TERMINATED;
    terminatedNode.v = result;
    return result;
  }
  memoized.clearCache = () => {
    fnNode = createCacheNode();
    memoized.resetResultsCount();
  };
  memoized.resultsCount = () => resultsCount;
  memoized.resetResultsCount = () => {
    resultsCount = 0;
  };
  return memoized;
}

// src/createSelectorCreator.ts
function createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {
  const createSelectorCreatorOptions = typeof memoizeOrOptions === "function" ? {
    memoize: memoizeOrOptions,
    memoizeOptions: memoizeOptionsFromArgs
  } : memoizeOrOptions;
  const createSelector2 = (...createSelectorArgs) => {
    let recomputations = 0;
    let dependencyRecomputations = 0;
    let lastResult;
    let directlyPassedOptions = {};
    let resultFunc = createSelectorArgs.pop();
    if (typeof resultFunc === "object") {
      directlyPassedOptions = resultFunc;
      resultFunc = createSelectorArgs.pop();
    }
    assertIsFunction(
      resultFunc,
      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`
    );
    const combinedOptions = {
      ...createSelectorCreatorOptions,
      ...directlyPassedOptions
    };
    const {
      memoize,
      memoizeOptions = [],
      argsMemoize = weakMapMemoize,
      argsMemoizeOptions = [],
      devModeChecks = {}
    } = combinedOptions;
    const finalMemoizeOptions = ensureIsArray(memoizeOptions);
    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);
    const dependencies = getDependencies(createSelectorArgs);
    const memoizedResultFunc = memoize(function recomputationWrapper() {
      recomputations++;
      return resultFunc.apply(
        null,
        arguments
      );
    }, ...finalMemoizeOptions);
    let firstRun = true;
    const selector = argsMemoize(function dependenciesChecker() {
      dependencyRecomputations++;
      const inputSelectorResults = collectInputSelectorResults(
        dependencies,
        arguments
      );
      lastResult = memoizedResultFunc.apply(null, inputSelectorResults);
      if (true) {
        const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);
        if (identityFunctionCheck.shouldRun) {
          identityFunctionCheck.run(
            resultFunc,
            inputSelectorResults,
            lastResult
          );
        }
        if (inputStabilityCheck.shouldRun) {
          const inputSelectorResultsCopy = collectInputSelectorResults(
            dependencies,
            arguments
          );
          inputStabilityCheck.run(
            { inputSelectorResults, inputSelectorResultsCopy },
            { memoize, memoizeOptions: finalMemoizeOptions },
            arguments
          );
        }
        if (firstRun)
          firstRun = false;
      }
      return lastResult;
    }, ...finalArgsMemoizeOptions);
    return Object.assign(selector, {
      resultFunc,
      memoizedResultFunc,
      dependencies,
      dependencyRecomputations: () => dependencyRecomputations,
      resetDependencyRecomputations: () => {
        dependencyRecomputations = 0;
      },
      lastResult: () => lastResult,
      recomputations: () => recomputations,
      resetRecomputations: () => {
        recomputations = 0;
      },
      memoize,
      argsMemoize
    });
  };
  Object.assign(createSelector2, {
    withTypes: () => createSelector2
  });
  return createSelector2;
}
var createSelector = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);

// src/createStructuredSelector.ts
var createStructuredSelector = Object.assign(
  (inputSelectorsObject, selectorCreator = createSelector) => {
    assertIsObject(
      inputSelectorsObject,
      `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`
    );
    const inputSelectorKeys = Object.keys(inputSelectorsObject);
    const dependencies = inputSelectorKeys.map(
      (key) => inputSelectorsObject[key]
    );
    const structuredSelector = selectorCreator(
      dependencies,
      (...inputSelectorResults) => {
        return inputSelectorResults.reduce((composition, value, index) => {
          composition[inputSelectorKeys[index]] = value;
          return composition;
        }, {});
      }
    );
    return structuredSelector;
  },
  { withTypes: () => createStructuredSelector }
);

//# sourceMappingURL=reselect.mjs.map

/***/ }),

/***/ "./node_modules/turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js":
/*!************************************************************************!*\
  !*** ./node_modules/turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   gfm: () => (/* binding */ gfm)
/* harmony export */ });
/* unused harmony exports highlightedCodeBlock, strikethrough, tables, taskListItems */
var highlightRegExp = /highlight-(?:text|source)-([a-z0-9]+)/;

function highlightedCodeBlock (turndownService) {
  turndownService.addRule('highlightedCodeBlock', {
    filter: function (node) {
      var firstChild = node.firstChild;
      return (
        node.nodeName === 'DIV' &&
        highlightRegExp.test(node.className) &&
        firstChild &&
        firstChild.nodeName === 'PRE'
      )
    },
    replacement: function (content, node, options) {
      var className = node.className || '';
      var language = (className.match(highlightRegExp) || [null, ''])[1];

      return (
        '\n\n' + options.fence + language + '\n' +
        node.firstChild.textContent +
        '\n' + options.fence + '\n\n'
      )
    }
  });
}

function strikethrough (turndownService) {
  turndownService.addRule('strikethrough', {
    filter: ['del', 's', 'strike'],
    replacement: function (content) {
      return '~' + content + '~'
    }
  });
}

var indexOf = Array.prototype.indexOf;
var every = Array.prototype.every;
var rules = {};

rules.tableCell = {
  filter: ['th', 'td'],
  replacement: function (content, node) {
    return cell(content, node)
  }
};

rules.tableRow = {
  filter: 'tr',
  replacement: function (content, node) {
    var borderCells = '';
    var alignMap = { left: ':--', right: '--:', center: ':-:' };

    if (isHeadingRow(node)) {
      for (var i = 0; i < node.childNodes.length; i++) {
        var border = '---';
        var align = (
          node.childNodes[i].getAttribute('align') || ''
        ).toLowerCase();

        if (align) border = alignMap[align] || border;

        borderCells += cell(border, node.childNodes[i]);
      }
    }
    return '\n' + content + (borderCells ? '\n' + borderCells : '')
  }
};

rules.table = {
  // Only convert tables with a heading row.
  // Tables with no heading row are kept using `keep` (see below).
  filter: function (node) {
    return node.nodeName === 'TABLE' && isHeadingRow(node.rows[0])
  },

  replacement: function (content) {
    // Ensure there are no blank lines
    content = content.replace('\n\n', '\n');
    return '\n\n' + content + '\n\n'
  }
};

rules.tableSection = {
  filter: ['thead', 'tbody', 'tfoot'],
  replacement: function (content) {
    return content
  }
};

// A tr is a heading row if:
// - the parent is a THEAD
// - or if its the first child of the TABLE or the first TBODY (possibly
//   following a blank THEAD)
// - and every cell is a TH
function isHeadingRow (tr) {
  var parentNode = tr.parentNode;
  return (
    parentNode.nodeName === 'THEAD' ||
    (
      parentNode.firstChild === tr &&
      (parentNode.nodeName === 'TABLE' || isFirstTbody(parentNode)) &&
      every.call(tr.childNodes, function (n) { return n.nodeName === 'TH' })
    )
  )
}

function isFirstTbody (element) {
  var previousSibling = element.previousSibling;
  return (
    element.nodeName === 'TBODY' && (
      !previousSibling ||
      (
        previousSibling.nodeName === 'THEAD' &&
        /^\s*$/i.test(previousSibling.textContent)
      )
    )
  )
}

function cell (content, node) {
  var index = indexOf.call(node.parentNode.childNodes, node);
  var prefix = ' ';
  if (index === 0) prefix = '| ';
  return prefix + content + ' |'
}

function tables (turndownService) {
  turndownService.keep(function (node) {
    return node.nodeName === 'TABLE' && !isHeadingRow(node.rows[0])
  });
  for (var key in rules) turndownService.addRule(key, rules[key]);
}

function taskListItems (turndownService) {
  turndownService.addRule('taskListItems', {
    filter: function (node) {
      return node.type === 'checkbox' && node.parentNode.nodeName === 'LI'
    },
    replacement: function (content, node) {
      return (node.checked ? '[x]' : '[ ]') + ' '
    }
  });
}

function gfm (turndownService) {
  turndownService.use([
    highlightedCodeBlock,
    strikethrough,
    tables,
    taskListItems
  ]);
}




/***/ }),

/***/ "./node_modules/turndown/lib/turndown.browser.es.js":
/*!**********************************************************!*\
  !*** ./node_modules/turndown/lib/turndown.browser.es.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
function extend (destination) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i];
    for (var key in source) {
      if (source.hasOwnProperty(key)) destination[key] = source[key];
    }
  }
  return destination
}

function repeat (character, count) {
  return Array(count + 1).join(character)
}

function trimLeadingNewlines (string) {
  return string.replace(/^\n*/, '')
}

function trimTrailingNewlines (string) {
  // avoid match-at-end regexp bottleneck, see #370
  var indexEnd = string.length;
  while (indexEnd > 0 && string[indexEnd - 1] === '\n') indexEnd--;
  return string.substring(0, indexEnd)
}

var blockElements = [
  'ADDRESS', 'ARTICLE', 'ASIDE', 'AUDIO', 'BLOCKQUOTE', 'BODY', 'CANVAS',
  'CENTER', 'DD', 'DIR', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE',
  'FOOTER', 'FORM', 'FRAMESET', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER',
  'HGROUP', 'HR', 'HTML', 'ISINDEX', 'LI', 'MAIN', 'MENU', 'NAV', 'NOFRAMES',
  'NOSCRIPT', 'OL', 'OUTPUT', 'P', 'PRE', 'SECTION', 'TABLE', 'TBODY', 'TD',
  'TFOOT', 'TH', 'THEAD', 'TR', 'UL'
];

function isBlock (node) {
  return is(node, blockElements)
}

var voidElements = [
  'AREA', 'BASE', 'BR', 'COL', 'COMMAND', 'EMBED', 'HR', 'IMG', 'INPUT',
  'KEYGEN', 'LINK', 'META', 'PARAM', 'SOURCE', 'TRACK', 'WBR'
];

function isVoid (node) {
  return is(node, voidElements)
}

function hasVoid (node) {
  return has(node, voidElements)
}

var meaningfulWhenBlankElements = [
  'A', 'TABLE', 'THEAD', 'TBODY', 'TFOOT', 'TH', 'TD', 'IFRAME', 'SCRIPT',
  'AUDIO', 'VIDEO'
];

function isMeaningfulWhenBlank (node) {
  return is(node, meaningfulWhenBlankElements)
}

function hasMeaningfulWhenBlank (node) {
  return has(node, meaningfulWhenBlankElements)
}

function is (node, tagNames) {
  return tagNames.indexOf(node.nodeName) >= 0
}

function has (node, tagNames) {
  return (
    node.getElementsByTagName &&
    tagNames.some(function (tagName) {
      return node.getElementsByTagName(tagName).length
    })
  )
}

var rules = {};

rules.paragraph = {
  filter: 'p',

  replacement: function (content) {
    return '\n\n' + content + '\n\n'
  }
};

rules.lineBreak = {
  filter: 'br',

  replacement: function (content, node, options) {
    return options.br + '\n'
  }
};

rules.heading = {
  filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],

  replacement: function (content, node, options) {
    var hLevel = Number(node.nodeName.charAt(1));

    if (options.headingStyle === 'setext' && hLevel < 3) {
      var underline = repeat((hLevel === 1 ? '=' : '-'), content.length);
      return (
        '\n\n' + content + '\n' + underline + '\n\n'
      )
    } else {
      return '\n\n' + repeat('#', hLevel) + ' ' + content + '\n\n'
    }
  }
};

rules.blockquote = {
  filter: 'blockquote',

  replacement: function (content) {
    content = content.replace(/^\n+|\n+$/g, '');
    content = content.replace(/^/gm, '> ');
    return '\n\n' + content + '\n\n'
  }
};

rules.list = {
  filter: ['ul', 'ol'],

  replacement: function (content, node) {
    var parent = node.parentNode;
    if (parent.nodeName === 'LI' && parent.lastElementChild === node) {
      return '\n' + content
    } else {
      return '\n\n' + content + '\n\n'
    }
  }
};

rules.listItem = {
  filter: 'li',

  replacement: function (content, node, options) {
    content = content
      .replace(/^\n+/, '') // remove leading newlines
      .replace(/\n+$/, '\n') // replace trailing newlines with just a single one
      .replace(/\n/gm, '\n    '); // indent
    var prefix = options.bulletListMarker + '   ';
    var parent = node.parentNode;
    if (parent.nodeName === 'OL') {
      var start = parent.getAttribute('start');
      var index = Array.prototype.indexOf.call(parent.children, node);
      prefix = (start ? Number(start) + index : index + 1) + '.  ';
    }
    return (
      prefix + content + (node.nextSibling && !/\n$/.test(content) ? '\n' : '')
    )
  }
};

rules.indentedCodeBlock = {
  filter: function (node, options) {
    return (
      options.codeBlockStyle === 'indented' &&
      node.nodeName === 'PRE' &&
      node.firstChild &&
      node.firstChild.nodeName === 'CODE'
    )
  },

  replacement: function (content, node, options) {
    return (
      '\n\n    ' +
      node.firstChild.textContent.replace(/\n/g, '\n    ') +
      '\n\n'
    )
  }
};

rules.fencedCodeBlock = {
  filter: function (node, options) {
    return (
      options.codeBlockStyle === 'fenced' &&
      node.nodeName === 'PRE' &&
      node.firstChild &&
      node.firstChild.nodeName === 'CODE'
    )
  },

  replacement: function (content, node, options) {
    var className = node.firstChild.getAttribute('class') || '';
    var language = (className.match(/language-(\S+)/) || [null, ''])[1];
    var code = node.firstChild.textContent;

    var fenceChar = options.fence.charAt(0);
    var fenceSize = 3;
    var fenceInCodeRegex = new RegExp('^' + fenceChar + '{3,}', 'gm');

    var match;
    while ((match = fenceInCodeRegex.exec(code))) {
      if (match[0].length >= fenceSize) {
        fenceSize = match[0].length + 1;
      }
    }

    var fence = repeat(fenceChar, fenceSize);

    return (
      '\n\n' + fence + language + '\n' +
      code.replace(/\n$/, '') +
      '\n' + fence + '\n\n'
    )
  }
};

rules.horizontalRule = {
  filter: 'hr',

  replacement: function (content, node, options) {
    return '\n\n' + options.hr + '\n\n'
  }
};

rules.inlineLink = {
  filter: function (node, options) {
    return (
      options.linkStyle === 'inlined' &&
      node.nodeName === 'A' &&
      node.getAttribute('href')
    )
  },

  replacement: function (content, node) {
    var href = node.getAttribute('href');
    if (href) href = href.replace(/([()])/g, '\\$1');
    var title = cleanAttribute(node.getAttribute('title'));
    if (title) title = ' "' + title.replace(/"/g, '\\"') + '"';
    return '[' + content + '](' + href + title + ')'
  }
};

rules.referenceLink = {
  filter: function (node, options) {
    return (
      options.linkStyle === 'referenced' &&
      node.nodeName === 'A' &&
      node.getAttribute('href')
    )
  },

  replacement: function (content, node, options) {
    var href = node.getAttribute('href');
    var title = cleanAttribute(node.getAttribute('title'));
    if (title) title = ' "' + title + '"';
    var replacement;
    var reference;

    switch (options.linkReferenceStyle) {
      case 'collapsed':
        replacement = '[' + content + '][]';
        reference = '[' + content + ']: ' + href + title;
        break
      case 'shortcut':
        replacement = '[' + content + ']';
        reference = '[' + content + ']: ' + href + title;
        break
      default:
        var id = this.references.length + 1;
        replacement = '[' + content + '][' + id + ']';
        reference = '[' + id + ']: ' + href + title;
    }

    this.references.push(reference);
    return replacement
  },

  references: [],

  append: function (options) {
    var references = '';
    if (this.references.length) {
      references = '\n\n' + this.references.join('\n') + '\n\n';
      this.references = []; // Reset references
    }
    return references
  }
};

rules.emphasis = {
  filter: ['em', 'i'],

  replacement: function (content, node, options) {
    if (!content.trim()) return ''
    return options.emDelimiter + content + options.emDelimiter
  }
};

rules.strong = {
  filter: ['strong', 'b'],

  replacement: function (content, node, options) {
    if (!content.trim()) return ''
    return options.strongDelimiter + content + options.strongDelimiter
  }
};

rules.code = {
  filter: function (node) {
    var hasSiblings = node.previousSibling || node.nextSibling;
    var isCodeBlock = node.parentNode.nodeName === 'PRE' && !hasSiblings;

    return node.nodeName === 'CODE' && !isCodeBlock
  },

  replacement: function (content) {
    if (!content) return ''
    content = content.replace(/\r?\n|\r/g, ' ');

    var extraSpace = /^`|^ .*?[^ ].* $|`$/.test(content) ? ' ' : '';
    var delimiter = '`';
    var matches = content.match(/`+/gm) || [];
    while (matches.indexOf(delimiter) !== -1) delimiter = delimiter + '`';

    return delimiter + extraSpace + content + extraSpace + delimiter
  }
};

rules.image = {
  filter: 'img',

  replacement: function (content, node) {
    var alt = cleanAttribute(node.getAttribute('alt'));
    var src = node.getAttribute('src') || '';
    var title = cleanAttribute(node.getAttribute('title'));
    var titlePart = title ? ' "' + title + '"' : '';
    return src ? '![' + alt + ']' + '(' + src + titlePart + ')' : ''
  }
};

function cleanAttribute (attribute) {
  return attribute ? attribute.replace(/(\n+\s*)+/g, '\n') : ''
}

/**
 * Manages a collection of rules used to convert HTML to Markdown
 */

function Rules (options) {
  this.options = options;
  this._keep = [];
  this._remove = [];

  this.blankRule = {
    replacement: options.blankReplacement
  };

  this.keepReplacement = options.keepReplacement;

  this.defaultRule = {
    replacement: options.defaultReplacement
  };

  this.array = [];
  for (var key in options.rules) this.array.push(options.rules[key]);
}

Rules.prototype = {
  add: function (key, rule) {
    this.array.unshift(rule);
  },

  keep: function (filter) {
    this._keep.unshift({
      filter: filter,
      replacement: this.keepReplacement
    });
  },

  remove: function (filter) {
    this._remove.unshift({
      filter: filter,
      replacement: function () {
        return ''
      }
    });
  },

  forNode: function (node) {
    if (node.isBlank) return this.blankRule
    var rule;

    if ((rule = findRule(this.array, node, this.options))) return rule
    if ((rule = findRule(this._keep, node, this.options))) return rule
    if ((rule = findRule(this._remove, node, this.options))) return rule

    return this.defaultRule
  },

  forEach: function (fn) {
    for (var i = 0; i < this.array.length; i++) fn(this.array[i], i);
  }
};

function findRule (rules, node, options) {
  for (var i = 0; i < rules.length; i++) {
    var rule = rules[i];
    if (filterValue(rule, node, options)) return rule
  }
  return void 0
}

function filterValue (rule, node, options) {
  var filter = rule.filter;
  if (typeof filter === 'string') {
    if (filter === node.nodeName.toLowerCase()) return true
  } else if (Array.isArray(filter)) {
    if (filter.indexOf(node.nodeName.toLowerCase()) > -1) return true
  } else if (typeof filter === 'function') {
    if (filter.call(rule, node, options)) return true
  } else {
    throw new TypeError('`filter` needs to be a string, array, or function')
  }
}

/**
 * The collapseWhitespace function is adapted from collapse-whitespace
 * by Luc Thevenard.
 *
 * The MIT License (MIT)
 *
 * Copyright (c) 2014 Luc Thevenard <<EMAIL>>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

/**
 * collapseWhitespace(options) removes extraneous whitespace from an the given element.
 *
 * @param {Object} options
 */
function collapseWhitespace (options) {
  var element = options.element;
  var isBlock = options.isBlock;
  var isVoid = options.isVoid;
  var isPre = options.isPre || function (node) {
    return node.nodeName === 'PRE'
  };

  if (!element.firstChild || isPre(element)) return

  var prevText = null;
  var keepLeadingWs = false;

  var prev = null;
  var node = next(prev, element, isPre);

  while (node !== element) {
    if (node.nodeType === 3 || node.nodeType === 4) { // Node.TEXT_NODE or Node.CDATA_SECTION_NODE
      var text = node.data.replace(/[ \r\n\t]+/g, ' ');

      if ((!prevText || / $/.test(prevText.data)) &&
          !keepLeadingWs && text[0] === ' ') {
        text = text.substr(1);
      }

      // `text` might be empty at this point.
      if (!text) {
        node = remove(node);
        continue
      }

      node.data = text;

      prevText = node;
    } else if (node.nodeType === 1) { // Node.ELEMENT_NODE
      if (isBlock(node) || node.nodeName === 'BR') {
        if (prevText) {
          prevText.data = prevText.data.replace(/ $/, '');
        }

        prevText = null;
        keepLeadingWs = false;
      } else if (isVoid(node) || isPre(node)) {
        // Avoid trimming space around non-block, non-BR void elements and inline PRE.
        prevText = null;
        keepLeadingWs = true;
      } else if (prevText) {
        // Drop protection if set previously.
        keepLeadingWs = false;
      }
    } else {
      node = remove(node);
      continue
    }

    var nextNode = next(prev, node, isPre);
    prev = node;
    node = nextNode;
  }

  if (prevText) {
    prevText.data = prevText.data.replace(/ $/, '');
    if (!prevText.data) {
      remove(prevText);
    }
  }
}

/**
 * remove(node) removes the given node from the DOM and returns the
 * next node in the sequence.
 *
 * @param {Node} node
 * @return {Node} node
 */
function remove (node) {
  var next = node.nextSibling || node.parentNode;

  node.parentNode.removeChild(node);

  return next
}

/**
 * next(prev, current, isPre) returns the next node in the sequence, given the
 * current and previous nodes.
 *
 * @param {Node} prev
 * @param {Node} current
 * @param {Function} isPre
 * @return {Node}
 */
function next (prev, current, isPre) {
  if ((prev && prev.parentNode === current) || isPre(current)) {
    return current.nextSibling || current.parentNode
  }

  return current.firstChild || current.nextSibling || current.parentNode
}

/*
 * Set up window for Node.js
 */

var root = (typeof window !== 'undefined' ? window : {});

/*
 * Parsing HTML strings
 */

function canParseHTMLNatively () {
  var Parser = root.DOMParser;
  var canParse = false;

  // Adapted from https://gist.github.com/1129031
  // Firefox/Opera/IE throw errors on unsupported types
  try {
    // WebKit returns null on unsupported types
    if (new Parser().parseFromString('', 'text/html')) {
      canParse = true;
    }
  } catch (e) {}

  return canParse
}

function createHTMLParser () {
  var Parser = function () {};

  {
    if (shouldUseActiveX()) {
      Parser.prototype.parseFromString = function (string) {
        var doc = new window.ActiveXObject('htmlfile');
        doc.designMode = 'on'; // disable on-page scripts
        doc.open();
        doc.write(string);
        doc.close();
        return doc
      };
    } else {
      Parser.prototype.parseFromString = function (string) {
        var doc = document.implementation.createHTMLDocument('');
        doc.open();
        doc.write(string);
        doc.close();
        return doc
      };
    }
  }
  return Parser
}

function shouldUseActiveX () {
  var useActiveX = false;
  try {
    document.implementation.createHTMLDocument('').open();
  } catch (e) {
    if (root.ActiveXObject) useActiveX = true;
  }
  return useActiveX
}

var HTMLParser = canParseHTMLNatively() ? root.DOMParser : createHTMLParser();

function RootNode (input, options) {
  var root;
  if (typeof input === 'string') {
    var doc = htmlParser().parseFromString(
      // DOM parsers arrange elements in the <head> and <body>.
      // Wrapping in a custom element ensures elements are reliably arranged in
      // a single element.
      '<x-turndown id="turndown-root">' + input + '</x-turndown>',
      'text/html'
    );
    root = doc.getElementById('turndown-root');
  } else {
    root = input.cloneNode(true);
  }
  collapseWhitespace({
    element: root,
    isBlock: isBlock,
    isVoid: isVoid,
    isPre: options.preformattedCode ? isPreOrCode : null
  });

  return root
}

var _htmlParser;
function htmlParser () {
  _htmlParser = _htmlParser || new HTMLParser();
  return _htmlParser
}

function isPreOrCode (node) {
  return node.nodeName === 'PRE' || node.nodeName === 'CODE'
}

function Node (node, options) {
  node.isBlock = isBlock(node);
  node.isCode = node.nodeName === 'CODE' || node.parentNode.isCode;
  node.isBlank = isBlank(node);
  node.flankingWhitespace = flankingWhitespace(node, options);
  return node
}

function isBlank (node) {
  return (
    !isVoid(node) &&
    !isMeaningfulWhenBlank(node) &&
    /^\s*$/i.test(node.textContent) &&
    !hasVoid(node) &&
    !hasMeaningfulWhenBlank(node)
  )
}

function flankingWhitespace (node, options) {
  if (node.isBlock || (options.preformattedCode && node.isCode)) {
    return { leading: '', trailing: '' }
  }

  var edges = edgeWhitespace(node.textContent);

  // abandon leading ASCII WS if left-flanked by ASCII WS
  if (edges.leadingAscii && isFlankedByWhitespace('left', node, options)) {
    edges.leading = edges.leadingNonAscii;
  }

  // abandon trailing ASCII WS if right-flanked by ASCII WS
  if (edges.trailingAscii && isFlankedByWhitespace('right', node, options)) {
    edges.trailing = edges.trailingNonAscii;
  }

  return { leading: edges.leading, trailing: edges.trailing }
}

function edgeWhitespace (string) {
  var m = string.match(/^(([ \t\r\n]*)(\s*))(?:(?=\S)[\s\S]*\S)?((\s*?)([ \t\r\n]*))$/);
  return {
    leading: m[1], // whole string for whitespace-only strings
    leadingAscii: m[2],
    leadingNonAscii: m[3],
    trailing: m[4], // empty for whitespace-only strings
    trailingNonAscii: m[5],
    trailingAscii: m[6]
  }
}

function isFlankedByWhitespace (side, node, options) {
  var sibling;
  var regExp;
  var isFlanked;

  if (side === 'left') {
    sibling = node.previousSibling;
    regExp = / $/;
  } else {
    sibling = node.nextSibling;
    regExp = /^ /;
  }

  if (sibling) {
    if (sibling.nodeType === 3) {
      isFlanked = regExp.test(sibling.nodeValue);
    } else if (options.preformattedCode && sibling.nodeName === 'CODE') {
      isFlanked = false;
    } else if (sibling.nodeType === 1 && !isBlock(sibling)) {
      isFlanked = regExp.test(sibling.textContent);
    }
  }
  return isFlanked
}

var reduce = Array.prototype.reduce;
var escapes = [
  [/\\/g, '\\\\'],
  [/\*/g, '\\*'],
  [/^-/g, '\\-'],
  [/^\+ /g, '\\+ '],
  [/^(=+)/g, '\\$1'],
  [/^(#{1,6}) /g, '\\$1 '],
  [/`/g, '\\`'],
  [/^~~~/g, '\\~~~'],
  [/\[/g, '\\['],
  [/\]/g, '\\]'],
  [/^>/g, '\\>'],
  [/_/g, '\\_'],
  [/^(\d+)\. /g, '$1\\. ']
];

function TurndownService (options) {
  if (!(this instanceof TurndownService)) return new TurndownService(options)

  var defaults = {
    rules: rules,
    headingStyle: 'setext',
    hr: '* * *',
    bulletListMarker: '*',
    codeBlockStyle: 'indented',
    fence: '```',
    emDelimiter: '_',
    strongDelimiter: '**',
    linkStyle: 'inlined',
    linkReferenceStyle: 'full',
    br: '  ',
    preformattedCode: false,
    blankReplacement: function (content, node) {
      return node.isBlock ? '\n\n' : ''
    },
    keepReplacement: function (content, node) {
      return node.isBlock ? '\n\n' + node.outerHTML + '\n\n' : node.outerHTML
    },
    defaultReplacement: function (content, node) {
      return node.isBlock ? '\n\n' + content + '\n\n' : content
    }
  };
  this.options = extend({}, defaults, options);
  this.rules = new Rules(this.options);
}

TurndownService.prototype = {
  /**
   * The entry point for converting a string or DOM node to Markdown
   * @public
   * @param {String|HTMLElement} input The string or DOM node to convert
   * @returns A Markdown representation of the input
   * @type String
   */

  turndown: function (input) {
    if (!canConvert(input)) {
      throw new TypeError(
        input + ' is not a string, or an element/document/fragment node.'
      )
    }

    if (input === '') return ''

    var output = process.call(this, new RootNode(input, this.options));
    return postProcess.call(this, output)
  },

  /**
   * Add one or more plugins
   * @public
   * @param {Function|Array} plugin The plugin or array of plugins to add
   * @returns The Turndown instance for chaining
   * @type Object
   */

  use: function (plugin) {
    if (Array.isArray(plugin)) {
      for (var i = 0; i < plugin.length; i++) this.use(plugin[i]);
    } else if (typeof plugin === 'function') {
      plugin(this);
    } else {
      throw new TypeError('plugin must be a Function or an Array of Functions')
    }
    return this
  },

  /**
   * Adds a rule
   * @public
   * @param {String} key The unique key of the rule
   * @param {Object} rule The rule
   * @returns The Turndown instance for chaining
   * @type Object
   */

  addRule: function (key, rule) {
    this.rules.add(key, rule);
    return this
  },

  /**
   * Keep a node (as HTML) that matches the filter
   * @public
   * @param {String|Array|Function} filter The unique key of the rule
   * @returns The Turndown instance for chaining
   * @type Object
   */

  keep: function (filter) {
    this.rules.keep(filter);
    return this
  },

  /**
   * Remove a node that matches the filter
   * @public
   * @param {String|Array|Function} filter The unique key of the rule
   * @returns The Turndown instance for chaining
   * @type Object
   */

  remove: function (filter) {
    this.rules.remove(filter);
    return this
  },

  /**
   * Escapes Markdown syntax
   * @public
   * @param {String} string The string to escape
   * @returns A string with Markdown syntax escaped
   * @type String
   */

  escape: function (string) {
    return escapes.reduce(function (accumulator, escape) {
      return accumulator.replace(escape[0], escape[1])
    }, string)
  }
};

/**
 * Reduces a DOM node down to its Markdown string equivalent
 * @private
 * @param {HTMLElement} parentNode The node to convert
 * @returns A Markdown representation of the node
 * @type String
 */

function process (parentNode) {
  var self = this;
  return reduce.call(parentNode.childNodes, function (output, node) {
    node = new Node(node, self.options);

    var replacement = '';
    if (node.nodeType === 3) {
      replacement = node.isCode ? node.nodeValue : self.escape(node.nodeValue);
    } else if (node.nodeType === 1) {
      replacement = replacementForNode.call(self, node);
    }

    return join(output, replacement)
  }, '')
}

/**
 * Appends strings as each rule requires and trims the output
 * @private
 * @param {String} output The conversion output
 * @returns A trimmed version of the ouput
 * @type String
 */

function postProcess (output) {
  var self = this;
  this.rules.forEach(function (rule) {
    if (typeof rule.append === 'function') {
      output = join(output, rule.append(self.options));
    }
  });

  return output.replace(/^[\t\r\n]+/, '').replace(/[\t\r\n\s]+$/, '')
}

/**
 * Converts an element node to its Markdown equivalent
 * @private
 * @param {HTMLElement} node The node to convert
 * @returns A Markdown representation of the node
 * @type String
 */

function replacementForNode (node) {
  var rule = this.rules.forNode(node);
  var content = process.call(this, node);
  var whitespace = node.flankingWhitespace;
  if (whitespace.leading || whitespace.trailing) content = content.trim();
  return (
    whitespace.leading +
    rule.replacement(content, node, this.options) +
    whitespace.trailing
  )
}

/**
 * Joins replacement to the current output with appropriate number of new lines
 * @private
 * @param {String} output The current conversion output
 * @param {String} replacement The string to append to the output
 * @returns Joined output
 * @type String
 */

function join (output, replacement) {
  var s1 = trimTrailingNewlines(output);
  var s2 = trimLeadingNewlines(replacement);
  var nls = Math.max(output.length - s1.length, replacement.length - s2.length);
  var separator = '\n\n'.substring(0, nls);

  return s1 + separator + s2
}

/**
 * Determines whether an input can be converted
 * @private
 * @param {String|HTMLElement} input Describe this parameter
 * @returns Describe what it returns
 * @type String|Object|Array|Boolean|Number
 */

function canConvert (input) {
  return (
    input != null && (
      typeof input === 'string' ||
      (input.nodeType && (
        input.nodeType === 1 || input.nodeType === 9 || input.nodeType === 11
      ))
    )
  )
}

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TurndownService);


/***/ }),

/***/ "./node_modules/webext-redux/lib/alias/alias.js":
/*!******************************************************!*\
  !*** ./node_modules/webext-redux/lib/alias/alias.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
/**
 * Simple middleware intercepts actions and replaces with
 * another by calling an alias function with the original action
 * @type {object} aliases an object that maps action types (keys) to alias functions (values) (e.g. { SOME_ACTION: newActionAliasFunc })
 */
var _default = exports["default"] = function _default(aliases) {
  return function () {
    return function (next) {
      return function (action) {
        var alias = aliases[action.type];
        if (alias) {
          return next(alias(action));
        }
        return next(action);
      };
    };
  };
};

/***/ }),

/***/ "./node_modules/webext-redux/lib/constants/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/webext-redux/lib/constants/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.STATE_TYPE = exports.PATCH_STATE_TYPE = exports.FETCH_STATE_TYPE = exports.DISPATCH_TYPE = exports.DEFAULT_CHANNEL_NAME = void 0;
// Message type used for dispatch events
// from the Proxy Stores to background
var DISPATCH_TYPE = exports.DISPATCH_TYPE = "webext.dispatch";

// Message type for fetching current state from
// background to Proxy Stores
var FETCH_STATE_TYPE = exports.FETCH_STATE_TYPE = "webext.fetch_state";

// Message type for state update events from
// background to Proxy Stores
var STATE_TYPE = exports.STATE_TYPE = "webext.state";

// Message type for state patch events from
// background to Proxy Stores
var PATCH_STATE_TYPE = exports.PATCH_STATE_TYPE = "webext.patch_state";

// The default name for the store channel
var DEFAULT_CHANNEL_NAME = exports.DEFAULT_CHANNEL_NAME = "webext.channel";

/***/ }),

/***/ "./node_modules/webext-redux/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/webext-redux/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
var __webpack_unused_export__;


__webpack_unused_export__ = ({
  value: true
});
Object.defineProperty(exports, "Store", ({
  enumerable: true,
  get: function get() {
    return _Store["default"];
  }
}));
Object.defineProperty(exports, "alias", ({
  enumerable: true,
  get: function get() {
    return _alias["default"];
  }
}));
Object.defineProperty(exports, "applyMiddleware", ({
  enumerable: true,
  get: function get() {
    return _applyMiddleware["default"];
  }
}));
Object.defineProperty(exports, "createWrapStore", ({
  enumerable: true,
  get: function get() {
    return _wrapStore["default"];
  }
}));
var _Store = _interopRequireDefault(__webpack_require__(/*! ./store/Store */ "./node_modules/webext-redux/lib/store/Store.js"));
var _applyMiddleware = _interopRequireDefault(__webpack_require__(/*! ./store/applyMiddleware */ "./node_modules/webext-redux/lib/store/applyMiddleware.js"));
var _wrapStore = _interopRequireDefault(__webpack_require__(/*! ./wrap-store/wrapStore */ "./node_modules/webext-redux/lib/wrap-store/wrapStore.js"));
var _alias = _interopRequireDefault(__webpack_require__(/*! ./alias/alias */ "./node_modules/webext-redux/lib/alias/alias.js"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }

/***/ }),

/***/ "./node_modules/webext-redux/lib/listener.js":
/*!***************************************************!*\
  !*** ./node_modules/webext-redux/lib/listener.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.createDeferredListener = void 0;
/**
 * Returns a function that can be passed as a listener callback to a browser
 * API. The listener will queue events until setListener is called.
 *
 * @param {Function} filter - A function that filters messages to be handled by
 * the listener. This is important to avoid telling the browser to expect an
 * async response when the message is not intended for this listener.
 *
 * @example
 * const filter = (message, sender, sendResponse) => {
 *   return message.type === "my_type"
 * }
 *
 * const { listener, setListener } = createDeferredListener(filter);
 * chrome.runtime.onMessage.addListener(listener);
 *
 * // Later, define the listener to handle messages. Messages received
 * // before this point are queued.
 * setListener((message, sender, sendResponse) => {
 *  console.log(message);
 * });
 */
var createDeferredListener = exports.createDeferredListener = function createDeferredListener(filter) {
  var resolve = function resolve() {};
  var fnPromise = new Promise(function (resolve_) {
    return resolve = resolve_;
  });
  var listener = function listener(message, sender, sendResponse) {
    if (!filter(message, sender, sendResponse)) {
      return;
    }
    fnPromise.then(function (fn) {
      fn(message, sender, sendResponse);
    });

    // Allow response to be async
    return true;
  };
  return {
    setListener: resolve,
    listener: listener
  };
};

/***/ }),

/***/ "./node_modules/webext-redux/lib/serialization.js":
/*!********************************************************!*\
  !*** ./node_modules/webext-redux/lib/serialization.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.withSerializer = exports.withDeserializer = exports.noop = void 0;
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var noop = exports.noop = function noop(payload) {
  return payload;
};
var transformPayload = function transformPayload(message) {
  var transformer = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;
  return _objectSpread(_objectSpread({}, message), message.payload ? {
    payload: transformer(message.payload)
  } : {});
};
var deserializeListener = function deserializeListener(listener) {
  var deserializer = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;
  var shouldDeserialize = arguments.length > 2 ? arguments[2] : undefined;
  // If a shouldDeserialize function is passed, return a function that uses it
  // to check if any given message payload should be deserialized
  if (shouldDeserialize) {
    return function (message) {
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      if (shouldDeserialize.apply(void 0, [message].concat(args))) {
        return listener.apply(void 0, [transformPayload(message, deserializer)].concat(args));
      }
      return listener.apply(void 0, [message].concat(args));
    };
  }
  // Otherwise, return a function that tries to deserialize on every message
  return function (message) {
    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
      args[_key2 - 1] = arguments[_key2];
    }
    return listener.apply(void 0, [transformPayload(message, deserializer)].concat(args));
  };
};

/**
 * A function returned from withDeserializer that, when called, wraps addListenerFn with the
 * deserializer passed to withDeserializer.
 * @name AddListenerDeserializer
 * @function
 * @param {Function} addListenerFn The add listener function to wrap.
 * @returns {DeserializedAddListener}
 */

/**
 * A wrapped add listener function that registers the given listener.
 * @name DeserializedAddListener
 * @function
 * @param {Function} listener The listener function to register. It should expect the (optionally)
 * deserialized message as its first argument.
 * @param {Function} [shouldDeserialize] A function that takes the arguments passed to the listener
 * and returns whether the message payload should be deserialized. Not all messages (notably, messages
 * this listener doesn't care about) should be attempted to be deserialized.
 */

/**
 * Given a deserializer, returns an AddListenerDeserializer function that that takes an add listener
 * function and returns a DeserializedAddListener that automatically deserializes message payloads.
 * Each message listener is expected to take the message as its first argument.
 * @param {Function} deserializer A function that deserializes a message payload.
 * @returns {AddListenerDeserializer}
 * Example Usage:
 *   const withJsonDeserializer = withDeserializer(payload => JSON.parse(payload));
 *   const deserializedChromeListener = withJsonDeserializer(chrome.runtime.onMessage.addListener);
 *   const shouldDeserialize = (message) => message.type === 'DESERIALIZE_ME';
 *   deserializedChromeListener(message => console.log("Payload:", message.payload), shouldDeserialize);
 *   chrome.runtime.sendMessage("{'type:'DESERIALIZE_ME','payload':{'prop':4}}");
 *   //Payload: { prop: 4 };
 *   chrome.runtime.sendMessage("{'payload':{'prop':4}}");
 *   //Payload: "{'prop':4}";
 */
var withDeserializer = exports.withDeserializer = function withDeserializer() {
  var deserializer = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : noop;
  return function (addListenerFn) {
    return function (listener, shouldDeserialize) {
      return addListenerFn(deserializeListener(listener, deserializer, shouldDeserialize));
    };
  };
};

/**
 * Given a serializer, returns a function that takes a message sending
 * function as its sole argument and returns a wrapped message sender that
 * automaticaly serializes message payloads. The message sender
 * is expected to take the message as its first argument, unless messageArgIndex
 * is nonzero, in which case it is expected in the position specified by messageArgIndex.
 * @param {Function} serializer A function that serializes a message payload
 * Example Usage:
 *   const withJsonSerializer = withSerializer(payload => JSON.stringify(payload))
 *   const serializedChromeSender = withJsonSerializer(chrome.runtime.sendMessage)
 *   chrome.runtime.addListener(message => console.log("Payload:", message.payload))
 *   serializedChromeSender({ payload: { prop: 4 }})
 *   //Payload: "{'prop':4}"
 */
var withSerializer = exports.withSerializer = function withSerializer() {
  var serializer = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : noop;
  return function (sendMessageFn) {
    var messageArgIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    return function () {
      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        args[_key3] = arguments[_key3];
      }
      if (args.length <= messageArgIndex) {
        throw new Error("Message in request could not be serialized. " + "Expected message in position ".concat(messageArgIndex, " but only received ").concat(args.length, " args."));
      }
      args[messageArgIndex] = transformPayload(args[messageArgIndex], serializer);
      return sendMessageFn.apply(void 0, args);
    };
  };
};

/***/ }),

/***/ "./node_modules/webext-redux/lib/store/Store.js":
/*!******************************************************!*\
  !*** ./node_modules/webext-redux/lib/store/Store.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _lodash = _interopRequireDefault(__webpack_require__(/*! lodash.assignin */ "./node_modules/lodash.assignin/index.js"));
var _constants = __webpack_require__(/*! ../constants */ "./node_modules/webext-redux/lib/constants/index.js");
var _serialization = __webpack_require__(/*! ../serialization */ "./node_modules/webext-redux/lib/serialization.js");
var _patch = _interopRequireDefault(__webpack_require__(/*! ../strategies/shallowDiff/patch */ "./node_modules/webext-redux/lib/strategies/shallowDiff/patch.js"));
var _util = __webpack_require__(/*! ../util */ "./node_modules/webext-redux/lib/util.js");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var backgroundErrPrefix = '\nLooks like there is an error in the background page. ' + 'You might want to inspect your background page for more details.\n';
var defaultOpts = {
  channelName: _constants.DEFAULT_CHANNEL_NAME,
  state: {},
  serializer: _serialization.noop,
  deserializer: _serialization.noop,
  patchStrategy: _patch["default"]
};
var Store = /*#__PURE__*/function () {
  /**
   * Creates a new Proxy store
   * @param  {object} options
   * @param {string} options.channelName The name of the channel for this store.
   * @param {object} options.state The initial state of the store (default
   * `{}`).
   * @param {function} options.serializer A function to serialize outgoing
   * messages (default is passthrough).
   * @param {function} options.deserializer A function to deserialize incoming
   * messages (default is passthrough).
   * @param {function} options.patchStrategy A function to patch the state with
   * incoming messages. Use one of the included patching strategies or a custom
   * patching function. (default is shallow diff).
   */
  function Store() {
    var _this = this;
    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : defaultOpts,
      _ref$channelName = _ref.channelName,
      channelName = _ref$channelName === void 0 ? defaultOpts.channelName : _ref$channelName,
      _ref$state = _ref.state,
      state = _ref$state === void 0 ? defaultOpts.state : _ref$state,
      _ref$serializer = _ref.serializer,
      serializer = _ref$serializer === void 0 ? defaultOpts.serializer : _ref$serializer,
      _ref$deserializer = _ref.deserializer,
      deserializer = _ref$deserializer === void 0 ? defaultOpts.deserializer : _ref$deserializer,
      _ref$patchStrategy = _ref.patchStrategy,
      patchStrategy = _ref$patchStrategy === void 0 ? defaultOpts.patchStrategy : _ref$patchStrategy;
    _classCallCheck(this, Store);
    if (!channelName) {
      throw new Error('channelName is required in options');
    }
    if (typeof serializer !== 'function') {
      throw new Error('serializer must be a function');
    }
    if (typeof deserializer !== 'function') {
      throw new Error('deserializer must be a function');
    }
    if (typeof patchStrategy !== 'function') {
      throw new Error('patchStrategy must be one of the included patching strategies or a custom patching function');
    }
    this.channelName = channelName;
    this.readyResolved = false;
    this.readyPromise = new Promise(function (resolve) {
      return _this.readyResolve = resolve;
    });
    this.browserAPI = (0, _util.getBrowserAPI)();
    this.initializeStore = this.initializeStore.bind(this);

    // We request the latest available state data to initialise our store
    this.browserAPI.runtime.sendMessage({
      type: _constants.FETCH_STATE_TYPE,
      channelName: channelName
    }, undefined, this.initializeStore);
    this.deserializer = deserializer;
    this.serializedPortListener = (0, _serialization.withDeserializer)(deserializer)(function () {
      var _this$browserAPI$runt;
      return (_this$browserAPI$runt = _this.browserAPI.runtime.onMessage).addListener.apply(_this$browserAPI$runt, arguments);
    });
    this.serializedMessageSender = (0, _serialization.withSerializer)(serializer)(function () {
      var _this$browserAPI$runt2;
      return (_this$browserAPI$runt2 = _this.browserAPI.runtime).sendMessage.apply(_this$browserAPI$runt2, arguments);
    });
    this.listeners = [];
    this.state = state;
    this.patchStrategy = patchStrategy;

    /**
     * Determine if the message should be run through the deserializer. We want
     * to skip processing messages that probably didn't come from this library.
     * Note that the listener below is still called for each message so it needs
     * its own guard, the shouldDeserialize predicate only skips _deserializing_
     * the message.
     */
    var shouldDeserialize = function shouldDeserialize(message) {
      return Boolean(message) && typeof message.type === "string" && message.channelName === _this.channelName;
    };
    this.serializedPortListener(function (message) {
      if (!message || message.channelName !== _this.channelName) {
        return;
      }
      switch (message.type) {
        case _constants.STATE_TYPE:
          _this.replaceState(message.payload);
          if (!_this.readyResolved) {
            _this.readyResolved = true;
            _this.readyResolve();
          }
          break;
        case _constants.PATCH_STATE_TYPE:
          _this.patchState(message.payload);
          break;
        default:
        // do nothing
      }
    }, shouldDeserialize);
    this.dispatch = this.dispatch.bind(this); // add this context to dispatch
    this.getState = this.getState.bind(this); // add this context to getState
    this.subscribe = this.subscribe.bind(this); // add this context to subscribe
  }

  /**
  * Returns a promise that resolves when the store is ready. Optionally a callback may be passed in instead.
  * @param [function] callback An optional callback that may be passed in and will fire when the store is ready.
  * @return {object} promise A promise that resolves when the store has established a connection with the background page.
  */
  return _createClass(Store, [{
    key: "ready",
    value: function ready() {
      var cb = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      if (cb !== null) {
        return this.readyPromise.then(cb);
      }
      return this.readyPromise;
    }

    /**
     * Subscribes a listener function for all state changes
     * @param  {function} listener A listener function to be called when store state changes
     * @return {function}          An unsubscribe function which can be called to remove the listener from state updates
     */
  }, {
    key: "subscribe",
    value: function subscribe(listener) {
      var _this2 = this;
      this.listeners.push(listener);
      return function () {
        _this2.listeners = _this2.listeners.filter(function (l) {
          return l !== listener;
        });
      };
    }

    /**
     * Replaces the state for only the keys in the updated state. Notifies all listeners of state change.
     * @param {object} state the new (partial) redux state
     */
  }, {
    key: "patchState",
    value: function patchState(difference) {
      this.state = this.patchStrategy(this.state, difference);
      this.listeners.forEach(function (l) {
        return l();
      });
    }

    /**
     * Replace the current state with a new state. Notifies all listeners of state change.
     * @param  {object} state The new state for the store
     */
  }, {
    key: "replaceState",
    value: function replaceState(state) {
      this.state = state;
      this.listeners.forEach(function (l) {
        return l();
      });
    }

    /**
     * Get the current state of the store
     * @return {object} the current store state
     */
  }, {
    key: "getState",
    value: function getState() {
      return this.state;
    }

    /**
     * Stub function to stay consistent with Redux Store API. No-op.
     */
  }, {
    key: "replaceReducer",
    value: function replaceReducer() {
      return;
    }

    /**
     * Dispatch an action to the background using messaging passing
     * @param  {object} data The action data to dispatch
     * @return {Promise}     Promise that will resolve/reject based on the action response from the background
     */
  }, {
    key: "dispatch",
    value: function dispatch(data) {
      var _this3 = this;
      return new Promise(function (resolve, reject) {
        _this3.serializedMessageSender({
          type: _constants.DISPATCH_TYPE,
          channelName: _this3.channelName,
          payload: data
        }, null, function (resp) {
          if (!resp) {
            var _error = _this3.browserAPI.runtime.lastError;
            var bgErr = new Error("".concat(backgroundErrPrefix).concat(_error));
            reject((0, _lodash["default"])(bgErr, _error));
            return;
          }
          var error = resp.error,
            value = resp.value;
          if (error) {
            var _bgErr = new Error("".concat(backgroundErrPrefix).concat(error));
            reject((0, _lodash["default"])(_bgErr, error));
          } else {
            resolve(value && value.payload);
          }
        });
      });
    }
  }, {
    key: "initializeStore",
    value: function initializeStore(message) {
      if (message && message.type === _constants.FETCH_STATE_TYPE) {
        this.replaceState(message.payload);

        // Resolve if readyPromise has not been resolved.
        if (!this.readyResolved) {
          this.readyResolved = true;
          this.readyResolve();
        }
      }
    }
  }]);
}();
var _default = exports["default"] = Store;

/***/ }),

/***/ "./node_modules/webext-redux/lib/store/applyMiddleware.js":
/*!****************************************************************!*\
  !*** ./node_modules/webext-redux/lib/store/applyMiddleware.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = applyMiddleware;
function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
// Function taken from redux source
// https://github.com/reactjs/redux/blob/master/src/compose.js
function compose() {
  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {
    funcs[_key] = arguments[_key];
  }
  if (funcs.length === 0) {
    return function (arg) {
      return arg;
    };
  }
  if (funcs.length === 1) {
    return funcs[0];
  }
  return funcs.reduce(function (a, b) {
    return function () {
      return a(b.apply(void 0, arguments));
    };
  });
}

// Based on redux implementation of applyMiddleware to support all standard
// redux middlewares
function applyMiddleware(store) {
  for (var _len2 = arguments.length, middlewares = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
    middlewares[_key2 - 1] = arguments[_key2];
  }
  var _dispatch = function dispatch() {
    throw new Error('Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');
  };
  var middlewareAPI = {
    getState: store.getState.bind(store),
    dispatch: function dispatch() {
      return _dispatch.apply(void 0, arguments);
    }
  };
  middlewares = (middlewares || []).map(function (middleware) {
    return middleware(middlewareAPI);
  });
  _dispatch = compose.apply(void 0, _toConsumableArray(middlewares))(store.dispatch);
  store.dispatch = _dispatch;
  return store;
}

/***/ }),

/***/ "./node_modules/webext-redux/lib/strategies/constants.js":
/*!***************************************************************!*\
  !*** ./node_modules/webext-redux/lib/strategies/constants.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.DIFF_STATUS_UPDATED = exports.DIFF_STATUS_REMOVED = exports.DIFF_STATUS_KEYS_UPDATED = exports.DIFF_STATUS_ARRAY_UPDATED = void 0;
// The `change` value for updated or inserted fields resulting from shallow diff
var DIFF_STATUS_UPDATED = exports.DIFF_STATUS_UPDATED = 'updated';

// The `change` value for removed fields resulting from shallow diff
var DIFF_STATUS_REMOVED = exports.DIFF_STATUS_REMOVED = 'removed';
var DIFF_STATUS_KEYS_UPDATED = exports.DIFF_STATUS_KEYS_UPDATED = 'updated_keys';
var DIFF_STATUS_ARRAY_UPDATED = exports.DIFF_STATUS_ARRAY_UPDATED = 'updated_array';

/***/ }),

/***/ "./node_modules/webext-redux/lib/strategies/shallowDiff/diff.js":
/*!**********************************************************************!*\
  !*** ./node_modules/webext-redux/lib/strategies/shallowDiff/diff.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = shallowDiff;
var _constants = __webpack_require__(/*! ../constants */ "./node_modules/webext-redux/lib/strategies/constants.js");
/**
 * Returns a new Object containing only the fields in `new` that differ from `old`
 *
 * @param {Object} old
 * @param {Object} new
 * @return {Array} An array of changes. The changes have a `key`, `value`, and `change`.
 *   The change is either `updated`, which is if the value has changed or been added,
 *   or `removed`.
 */
function shallowDiff(oldObj, newObj) {
  var difference = [];
  Object.keys(newObj).forEach(function (key) {
    if (oldObj[key] !== newObj[key]) {
      difference.push({
        key: key,
        value: newObj[key],
        change: _constants.DIFF_STATUS_UPDATED
      });
    }
  });
  Object.keys(oldObj).forEach(function (key) {
    if (!newObj.hasOwnProperty(key)) {
      difference.push({
        key: key,
        change: _constants.DIFF_STATUS_REMOVED
      });
    }
  });
  return difference;
}

/***/ }),

/***/ "./node_modules/webext-redux/lib/strategies/shallowDiff/patch.js":
/*!***********************************************************************!*\
  !*** ./node_modules/webext-redux/lib/strategies/shallowDiff/patch.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = _default;
var _constants = __webpack_require__(/*! ../constants */ "./node_modules/webext-redux/lib/strategies/constants.js");
function _default(obj, difference) {
  var newObj = Object.assign({}, obj);
  difference.forEach(function (_ref) {
    var change = _ref.change,
      key = _ref.key,
      value = _ref.value;
    switch (change) {
      case _constants.DIFF_STATUS_UPDATED:
        newObj[key] = value;
        break;
      case _constants.DIFF_STATUS_REMOVED:
        Reflect.deleteProperty(newObj, key);
        break;
      default:
      // do nothing
    }
  });
  return newObj;
}

/***/ }),

/***/ "./node_modules/webext-redux/lib/util.js":
/*!***********************************************!*\
  !*** ./node_modules/webext-redux/lib/util.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports.getBrowserAPI = getBrowserAPI;
/**
 * Looks for a global browser api, first checking the chrome namespace and then
 * checking the browser namespace. If no appropriate namespace is present, this
 * function will throw an error.
 */
function getBrowserAPI() {
  var api;
  try {
    // eslint-disable-next-line no-undef
    api = self.chrome || self.browser || browser;
  } catch (error) {
    // eslint-disable-next-line no-undef
    api = browser;
  }
  if (!api) {
    throw new Error("Browser API is not present");
  }
  return api;
}

/***/ }),

/***/ "./node_modules/webext-redux/lib/wrap-store/wrapStore.js":
/*!***************************************************************!*\
  !*** ./node_modules/webext-redux/lib/wrap-store/wrapStore.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";


Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
var _constants = __webpack_require__(/*! ../constants */ "./node_modules/webext-redux/lib/constants/index.js");
var _serialization = __webpack_require__(/*! ../serialization */ "./node_modules/webext-redux/lib/serialization.js");
var _util = __webpack_require__(/*! ../util */ "./node_modules/webext-redux/lib/util.js");
var _diff = _interopRequireDefault(__webpack_require__(/*! ../strategies/shallowDiff/diff */ "./node_modules/webext-redux/lib/strategies/shallowDiff/diff.js"));
var _listener = __webpack_require__(/*! ../listener */ "./node_modules/webext-redux/lib/listener.js");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { "default": e }; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t["return"] || t["return"](); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
/**
 * Responder for promisified results
 * @param  {object} dispatchResult The result from `store.dispatch()`
 * @param  {function} send         The function used to respond to original message
 * @return {undefined}
 */
var promiseResponder = function promiseResponder(dispatchResult, send) {
  Promise.resolve(dispatchResult).then(function (res) {
    send({
      error: null,
      value: res
    });
  })["catch"](function (err) {
    console.error("error dispatching result:", err);
    send({
      error: err.message,
      value: null
    });
  });
};
var defaultOpts = {
  channelName: _constants.DEFAULT_CHANNEL_NAME,
  dispatchResponder: promiseResponder,
  serializer: _serialization.noop,
  deserializer: _serialization.noop,
  diffStrategy: _diff["default"]
};

/**
 * @typedef {function} WrapStore
 * @param {Object} store A Redux store
 * @param {Object} options
 * @param {function} options.dispatchResponder A function that takes the result
 * of a store dispatch and optionally implements custom logic for responding to
 * the original dispatch message.
 * @param {function} options.serializer A function to serialize outgoing message
 * payloads (default is passthrough).
 * @param {function} options.deserializer A function to deserialize incoming
 * message payloads (default is passthrough).
 * @param {function} options.diffStrategy A function to diff the previous state
 * and the new state (default is shallow diff).
 */

/**
 * Wraps a Redux store so that proxy stores can connect to it. This function
 * must be called synchronously when the extension loads to avoid dropping
 * messages that woke the service worker.
 * @param {Object} options
 * @param {string} options.channelName The name of the channel for this store.
 * @return {WrapStore} The wrapStore function that accepts a Redux store and
 * options. See {@link WrapStore}.
 */
var _default = exports["default"] = function _default() {
  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : defaultOpts,
    _ref$channelName = _ref.channelName,
    channelName = _ref$channelName === void 0 ? defaultOpts.channelName : _ref$channelName;
  var browserAPI = (0, _util.getBrowserAPI)();
  var filterStateMessages = function filterStateMessages(message) {
    return message.type === _constants.FETCH_STATE_TYPE && message.channelName === channelName;
  };
  var filterActionMessages = function filterActionMessages(message) {
    return message.type === _constants.DISPATCH_TYPE && message.channelName === channelName;
  };

  // Setup message listeners synchronously to avoid dropping messages if the
  // extension is woken by a message.
  var stateProviderListener = (0, _listener.createDeferredListener)(filterStateMessages);
  var actionListener = (0, _listener.createDeferredListener)(filterActionMessages);
  browserAPI.runtime.onMessage.addListener(stateProviderListener.listener);
  browserAPI.runtime.onMessage.addListener(actionListener.listener);
  return function (store) {
    var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultOpts,
      _ref2$dispatchRespond = _ref2.dispatchResponder,
      dispatchResponder = _ref2$dispatchRespond === void 0 ? defaultOpts.dispatchResponder : _ref2$dispatchRespond,
      _ref2$serializer = _ref2.serializer,
      serializer = _ref2$serializer === void 0 ? defaultOpts.serializer : _ref2$serializer,
      _ref2$deserializer = _ref2.deserializer,
      deserializer = _ref2$deserializer === void 0 ? defaultOpts.deserializer : _ref2$deserializer,
      _ref2$diffStrategy = _ref2.diffStrategy,
      diffStrategy = _ref2$diffStrategy === void 0 ? defaultOpts.diffStrategy : _ref2$diffStrategy;
    if (typeof serializer !== "function") {
      throw new Error("serializer must be a function");
    }
    if (typeof deserializer !== "function") {
      throw new Error("deserializer must be a function");
    }
    if (typeof diffStrategy !== "function") {
      throw new Error("diffStrategy must be one of the included diffing strategies or a custom diff function");
    }

    /**
     * Respond to dispatches from UI components
     */
    var dispatchResponse = function dispatchResponse(request, sender, sendResponse) {
      //  Only called with messages that pass the filterActionMessages filter.
      var action = Object.assign({}, request.payload, {
        _sender: sender
      });
      var dispatchResult = null;
      try {
        dispatchResult = store.dispatch(action);
      } catch (e) {
        dispatchResult = Promise.reject(e.message);
        console.error(e);
      }
      dispatchResponder(dispatchResult, sendResponse);
    };

    /**
     * Setup for state updates
     */
    var serializedMessagePoster = (0, _serialization.withSerializer)(serializer)(function () {
      var _browserAPI$runtime;
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      var onErrorCallback = function onErrorCallback() {
        if (browserAPI.runtime.lastError) {
          // do nothing - errors can be present
          // if no content script exists on receiver
        }
      };
      (_browserAPI$runtime = browserAPI.runtime).sendMessage.apply(_browserAPI$runtime, args.concat([onErrorCallback]));
      // We will broadcast state changes to all tabs to sync state across content scripts
      return browserAPI.tabs.query({}, function (tabs) {
        var _iterator = _createForOfIteratorHelper(tabs),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var _browserAPI$tabs;
            var tab = _step.value;
            (_browserAPI$tabs = browserAPI.tabs).sendMessage.apply(_browserAPI$tabs, [tab.id].concat(args, [onErrorCallback]));
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
      });
    });
    var currentState = store.getState();
    var patchState = function patchState() {
      var newState = store.getState();
      var diff = diffStrategy(currentState, newState);
      if (diff.length) {
        currentState = newState;
        serializedMessagePoster({
          type: _constants.PATCH_STATE_TYPE,
          payload: diff,
          channelName: channelName // Notifying what store is broadcasting the state changes
        });
      }
    };

    // Send patched state to listeners on every redux store state change
    store.subscribe(patchState);

    // Send store's initial state
    serializedMessagePoster({
      type: _constants.STATE_TYPE,
      payload: currentState,
      channelName: channelName // Notifying what store is broadcasting the state changes
    });

    /**
     * State provider for content-script initialization
     */
    stateProviderListener.setListener(function (request, sender, sendResponse) {
      // This listener is only called with messages that pass filterStateMessages
      var state = store.getState();
      sendResponse({
        type: _constants.FETCH_STATE_TYPE,
        payload: state
      });
    });

    /**
     * Setup action handler
     */
    var withPayloadDeserializer = (0, _serialization.withDeserializer)(deserializer);
    withPayloadDeserializer(actionListener.setListener)(dispatchResponse, filterActionMessages);
  };
};

/***/ }),

/***/ "./src/state/slices/content.ts":
/*!*************************************!*\
  !*** ./src/state/slices/content.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   aliases: () => (/* binding */ aliases),
/* harmony export */   contentLoaded: () => (/* binding */ contentLoaded),
/* harmony export */   reducer: () => (/* binding */ reducer)
/* harmony export */ });
/* unused harmony exports contentDefaultState, actions */
/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ "./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs");

const contentDefaultState = {
    isLoaded: false,
};
const slice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({
    name: 'content',
    initialState: contentDefaultState,
    reducers: {
        reset: () => contentDefaultState,
        contentLoaded: state => {
            state.isLoaded = true;
        },
    }
});
/**
 * this is an example of a thunk, you could add api requests from here
 * and dispatch actions to update the state
 */
const contentLoaded = () => async (dispatch, getState) => {
    const { isLoaded } = getState().content || {};
    if (isLoaded)
        return;
    await dispatch(slice.actions.contentLoaded());
};
const { actions, reducer } = slice;
const aliases = {};



/***/ }),

/***/ "./src/state/slices/sidePanel.ts":
/*!***************************************!*\
  !*** ./src/state/slices/sidePanel.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   aliases: () => (/* binding */ aliases),
/* harmony export */   reducer: () => (/* binding */ reducer)
/* harmony export */ });
/* unused harmony exports ModalConfirmActions, ModalCancelActions, sidePanelDefaultState, actions */
/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ "./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs");

var ModalConfirmActions;
(function (ModalConfirmActions) {
    ModalConfirmActions["Default"] = "default";
    ModalConfirmActions["ConfirmDeleteCard"] = "confirmDeleteCard";
})(ModalConfirmActions || (ModalConfirmActions = {}));
var ModalCancelActions;
(function (ModalCancelActions) {
    ModalCancelActions["Default"] = "default";
})(ModalCancelActions || (ModalCancelActions = {}));
const sidePanelDefaultState = { isOpen: false };
const slice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({
    name: 'sidePanel',
    initialState: sidePanelDefaultState,
    reducers: { reset: () => sidePanelDefaultState }
});
const { actions, reducer } = slice;
const aliases = {};



/***/ }),

/***/ "./src/state/store.ts":
/*!****************************!*\
  !*** ./src/state/store.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   createStoreProxy: () => (/* binding */ createStoreProxy)
/* harmony export */ });
/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ "./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs");
/* harmony import */ var redux_logger__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redux-logger */ "./node_modules/redux-logger/dist/redux-logger.js");
/* harmony import */ var redux_logger__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(redux_logger__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var redux_thunk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! redux-thunk */ "./node_modules/redux-thunk/dist/redux-thunk.mjs");
/* harmony import */ var webext_redux__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! webext-redux */ "./node_modules/webext-redux/lib/index.js");
/* harmony import */ var src_state_slices_content__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! src/state/slices/content */ "./src/state/slices/content.ts");
/* harmony import */ var src_state_slices_sidePanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! src/state/slices/sidePanel */ "./src/state/slices/sidePanel.ts");






const backgroundAliases = { ...src_state_slices_sidePanel__WEBPACK_IMPORTED_MODULE_5__.aliases, ...src_state_slices_content__WEBPACK_IMPORTED_MODULE_4__.aliases };
const middleware = [
    (0,webext_redux__WEBPACK_IMPORTED_MODULE_3__.alias)(backgroundAliases),
    redux_thunk__WEBPACK_IMPORTED_MODULE_2__.thunk,
    (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSerializableStateInvariantMiddleware)(),
    redux_logger__WEBPACK_IMPORTED_MODULE_1__.logger
];
const middlewareForProxy = [
    (0,webext_redux__WEBPACK_IMPORTED_MODULE_3__.alias)(backgroundAliases),
    redux_thunk__WEBPACK_IMPORTED_MODULE_2__.thunk,
    (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSerializableStateInvariantMiddleware)(),
    redux_logger__WEBPACK_IMPORTED_MODULE_1__.logger,
];
const additionalMiddlewareForConfigureStore = [
    (0,webext_redux__WEBPACK_IMPORTED_MODULE_3__.alias)(backgroundAliases),
    redux_logger__WEBPACK_IMPORTED_MODULE_1__.logger,
];
const buildStoreWithDefaults = ({ channelName } = {}) => {
    const reducer = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.combineReducers)({
        sidePanel: src_state_slices_sidePanel__WEBPACK_IMPORTED_MODULE_5__.reducer,
        content: src_state_slices_content__WEBPACK_IMPORTED_MODULE_4__.reducer
    });
    const store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.configureStore)({
        devTools: true,
        reducer,
        middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(additionalMiddlewareForConfigureStore),
    });
    if (channelName) {
        const specificWrapStore = (0,webext_redux__WEBPACK_IMPORTED_MODULE_3__.createWrapStore)({ channelName });
        specificWrapStore(store);
    }
    return store;
};
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (buildStoreWithDefaults);
const createStoreProxy = (channelName) => {
    const store = new webext_redux__WEBPACK_IMPORTED_MODULE_3__.Store({ channelName });
    (0,webext_redux__WEBPACK_IMPORTED_MODULE_3__.applyMiddleware)(store, ...middlewareForProxy);
    return store;
};


/***/ }),

/***/ "./src/types/ChannelNames.ts":
/*!***********************************!*\
  !*** ./src/types/ChannelNames.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
var ChannelNames;
(function (ChannelNames) {
    ChannelNames["ContentPort"] = "content";
    ChannelNames["SidePanelPort"] = "sidePanel";
    ChannelNames["SAVE_NOTE_TO_FILE"] = "save-note-to-file";
})(ChannelNames || (ChannelNames = {}));
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChannelNames);


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be in strict mode.
(() => {
"use strict";
/*!*******************************!*\
  !*** ./src/content/index.tsx ***!
  \*******************************/
/* harmony import */ var src_state_slices_content__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! src/state/slices/content */ "./src/state/slices/content.ts");
/* harmony import */ var src_state_store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! src/state/store */ "./src/state/store.ts");
/* harmony import */ var _types_ChannelNames__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../types/ChannelNames */ "./src/types/ChannelNames.ts");
/* harmony import */ var defuddle__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! defuddle */ "./node_modules/defuddle/dist/index.js");
/* harmony import */ var defuddle__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(defuddle__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var turndown_plugin_gfm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! turndown-plugin-gfm */ "./node_modules/turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js");
/* harmony import */ var turndown__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! turndown */ "./node_modules/turndown/lib/turndown.browser.es.js");






(async () => {
    try {
        if (window.location.protocol === 'chrome:' ||
            window.location.protocol === 'chrome-extension:') {
            return;
        }
        const store = (0,src_state_store__WEBPACK_IMPORTED_MODULE_1__.createStoreProxy)(_types_ChannelNames__WEBPACK_IMPORTED_MODULE_2__["default"].ContentPort);
        try {
            await store.ready();
            store.dispatch((0,src_state_slices_content__WEBPACK_IMPORTED_MODULE_0__.contentLoaded)());
        }
        catch (initError) {
            console.error('ChromePanion - Content script store init error:', initError);
        }
    }
    catch (err) {
        console.error('ChromePanion - Content script main initialization error:', err);
    }
})();
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.type === 'DEFUDDLE_PAGE_CONTENT') {
        let turndownService = null;
        try {
            if (document.contentType === 'application/pdf') {
                sendResponse({
                    success: false,
                    error: 'Cannot defuddle PDF content directly. Please save or copy text manually.',
                    title: document.title || 'PDF Document'
                });
                return true;
            }
            if (typeof (defuddle__WEBPACK_IMPORTED_MODULE_3___default()) === 'undefined') {
                sendResponse({ success: false, error: 'Defuddle library not available.', title: document.title });
                return true;
            }
            const defuddleInstance = new (defuddle__WEBPACK_IMPORTED_MODULE_3___default())(document, {
                markdown: false,
                url: document.location.href
            });
            const defuddleResult = defuddleInstance.parse();
            if (typeof turndown__WEBPACK_IMPORTED_MODULE_5__["default"] === 'undefined') {
                sendResponse({ success: false, error: 'TurndownService library not available.', title: document.title });
                return true;
            }
            turndownService = new turndown__WEBPACK_IMPORTED_MODULE_5__["default"]({ headingStyle: 'atx', codeBlockStyle: 'fenced' })
                .use(turndown_plugin_gfm__WEBPACK_IMPORTED_MODULE_4__.gfm);
            const markdownContent = turndownService.turndown(defuddleResult.content || '');
            const firstHeading = document.querySelector('h1, h2, h3')?.textContent?.trim();
            const fallbackTitle = document.title || 'Untitled Note';
            sendResponse({
                success: true,
                title: firstHeading || defuddleResult.title || fallbackTitle,
                content: markdownContent
            });
        }
        catch (error) {
            sendResponse({ success: false, error: error.message, title: document.title });
        }
        return true;
    }
});

})();

/******/ })()
;
//# sourceMappingURL=content.js.map