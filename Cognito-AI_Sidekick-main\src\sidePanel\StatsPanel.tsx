import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { FiBarChart2 } from './icons';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/src/background/util';
import type { QueryStats } from '../types/config';

interface StatsPanelProps {
  stats: QueryStats | null;
  isVisible: boolean;
  onToggle: () => void;
  className?: string;
}

export const StatsPanel: React.FC<StatsPanelProps> = ({
  stats,
  isVisible,
  onToggle,
  className
}) => {
  if (!stats) return null;

  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatTokensPerSecond = (tps: number): string => {
    return tps > 0 ? `${tps.toFixed(1)} tokens/s` : '0 tokens/s';
  };

  return (
    <div className={cn("flex flex-col items-center gap-2", className)}>
      {/* Stats Toggle Icon */}
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className={cn(
              "h-6 w-6 p-0 rounded-md transition-all duration-200",
              "text-muted-foreground hover:text-foreground",
              "hover:bg-muted/50",
              isVisible && "text-foreground bg-muted/30"
            )}
          >
            <FiBarChart2 className="h-3 w-3" />
          </Button>
        </TooltipTrigger>
        <TooltipContent side="top" className="bg-popover text-popover-foreground border border-border">
          <p className="text-apple-footnote">
            {isVisible ? 'Hide query stats' : 'Show query stats'}
          </p>
        </TooltipContent>
      </Tooltip>

      {/* Stats Panel */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className={cn(
              "w-full max-w-md bg-card border border-border rounded-xl shadow-lg",
              "backdrop-blur-sm p-4 space-y-3"
            )}
          >
            {/* Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-apple-body font-medium text-foreground">
                Query Statistics
              </h3>
              <div className="text-apple-footnote text-muted-foreground">
                {formatTime(stats.processingTimeMs)}
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-3">
              {/* Tokens */}
              <div className="space-y-1">
                <div className="text-apple-footnote text-muted-foreground">
                  Tokens Generated
                </div>
                <div className="text-apple-body font-medium text-foreground">
                  {stats.totalTokens.toLocaleString()}
                </div>
              </div>

              {/* Speed */}
              <div className="space-y-1">
                <div className="text-apple-footnote text-muted-foreground">
                  Generation Speed
                </div>
                <div className="text-apple-body font-medium text-foreground">
                  {formatTokensPerSecond(stats.tokensPerSecond)}
                </div>
              </div>
            </div>

            {/* URLs Section */}
            {stats.searchUrls.length > 0 ? (
              <div className="space-y-2 pt-1 border-t border-border/50">
                <div className="text-apple-footnote text-muted-foreground">
                  Sources Searched ({stats.searchUrls.length})
                </div>
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {stats.searchUrls.map((url, index) => (
                    <div
                      key={index}
                      className={cn(
                        "text-apple-footnote text-muted-foreground/80",
                        "truncate hover:text-muted-foreground transition-colors",
                        "cursor-pointer"
                      )}
                      title={url}
                      onClick={() => window.open(url, '_blank')}
                    >
                      {url}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="space-y-2 pt-1 border-t border-border/50">
                <div className="text-apple-footnote text-muted-foreground">
                  No web sources searched
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};
