{"version": 3, "file": "content.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAoDA;AACA;;;;;;;;;;AC7uEA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAqBA;AACA;;;;;;;;;;AC9sCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACjnBA;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAIA;;;;;;;;;;;;;;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAWA;AACA;;;;;;;;;;;;;;;;AC5YA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAUA;AACA;;;;;;;;;;;;;;;ACtuBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACzJA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACx8BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AChDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACxHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AC3PA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACtDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACpCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACzBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACxBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACrMA;AAOA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AAEA;AAAA;AAEA;AACA;AAEA;AACA;AAEA;;;;;;;;;;;;;;;;;;ACrCA;AAEA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AAMA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;AC1BA;AAQA;AACA;AACA;AAIA;AACA;AAUA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AAEA;AAEA;AACA;;;;;;;;;;;;;;;AC3EA;AAAA;AACA;AACA;AACA;AACA;AAEA;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;ACvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;ACPA;;;;;;;;;;;;;;;;;;ACAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AAGA;AAGA;AACA;AAEA;AAEA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AAEA", "sources": ["webpack://chromepanion/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "webpack://chromepanion/./node_modules/defuddle/dist/index.js", "webpack://chromepanion/./node_modules/immer/dist/immer.mjs", "webpack://chromepanion/./node_modules/lodash.assignin/index.js", "webpack://chromepanion/./node_modules/redux-logger/dist/redux-logger.js", "webpack://chromepanion/./node_modules/redux-thunk/dist/redux-thunk.mjs", "webpack://chromepanion/./node_modules/redux/dist/redux.mjs", "webpack://chromepanion/./node_modules/reselect/dist/reselect.mjs", "webpack://chromepanion/./node_modules/turndown-plugin-gfm/lib/turndown-plugin-gfm.es.js", "webpack://chromepanion/./node_modules/turndown/lib/turndown.browser.es.js", "webpack://chromepanion/./node_modules/webext-redux/lib/alias/alias.js", "webpack://chromepanion/./node_modules/webext-redux/lib/constants/index.js", "webpack://chromepanion/./node_modules/webext-redux/lib/index.js", "webpack://chromepanion/./node_modules/webext-redux/lib/listener.js", "webpack://chromepanion/./node_modules/webext-redux/lib/serialization.js", "webpack://chromepanion/./node_modules/webext-redux/lib/store/Store.js", "webpack://chromepanion/./node_modules/webext-redux/lib/store/applyMiddleware.js", "webpack://chromepanion/./node_modules/webext-redux/lib/strategies/constants.js", "webpack://chromepanion/./node_modules/webext-redux/lib/strategies/shallowDiff/diff.js", "webpack://chromepanion/./node_modules/webext-redux/lib/strategies/shallowDiff/patch.js", "webpack://chromepanion/./node_modules/webext-redux/lib/util.js", "webpack://chromepanion/./node_modules/webext-redux/lib/wrap-store/wrapStore.js", "webpack://chromepanion/./src/state/slices/content.ts", "webpack://chromepanion/./src/state/slices/sidePanel.ts", "webpack://chromepanion/./src/state/store.ts", "webpack://chromepanion/./src/types/ChannelNames.ts", "webpack://chromepanion/webpack/bootstrap", "webpack://chromepanion/webpack/runtime/compat get default export", "webpack://chromepanion/webpack/runtime/define property getters", "webpack://chromepanion/webpack/runtime/global", "webpack://chromepanion/webpack/runtime/hasOwnProperty shorthand", "webpack://chromepanion/./src/content/index.tsx"], "sourcesContent": ["// src/index.ts\nexport * from \"redux\";\nimport { produce, current as current3, freeze, original as original2, isDraft as isDraft5 } from \"immer\";\nimport { createSelector, createSelectorCreator as createSelectorCreator2, lruMemoize, weakMapMemoize as weakMapMemoize2 } from \"reselect\";\n\n// src/createDraftSafeSelector.ts\nimport { current, isDraft } from \"immer\";\nimport { createSelectorCreator, weakMapMemoize } from \"reselect\";\nvar createDraftSafeSelectorCreator = (...args) => {\n  const createSelector2 = createSelectorCreator(...args);\n  const createDraftSafeSelector2 = Object.assign((...args2) => {\n    const selector = createSelector2(...args2);\n    const wrappedSelector = (value, ...rest) => selector(isDraft(value) ? current(value) : value, ...rest);\n    Object.assign(wrappedSelector, selector);\n    return wrappedSelector;\n  }, {\n    withTypes: () => createDraftSafeSelector2\n  });\n  return createDraftSafeSelector2;\n};\nvar createDraftSafeSelector = /* @__PURE__ */ createDraftSafeSelectorCreator(weakMapMemoize);\n\n// src/configureStore.ts\nimport { applyMiddleware, createStore, compose as compose2, combineReducers, isPlainObject as isPlainObject2 } from \"redux\";\n\n// src/devtoolsExtension.ts\nimport { compose } from \"redux\";\nvar composeWithDevTools = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ : function() {\n  if (arguments.length === 0) return void 0;\n  if (typeof arguments[0] === \"object\") return compose;\n  return compose.apply(null, arguments);\n};\nvar devToolsEnhancer = typeof window !== \"undefined\" && window.__REDUX_DEVTOOLS_EXTENSION__ ? window.__REDUX_DEVTOOLS_EXTENSION__ : function() {\n  return function(noop3) {\n    return noop3;\n  };\n};\n\n// src/getDefaultMiddleware.ts\nimport { thunk as thunkMiddleware, withExtraArgument } from \"redux-thunk\";\n\n// src/createAction.ts\nimport { isAction } from \"redux\";\n\n// src/tsHelpers.ts\nvar hasMatchFunction = (v) => {\n  return v && typeof v.match === \"function\";\n};\n\n// src/createAction.ts\nfunction createAction(type, prepareAction) {\n  function actionCreator(...args) {\n    if (prepareAction) {\n      let prepared = prepareAction(...args);\n      if (!prepared) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : \"prepareAction did not return an object\");\n      }\n      return {\n        type,\n        payload: prepared.payload,\n        ...\"meta\" in prepared && {\n          meta: prepared.meta\n        },\n        ...\"error\" in prepared && {\n          error: prepared.error\n        }\n      };\n    }\n    return {\n      type,\n      payload: args[0]\n    };\n  }\n  actionCreator.toString = () => `${type}`;\n  actionCreator.type = type;\n  actionCreator.match = (action) => isAction(action) && action.type === type;\n  return actionCreator;\n}\nfunction isActionCreator(action) {\n  return typeof action === \"function\" && \"type\" in action && // hasMatchFunction only wants Matchers but I don't see the point in rewriting it\n  hasMatchFunction(action);\n}\nfunction isFSA(action) {\n  return isAction(action) && Object.keys(action).every(isValidKey);\n}\nfunction isValidKey(key) {\n  return [\"type\", \"payload\", \"error\", \"meta\"].indexOf(key) > -1;\n}\n\n// src/actionCreatorInvariantMiddleware.ts\nfunction getMessage(type) {\n  const splitType = type ? `${type}`.split(\"/\") : [];\n  const actionName = splitType[splitType.length - 1] || \"actionCreator\";\n  return `Detected an action creator with type \"${type || \"unknown\"}\" being dispatched. \nMake sure you're calling the action creator before dispatching, i.e. \\`dispatch(${actionName}())\\` instead of \\`dispatch(${actionName})\\`. This is necessary even if the action has no payload.`;\n}\nfunction createActionCreatorInvariantMiddleware(options = {}) {\n  if (process.env.NODE_ENV === \"production\") {\n    return () => (next) => (action) => next(action);\n  }\n  const {\n    isActionCreator: isActionCreator2 = isActionCreator\n  } = options;\n  return () => (next) => (action) => {\n    if (isActionCreator2(action)) {\n      console.warn(getMessage(action.type));\n    }\n    return next(action);\n  };\n}\n\n// src/utils.ts\nimport { produce as createNextState, isDraftable } from \"immer\";\nfunction getTimeMeasureUtils(maxDelay, fnName) {\n  let elapsed = 0;\n  return {\n    measureTime(fn) {\n      const started = Date.now();\n      try {\n        return fn();\n      } finally {\n        const finished = Date.now();\n        elapsed += finished - started;\n      }\n    },\n    warnIfExceeded() {\n      if (elapsed > maxDelay) {\n        console.warn(`${fnName} took ${elapsed}ms, which is more than the warning threshold of ${maxDelay}ms. \nIf your state or actions are very large, you may want to disable the middleware as it might cause too much of a slowdown in development mode. See https://redux-toolkit.js.org/api/getDefaultMiddleware for instructions.\nIt is disabled in production builds, so you don't need to worry about that.`);\n      }\n    }\n  };\n}\nvar Tuple = class _Tuple extends Array {\n  constructor(...items) {\n    super(...items);\n    Object.setPrototypeOf(this, _Tuple.prototype);\n  }\n  static get [Symbol.species]() {\n    return _Tuple;\n  }\n  concat(...arr) {\n    return super.concat.apply(this, arr);\n  }\n  prepend(...arr) {\n    if (arr.length === 1 && Array.isArray(arr[0])) {\n      return new _Tuple(...arr[0].concat(this));\n    }\n    return new _Tuple(...arr.concat(this));\n  }\n};\nfunction freezeDraftable(val) {\n  return isDraftable(val) ? createNextState(val, () => {\n  }) : val;\n}\nfunction getOrInsertComputed(map, key, compute) {\n  if (map.has(key)) return map.get(key);\n  return map.set(key, compute(key)).get(key);\n}\n\n// src/immutableStateInvariantMiddleware.ts\nfunction isImmutableDefault(value) {\n  return typeof value !== \"object\" || value == null || Object.isFrozen(value);\n}\nfunction trackForMutations(isImmutable, ignorePaths, obj) {\n  const trackedProperties = trackProperties(isImmutable, ignorePaths, obj);\n  return {\n    detectMutations() {\n      return detectMutations(isImmutable, ignorePaths, trackedProperties, obj);\n    }\n  };\n}\nfunction trackProperties(isImmutable, ignorePaths = [], obj, path = \"\", checkedObjects = /* @__PURE__ */ new Set()) {\n  const tracked = {\n    value: obj\n  };\n  if (!isImmutable(obj) && !checkedObjects.has(obj)) {\n    checkedObjects.add(obj);\n    tracked.children = {};\n    for (const key in obj) {\n      const childPath = path ? path + \".\" + key : key;\n      if (ignorePaths.length && ignorePaths.indexOf(childPath) !== -1) {\n        continue;\n      }\n      tracked.children[key] = trackProperties(isImmutable, ignorePaths, obj[key], childPath);\n    }\n  }\n  return tracked;\n}\nfunction detectMutations(isImmutable, ignoredPaths = [], trackedProperty, obj, sameParentRef = false, path = \"\") {\n  const prevObj = trackedProperty ? trackedProperty.value : void 0;\n  const sameRef = prevObj === obj;\n  if (sameParentRef && !sameRef && !Number.isNaN(obj)) {\n    return {\n      wasMutated: true,\n      path\n    };\n  }\n  if (isImmutable(prevObj) || isImmutable(obj)) {\n    return {\n      wasMutated: false\n    };\n  }\n  const keysToDetect = {};\n  for (let key in trackedProperty.children) {\n    keysToDetect[key] = true;\n  }\n  for (let key in obj) {\n    keysToDetect[key] = true;\n  }\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (let key in keysToDetect) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    const result = detectMutations(isImmutable, ignoredPaths, trackedProperty.children[key], obj[key], sameRef, nestedPath);\n    if (result.wasMutated) {\n      return result;\n    }\n  }\n  return {\n    wasMutated: false\n  };\n}\nfunction createImmutableStateInvariantMiddleware(options = {}) {\n  if (process.env.NODE_ENV === \"production\") {\n    return () => (next) => (action) => next(action);\n  } else {\n    let stringify2 = function(obj, serializer, indent, decycler) {\n      return JSON.stringify(obj, getSerialize2(serializer, decycler), indent);\n    }, getSerialize2 = function(serializer, decycler) {\n      let stack = [], keys = [];\n      if (!decycler) decycler = function(_, value) {\n        if (stack[0] === value) return \"[Circular ~]\";\n        return \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n      };\n      return function(key, value) {\n        if (stack.length > 0) {\n          var thisPos = stack.indexOf(this);\n          ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n          ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n          if (~stack.indexOf(value)) value = decycler.call(this, key, value);\n        } else stack.push(value);\n        return serializer == null ? value : serializer.call(this, key, value);\n      };\n    };\n    var stringify = stringify2, getSerialize = getSerialize2;\n    let {\n      isImmutable = isImmutableDefault,\n      ignoredPaths,\n      warnAfter = 32\n    } = options;\n    const track = trackForMutations.bind(null, isImmutable, ignoredPaths);\n    return ({\n      getState\n    }) => {\n      let state = getState();\n      let tracker = track(state);\n      let result;\n      return (next) => (action) => {\n        const measureUtils = getTimeMeasureUtils(warnAfter, \"ImmutableStateInvariantMiddleware\");\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(19) : `A state mutation was detected between dispatches, in the path '${result.path || \"\"}'.  This may cause incorrect behavior. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        const dispatchedAction = next(action);\n        measureUtils.measureTime(() => {\n          state = getState();\n          result = tracker.detectMutations();\n          tracker = track(state);\n          if (result.wasMutated) {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(20) : `A state mutation was detected inside a dispatch, in the path: ${result.path || \"\"}. Take a look at the reducer(s) handling the action ${stringify2(action)}. (https://redux.js.org/style-guide/style-guide#do-not-mutate-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n        return dispatchedAction;\n      };\n    };\n  }\n}\n\n// src/serializableStateInvariantMiddleware.ts\nimport { isAction as isAction2, isPlainObject } from \"redux\";\nfunction isPlain(val) {\n  const type = typeof val;\n  return val == null || type === \"string\" || type === \"boolean\" || type === \"number\" || Array.isArray(val) || isPlainObject(val);\n}\nfunction findNonSerializableValue(value, path = \"\", isSerializable = isPlain, getEntries, ignoredPaths = [], cache) {\n  let foundNestedSerializable;\n  if (!isSerializable(value)) {\n    return {\n      keyPath: path || \"<root>\",\n      value\n    };\n  }\n  if (typeof value !== \"object\" || value === null) {\n    return false;\n  }\n  if (cache?.has(value)) return false;\n  const entries = getEntries != null ? getEntries(value) : Object.entries(value);\n  const hasIgnoredPaths = ignoredPaths.length > 0;\n  for (const [key, nestedValue] of entries) {\n    const nestedPath = path ? path + \".\" + key : key;\n    if (hasIgnoredPaths) {\n      const hasMatches = ignoredPaths.some((ignored) => {\n        if (ignored instanceof RegExp) {\n          return ignored.test(nestedPath);\n        }\n        return nestedPath === ignored;\n      });\n      if (hasMatches) {\n        continue;\n      }\n    }\n    if (!isSerializable(nestedValue)) {\n      return {\n        keyPath: nestedPath,\n        value: nestedValue\n      };\n    }\n    if (typeof nestedValue === \"object\") {\n      foundNestedSerializable = findNonSerializableValue(nestedValue, nestedPath, isSerializable, getEntries, ignoredPaths, cache);\n      if (foundNestedSerializable) {\n        return foundNestedSerializable;\n      }\n    }\n  }\n  if (cache && isNestedFrozen(value)) cache.add(value);\n  return false;\n}\nfunction isNestedFrozen(value) {\n  if (!Object.isFrozen(value)) return false;\n  for (const nestedValue of Object.values(value)) {\n    if (typeof nestedValue !== \"object\" || nestedValue === null) continue;\n    if (!isNestedFrozen(nestedValue)) return false;\n  }\n  return true;\n}\nfunction createSerializableStateInvariantMiddleware(options = {}) {\n  if (process.env.NODE_ENV === \"production\") {\n    return () => (next) => (action) => next(action);\n  } else {\n    const {\n      isSerializable = isPlain,\n      getEntries,\n      ignoredActions = [],\n      ignoredActionPaths = [\"meta.arg\", \"meta.baseQueryMeta\"],\n      ignoredPaths = [],\n      warnAfter = 32,\n      ignoreState = false,\n      ignoreActions = false,\n      disableCache = false\n    } = options;\n    const cache = !disableCache && WeakSet ? /* @__PURE__ */ new WeakSet() : void 0;\n    return (storeAPI) => (next) => (action) => {\n      if (!isAction2(action)) {\n        return next(action);\n      }\n      const result = next(action);\n      const measureUtils = getTimeMeasureUtils(warnAfter, \"SerializableStateInvariantMiddleware\");\n      if (!ignoreActions && !(ignoredActions.length && ignoredActions.indexOf(action.type) !== -1)) {\n        measureUtils.measureTime(() => {\n          const foundActionNonSerializableValue = findNonSerializableValue(action, \"\", isSerializable, getEntries, ignoredActionPaths, cache);\n          if (foundActionNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundActionNonSerializableValue;\n            console.error(`A non-serializable value was detected in an action, in the path: \\`${keyPath}\\`. Value:`, value, \"\\nTake a look at the logic that dispatched this action: \", action, \"\\n(See https://redux.js.org/faq/actions#why-should-type-be-a-string-or-at-least-serializable-why-should-my-action-types-be-constants)\", \"\\n(To allow non-serializable values see: https://redux-toolkit.js.org/usage/usage-guide#working-with-non-serializable-data)\");\n          }\n        });\n      }\n      if (!ignoreState) {\n        measureUtils.measureTime(() => {\n          const state = storeAPI.getState();\n          const foundStateNonSerializableValue = findNonSerializableValue(state, \"\", isSerializable, getEntries, ignoredPaths, cache);\n          if (foundStateNonSerializableValue) {\n            const {\n              keyPath,\n              value\n            } = foundStateNonSerializableValue;\n            console.error(`A non-serializable value was detected in the state, in the path: \\`${keyPath}\\`. Value:`, value, `\nTake a look at the reducer(s) handling this action type: ${action.type}.\n(See https://redux.js.org/faq/organizing-state#can-i-put-functions-promises-or-other-non-serializable-items-in-my-store-state)`);\n          }\n        });\n        measureUtils.warnIfExceeded();\n      }\n      return result;\n    };\n  }\n}\n\n// src/getDefaultMiddleware.ts\nfunction isBoolean(x) {\n  return typeof x === \"boolean\";\n}\nvar buildGetDefaultMiddleware = () => function getDefaultMiddleware(options) {\n  const {\n    thunk = true,\n    immutableCheck = true,\n    serializableCheck = true,\n    actionCreatorCheck = true\n  } = options ?? {};\n  let middlewareArray = new Tuple();\n  if (thunk) {\n    if (isBoolean(thunk)) {\n      middlewareArray.push(thunkMiddleware);\n    } else {\n      middlewareArray.push(withExtraArgument(thunk.extraArgument));\n    }\n  }\n  if (process.env.NODE_ENV !== \"production\") {\n    if (immutableCheck) {\n      let immutableOptions = {};\n      if (!isBoolean(immutableCheck)) {\n        immutableOptions = immutableCheck;\n      }\n      middlewareArray.unshift(createImmutableStateInvariantMiddleware(immutableOptions));\n    }\n    if (serializableCheck) {\n      let serializableOptions = {};\n      if (!isBoolean(serializableCheck)) {\n        serializableOptions = serializableCheck;\n      }\n      middlewareArray.push(createSerializableStateInvariantMiddleware(serializableOptions));\n    }\n    if (actionCreatorCheck) {\n      let actionCreatorOptions = {};\n      if (!isBoolean(actionCreatorCheck)) {\n        actionCreatorOptions = actionCreatorCheck;\n      }\n      middlewareArray.unshift(createActionCreatorInvariantMiddleware(actionCreatorOptions));\n    }\n  }\n  return middlewareArray;\n};\n\n// src/autoBatchEnhancer.ts\nvar SHOULD_AUTOBATCH = \"RTK_autoBatch\";\nvar prepareAutoBatched = () => (payload) => ({\n  payload,\n  meta: {\n    [SHOULD_AUTOBATCH]: true\n  }\n});\nvar createQueueWithTimer = (timeout) => {\n  return (notify) => {\n    setTimeout(notify, timeout);\n  };\n};\nvar autoBatchEnhancer = (options = {\n  type: \"raf\"\n}) => (next) => (...args) => {\n  const store = next(...args);\n  let notifying = true;\n  let shouldNotifyAtEndOfTick = false;\n  let notificationQueued = false;\n  const listeners = /* @__PURE__ */ new Set();\n  const queueCallback = options.type === \"tick\" ? queueMicrotask : options.type === \"raf\" ? (\n    // requestAnimationFrame won't exist in SSR environments. Fall back to a vague approximation just to keep from erroring.\n    typeof window !== \"undefined\" && window.requestAnimationFrame ? window.requestAnimationFrame : createQueueWithTimer(10)\n  ) : options.type === \"callback\" ? options.queueNotification : createQueueWithTimer(options.timeout);\n  const notifyListeners = () => {\n    notificationQueued = false;\n    if (shouldNotifyAtEndOfTick) {\n      shouldNotifyAtEndOfTick = false;\n      listeners.forEach((l) => l());\n    }\n  };\n  return Object.assign({}, store, {\n    // Override the base `store.subscribe` method to keep original listeners\n    // from running if we're delaying notifications\n    subscribe(listener2) {\n      const wrappedListener = () => notifying && listener2();\n      const unsubscribe = store.subscribe(wrappedListener);\n      listeners.add(listener2);\n      return () => {\n        unsubscribe();\n        listeners.delete(listener2);\n      };\n    },\n    // Override the base `store.dispatch` method so that we can check actions\n    // for the `shouldAutoBatch` flag and determine if batching is active\n    dispatch(action) {\n      try {\n        notifying = !action?.meta?.[SHOULD_AUTOBATCH];\n        shouldNotifyAtEndOfTick = !notifying;\n        if (shouldNotifyAtEndOfTick) {\n          if (!notificationQueued) {\n            notificationQueued = true;\n            queueCallback(notifyListeners);\n          }\n        }\n        return store.dispatch(action);\n      } finally {\n        notifying = true;\n      }\n    }\n  });\n};\n\n// src/getDefaultEnhancers.ts\nvar buildGetDefaultEnhancers = (middlewareEnhancer) => function getDefaultEnhancers(options) {\n  const {\n    autoBatch = true\n  } = options ?? {};\n  let enhancerArray = new Tuple(middlewareEnhancer);\n  if (autoBatch) {\n    enhancerArray.push(autoBatchEnhancer(typeof autoBatch === \"object\" ? autoBatch : void 0));\n  }\n  return enhancerArray;\n};\n\n// src/configureStore.ts\nfunction configureStore(options) {\n  const getDefaultMiddleware = buildGetDefaultMiddleware();\n  const {\n    reducer = void 0,\n    middleware,\n    devTools = true,\n    duplicateMiddlewareCheck = true,\n    preloadedState = void 0,\n    enhancers = void 0\n  } = options || {};\n  let rootReducer;\n  if (typeof reducer === \"function\") {\n    rootReducer = reducer;\n  } else if (isPlainObject2(reducer)) {\n    rootReducer = combineReducers(reducer);\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"`reducer` is a required argument, and must be a function or an object of functions that can be passed to combineReducers\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && middleware && typeof middleware !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"`middleware` field must be a callback\");\n  }\n  let finalMiddleware;\n  if (typeof middleware === \"function\") {\n    finalMiddleware = middleware(getDefaultMiddleware);\n    if (process.env.NODE_ENV !== \"production\" && !Array.isArray(finalMiddleware)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : \"when using a middleware builder function, an array of middleware must be returned\");\n    }\n  } else {\n    finalMiddleware = getDefaultMiddleware();\n  }\n  if (process.env.NODE_ENV !== \"production\" && finalMiddleware.some((item) => typeof item !== \"function\")) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"each middleware provided to configureStore must be a function\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && duplicateMiddlewareCheck) {\n    let middlewareReferences = /* @__PURE__ */ new Set();\n    finalMiddleware.forEach((middleware2) => {\n      if (middlewareReferences.has(middleware2)) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(42) : \"Duplicate middleware references found when creating the store. Ensure that each middleware is only included once.\");\n      }\n      middlewareReferences.add(middleware2);\n    });\n  }\n  let finalCompose = compose2;\n  if (devTools) {\n    finalCompose = composeWithDevTools({\n      // Enable capture of stack traces for dispatched Redux actions\n      trace: process.env.NODE_ENV !== \"production\",\n      ...typeof devTools === \"object\" && devTools\n    });\n  }\n  const middlewareEnhancer = applyMiddleware(...finalMiddleware);\n  const getDefaultEnhancers = buildGetDefaultEnhancers(middlewareEnhancer);\n  if (process.env.NODE_ENV !== \"production\" && enhancers && typeof enhancers !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : \"`enhancers` field must be a callback\");\n  }\n  let storeEnhancers = typeof enhancers === \"function\" ? enhancers(getDefaultEnhancers) : getDefaultEnhancers();\n  if (process.env.NODE_ENV !== \"production\" && !Array.isArray(storeEnhancers)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : \"`enhancers` callback must return an array\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && storeEnhancers.some((item) => typeof item !== \"function\")) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"each enhancer provided to configureStore must be a function\");\n  }\n  if (process.env.NODE_ENV !== \"production\" && finalMiddleware.length && !storeEnhancers.includes(middlewareEnhancer)) {\n    console.error(\"middlewares were provided, but middleware enhancer was not included in final enhancers - make sure to call `getDefaultEnhancers`\");\n  }\n  const composedEnhancer = finalCompose(...storeEnhancers);\n  return createStore(rootReducer, preloadedState, composedEnhancer);\n}\n\n// src/createReducer.ts\nimport { produce as createNextState2, isDraft as isDraft2, isDraftable as isDraftable2 } from \"immer\";\n\n// src/mapBuilders.ts\nfunction executeReducerBuilderCallback(builderCallback) {\n  const actionsMap = {};\n  const actionMatchers = [];\n  let defaultCaseReducer;\n  const builder = {\n    addCase(typeOrActionCreator, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (actionMatchers.length > 0) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(26) : \"`builder.addCase` should only be called before calling `builder.addMatcher`\");\n        }\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(27) : \"`builder.addCase` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n      if (!type) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(28) : \"`builder.addCase` cannot be called with an empty action type\");\n      }\n      if (type in actionsMap) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(29) : `\\`builder.addCase\\` cannot be called with two reducers for the same action type '${type}'`);\n      }\n      actionsMap[type] = reducer;\n      return builder;\n    },\n    addMatcher(matcher, reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(30) : \"`builder.addMatcher` should only be called before calling `builder.addDefaultCase`\");\n        }\n      }\n      actionMatchers.push({\n        matcher,\n        reducer\n      });\n      return builder;\n    },\n    addDefaultCase(reducer) {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (defaultCaseReducer) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(31) : \"`builder.addDefaultCase` can only be called once\");\n        }\n      }\n      defaultCaseReducer = reducer;\n      return builder;\n    }\n  };\n  builderCallback(builder);\n  return [actionsMap, actionMatchers, defaultCaseReducer];\n}\n\n// src/createReducer.ts\nfunction isStateFunction(x) {\n  return typeof x === \"function\";\n}\nfunction createReducer(initialState, mapOrBuilderCallback) {\n  if (process.env.NODE_ENV !== \"production\") {\n    if (typeof mapOrBuilderCallback === \"object\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : \"The object notation for `createReducer` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer\");\n    }\n  }\n  let [actionsMap, finalActionMatchers, finalDefaultCaseReducer] = executeReducerBuilderCallback(mapOrBuilderCallback);\n  let getInitialState;\n  if (isStateFunction(initialState)) {\n    getInitialState = () => freezeDraftable(initialState());\n  } else {\n    const frozenInitialState = freezeDraftable(initialState);\n    getInitialState = () => frozenInitialState;\n  }\n  function reducer(state = getInitialState(), action) {\n    let caseReducers = [actionsMap[action.type], ...finalActionMatchers.filter(({\n      matcher\n    }) => matcher(action)).map(({\n      reducer: reducer2\n    }) => reducer2)];\n    if (caseReducers.filter((cr) => !!cr).length === 0) {\n      caseReducers = [finalDefaultCaseReducer];\n    }\n    return caseReducers.reduce((previousState, caseReducer) => {\n      if (caseReducer) {\n        if (isDraft2(previousState)) {\n          const draft = previousState;\n          const result = caseReducer(draft, action);\n          if (result === void 0) {\n            return previousState;\n          }\n          return result;\n        } else if (!isDraftable2(previousState)) {\n          const result = caseReducer(previousState, action);\n          if (result === void 0) {\n            if (previousState === null) {\n              return previousState;\n            }\n            throw Error(\"A case reducer on a non-draftable value must not return undefined\");\n          }\n          return result;\n        } else {\n          return createNextState2(previousState, (draft) => {\n            return caseReducer(draft, action);\n          });\n        }\n      }\n      return previousState;\n    }, state);\n  }\n  reducer.getInitialState = getInitialState;\n  return reducer;\n}\n\n// src/matchers.ts\nvar matches = (matcher, action) => {\n  if (hasMatchFunction(matcher)) {\n    return matcher.match(action);\n  } else {\n    return matcher(action);\n  }\n};\nfunction isAnyOf(...matchers) {\n  return (action) => {\n    return matchers.some((matcher) => matches(matcher, action));\n  };\n}\nfunction isAllOf(...matchers) {\n  return (action) => {\n    return matchers.every((matcher) => matches(matcher, action));\n  };\n}\nfunction hasExpectedRequestMetadata(action, validStatus) {\n  if (!action || !action.meta) return false;\n  const hasValidRequestId = typeof action.meta.requestId === \"string\";\n  const hasValidRequestStatus = validStatus.indexOf(action.meta.requestStatus) > -1;\n  return hasValidRequestId && hasValidRequestStatus;\n}\nfunction isAsyncThunkArray(a) {\n  return typeof a[0] === \"function\" && \"pending\" in a[0] && \"fulfilled\" in a[0] && \"rejected\" in a[0];\n}\nfunction isPending(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isPending()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.pending));\n}\nfunction isRejected(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejected()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.rejected));\n}\nfunction isRejectedWithValue(...asyncThunks) {\n  const hasFlag = (action) => {\n    return action && action.meta && action.meta.rejectedWithValue;\n  };\n  if (asyncThunks.length === 0) {\n    return isAllOf(isRejected(...asyncThunks), hasFlag);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isRejectedWithValue()(asyncThunks[0]);\n  }\n  return isAllOf(isRejected(...asyncThunks), hasFlag);\n}\nfunction isFulfilled(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"fulfilled\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isFulfilled()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.map((asyncThunk) => asyncThunk.fulfilled));\n}\nfunction isAsyncThunkAction(...asyncThunks) {\n  if (asyncThunks.length === 0) {\n    return (action) => hasExpectedRequestMetadata(action, [\"pending\", \"fulfilled\", \"rejected\"]);\n  }\n  if (!isAsyncThunkArray(asyncThunks)) {\n    return isAsyncThunkAction()(asyncThunks[0]);\n  }\n  return isAnyOf(...asyncThunks.flatMap((asyncThunk) => [asyncThunk.pending, asyncThunk.rejected, asyncThunk.fulfilled]));\n}\n\n// src/nanoid.ts\nvar urlAlphabet = \"ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW\";\nvar nanoid = (size = 21) => {\n  let id = \"\";\n  let i = size;\n  while (i--) {\n    id += urlAlphabet[Math.random() * 64 | 0];\n  }\n  return id;\n};\n\n// src/createAsyncThunk.ts\nvar commonProperties = [\"name\", \"message\", \"stack\", \"code\"];\nvar RejectWithValue = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar FulfillWithMeta = class {\n  constructor(payload, meta) {\n    this.payload = payload;\n    this.meta = meta;\n  }\n  /*\n  type-only property to distinguish between RejectWithValue and FulfillWithMeta\n  does not exist at runtime\n  */\n  _type;\n};\nvar miniSerializeError = (value) => {\n  if (typeof value === \"object\" && value !== null) {\n    const simpleError = {};\n    for (const property of commonProperties) {\n      if (typeof value[property] === \"string\") {\n        simpleError[property] = value[property];\n      }\n    }\n    return simpleError;\n  }\n  return {\n    message: String(value)\n  };\n};\nvar externalAbortMessage = \"External signal was aborted\";\nvar createAsyncThunk = /* @__PURE__ */ (() => {\n  function createAsyncThunk2(typePrefix, payloadCreator, options) {\n    const fulfilled = createAction(typePrefix + \"/fulfilled\", (payload, requestId, arg, meta) => ({\n      payload,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"fulfilled\"\n      }\n    }));\n    const pending = createAction(typePrefix + \"/pending\", (requestId, arg, meta) => ({\n      payload: void 0,\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        requestStatus: \"pending\"\n      }\n    }));\n    const rejected = createAction(typePrefix + \"/rejected\", (error, requestId, arg, payload, meta) => ({\n      payload,\n      error: (options && options.serializeError || miniSerializeError)(error || \"Rejected\"),\n      meta: {\n        ...meta || {},\n        arg,\n        requestId,\n        rejectedWithValue: !!payload,\n        requestStatus: \"rejected\",\n        aborted: error?.name === \"AbortError\",\n        condition: error?.name === \"ConditionError\"\n      }\n    }));\n    function actionCreator(arg, {\n      signal\n    } = {}) {\n      return (dispatch, getState, extra) => {\n        const requestId = options?.idGenerator ? options.idGenerator(arg) : nanoid();\n        const abortController = new AbortController();\n        let abortHandler;\n        let abortReason;\n        function abort(reason) {\n          abortReason = reason;\n          abortController.abort();\n        }\n        if (signal) {\n          if (signal.aborted) {\n            abort(externalAbortMessage);\n          } else {\n            signal.addEventListener(\"abort\", () => abort(externalAbortMessage), {\n              once: true\n            });\n          }\n        }\n        const promise = async function() {\n          let finalAction;\n          try {\n            let conditionResult = options?.condition?.(arg, {\n              getState,\n              extra\n            });\n            if (isThenable(conditionResult)) {\n              conditionResult = await conditionResult;\n            }\n            if (conditionResult === false || abortController.signal.aborted) {\n              throw {\n                name: \"ConditionError\",\n                message: \"Aborted due to condition callback returning false.\"\n              };\n            }\n            const abortedPromise = new Promise((_, reject) => {\n              abortHandler = () => {\n                reject({\n                  name: \"AbortError\",\n                  message: abortReason || \"Aborted\"\n                });\n              };\n              abortController.signal.addEventListener(\"abort\", abortHandler);\n            });\n            dispatch(pending(requestId, arg, options?.getPendingMeta?.({\n              requestId,\n              arg\n            }, {\n              getState,\n              extra\n            })));\n            finalAction = await Promise.race([abortedPromise, Promise.resolve(payloadCreator(arg, {\n              dispatch,\n              getState,\n              extra,\n              requestId,\n              signal: abortController.signal,\n              abort,\n              rejectWithValue: (value, meta) => {\n                return new RejectWithValue(value, meta);\n              },\n              fulfillWithValue: (value, meta) => {\n                return new FulfillWithMeta(value, meta);\n              }\n            })).then((result) => {\n              if (result instanceof RejectWithValue) {\n                throw result;\n              }\n              if (result instanceof FulfillWithMeta) {\n                return fulfilled(result.payload, requestId, arg, result.meta);\n              }\n              return fulfilled(result, requestId, arg);\n            })]);\n          } catch (err) {\n            finalAction = err instanceof RejectWithValue ? rejected(null, requestId, arg, err.payload, err.meta) : rejected(err, requestId, arg);\n          } finally {\n            if (abortHandler) {\n              abortController.signal.removeEventListener(\"abort\", abortHandler);\n            }\n          }\n          const skipDispatch = options && !options.dispatchConditionRejection && rejected.match(finalAction) && finalAction.meta.condition;\n          if (!skipDispatch) {\n            dispatch(finalAction);\n          }\n          return finalAction;\n        }();\n        return Object.assign(promise, {\n          abort,\n          requestId,\n          arg,\n          unwrap() {\n            return promise.then(unwrapResult);\n          }\n        });\n      };\n    }\n    return Object.assign(actionCreator, {\n      pending,\n      rejected,\n      fulfilled,\n      settled: isAnyOf(rejected, fulfilled),\n      typePrefix\n    });\n  }\n  createAsyncThunk2.withTypes = () => createAsyncThunk2;\n  return createAsyncThunk2;\n})();\nfunction unwrapResult(action) {\n  if (action.meta && action.meta.rejectedWithValue) {\n    throw action.payload;\n  }\n  if (action.error) {\n    throw action.error;\n  }\n  return action.payload;\n}\nfunction isThenable(value) {\n  return value !== null && typeof value === \"object\" && typeof value.then === \"function\";\n}\n\n// src/createSlice.ts\nvar asyncThunkSymbol = /* @__PURE__ */ Symbol.for(\"rtk-slice-createasyncthunk\");\nvar asyncThunkCreator = {\n  [asyncThunkSymbol]: createAsyncThunk\n};\nvar ReducerType = /* @__PURE__ */ ((ReducerType2) => {\n  ReducerType2[\"reducer\"] = \"reducer\";\n  ReducerType2[\"reducerWithPrepare\"] = \"reducerWithPrepare\";\n  ReducerType2[\"asyncThunk\"] = \"asyncThunk\";\n  return ReducerType2;\n})(ReducerType || {});\nfunction getType(slice, actionKey) {\n  return `${slice}/${actionKey}`;\n}\nfunction buildCreateSlice({\n  creators\n} = {}) {\n  const cAT = creators?.asyncThunk?.[asyncThunkSymbol];\n  return function createSlice2(options) {\n    const {\n      name,\n      reducerPath = name\n    } = options;\n    if (!name) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"`name` is a required option for createSlice\");\n    }\n    if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\n      if (options.initialState === void 0) {\n        console.error(\"You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`\");\n      }\n    }\n    const reducers = (typeof options.reducers === \"function\" ? options.reducers(buildReducerCreators()) : options.reducers) || {};\n    const reducerNames = Object.keys(reducers);\n    const context = {\n      sliceCaseReducersByName: {},\n      sliceCaseReducersByType: {},\n      actionCreators: {},\n      sliceMatchers: []\n    };\n    const contextMethods = {\n      addCase(typeOrActionCreator, reducer2) {\n        const type = typeof typeOrActionCreator === \"string\" ? typeOrActionCreator : typeOrActionCreator.type;\n        if (!type) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"`context.addCase` cannot be called with an empty action type\");\n        }\n        if (type in context.sliceCaseReducersByType) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"`context.addCase` cannot be called with two reducers for the same action type: \" + type);\n        }\n        context.sliceCaseReducersByType[type] = reducer2;\n        return contextMethods;\n      },\n      addMatcher(matcher, reducer2) {\n        context.sliceMatchers.push({\n          matcher,\n          reducer: reducer2\n        });\n        return contextMethods;\n      },\n      exposeAction(name2, actionCreator) {\n        context.actionCreators[name2] = actionCreator;\n        return contextMethods;\n      },\n      exposeCaseReducer(name2, reducer2) {\n        context.sliceCaseReducersByName[name2] = reducer2;\n        return contextMethods;\n      }\n    };\n    reducerNames.forEach((reducerName) => {\n      const reducerDefinition = reducers[reducerName];\n      const reducerDetails = {\n        reducerName,\n        type: getType(name, reducerName),\n        createNotation: typeof options.reducers === \"function\"\n      };\n      if (isAsyncThunkSliceReducerDefinition(reducerDefinition)) {\n        handleThunkCaseReducerDefinition(reducerDetails, reducerDefinition, contextMethods, cAT);\n      } else {\n        handleNormalReducerDefinition(reducerDetails, reducerDefinition, contextMethods);\n      }\n    });\n    function buildReducer() {\n      if (process.env.NODE_ENV !== \"production\") {\n        if (typeof options.extraReducers === \"object\") {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"The object notation for `createSlice.extraReducers` has been removed. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice\");\n        }\n      }\n      const [extraReducers = {}, actionMatchers = [], defaultCaseReducer = void 0] = typeof options.extraReducers === \"function\" ? executeReducerBuilderCallback(options.extraReducers) : [options.extraReducers];\n      const finalCaseReducers = {\n        ...extraReducers,\n        ...context.sliceCaseReducersByType\n      };\n      return createReducer(options.initialState, (builder) => {\n        for (let key in finalCaseReducers) {\n          builder.addCase(key, finalCaseReducers[key]);\n        }\n        for (let sM of context.sliceMatchers) {\n          builder.addMatcher(sM.matcher, sM.reducer);\n        }\n        for (let m of actionMatchers) {\n          builder.addMatcher(m.matcher, m.reducer);\n        }\n        if (defaultCaseReducer) {\n          builder.addDefaultCase(defaultCaseReducer);\n        }\n      });\n    }\n    const selectSelf = (state) => state;\n    const injectedSelectorCache = /* @__PURE__ */ new Map();\n    const injectedStateCache = /* @__PURE__ */ new WeakMap();\n    let _reducer;\n    function reducer(state, action) {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer(state, action);\n    }\n    function getInitialState() {\n      if (!_reducer) _reducer = buildReducer();\n      return _reducer.getInitialState();\n    }\n    function makeSelectorProps(reducerPath2, injected = false) {\n      function selectSlice(state) {\n        let sliceState = state[reducerPath2];\n        if (typeof sliceState === \"undefined\") {\n          if (injected) {\n            sliceState = getOrInsertComputed(injectedStateCache, selectSlice, getInitialState);\n          } else if (process.env.NODE_ENV !== \"production\") {\n            throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : \"selectSlice returned undefined for an uninjected slice reducer\");\n          }\n        }\n        return sliceState;\n      }\n      function getSelectors(selectState = selectSelf) {\n        const selectorCache = getOrInsertComputed(injectedSelectorCache, injected, () => /* @__PURE__ */ new WeakMap());\n        return getOrInsertComputed(selectorCache, selectState, () => {\n          const map = {};\n          for (const [name2, selector] of Object.entries(options.selectors ?? {})) {\n            map[name2] = wrapSelector(selector, selectState, () => getOrInsertComputed(injectedStateCache, selectState, getInitialState), injected);\n          }\n          return map;\n        });\n      }\n      return {\n        reducerPath: reducerPath2,\n        getSelectors,\n        get selectors() {\n          return getSelectors(selectSlice);\n        },\n        selectSlice\n      };\n    }\n    const slice = {\n      name,\n      reducer,\n      actions: context.actionCreators,\n      caseReducers: context.sliceCaseReducersByName,\n      getInitialState,\n      ...makeSelectorProps(reducerPath),\n      injectInto(injectable, {\n        reducerPath: pathOpt,\n        ...config\n      } = {}) {\n        const newReducerPath = pathOpt ?? reducerPath;\n        injectable.inject({\n          reducerPath: newReducerPath,\n          reducer\n        }, config);\n        return {\n          ...slice,\n          ...makeSelectorProps(newReducerPath, true)\n        };\n      }\n    };\n    return slice;\n  };\n}\nfunction wrapSelector(selector, selectState, getInitialState, injected) {\n  function wrapper(rootState, ...args) {\n    let sliceState = selectState(rootState);\n    if (typeof sliceState === \"undefined\") {\n      if (injected) {\n        sliceState = getInitialState();\n      } else if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"selectState returned undefined for an uninjected slice reducer\");\n      }\n    }\n    return selector(sliceState, ...args);\n  }\n  wrapper.unwrapped = selector;\n  return wrapper;\n}\nvar createSlice = /* @__PURE__ */ buildCreateSlice();\nfunction buildReducerCreators() {\n  function asyncThunk(payloadCreator, config) {\n    return {\n      _reducerDefinitionType: \"asyncThunk\" /* asyncThunk */,\n      payloadCreator,\n      ...config\n    };\n  }\n  asyncThunk.withTypes = () => asyncThunk;\n  return {\n    reducer(caseReducer) {\n      return Object.assign({\n        // hack so the wrapping function has the same name as the original\n        // we need to create a wrapper so the `reducerDefinitionType` is not assigned to the original\n        [caseReducer.name](...args) {\n          return caseReducer(...args);\n        }\n      }[caseReducer.name], {\n        _reducerDefinitionType: \"reducer\" /* reducer */\n      });\n    },\n    preparedReducer(prepare, reducer) {\n      return {\n        _reducerDefinitionType: \"reducerWithPrepare\" /* reducerWithPrepare */,\n        prepare,\n        reducer\n      };\n    },\n    asyncThunk\n  };\n}\nfunction handleNormalReducerDefinition({\n  type,\n  reducerName,\n  createNotation\n}, maybeReducerWithPrepare, context) {\n  let caseReducer;\n  let prepareCallback;\n  if (\"reducer\" in maybeReducerWithPrepare) {\n    if (createNotation && !isCaseReducerWithPrepareDefinition(maybeReducerWithPrepare)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(17) : \"Please use the `create.preparedReducer` notation for prepared action creators with the `create` notation.\");\n    }\n    caseReducer = maybeReducerWithPrepare.reducer;\n    prepareCallback = maybeReducerWithPrepare.prepare;\n  } else {\n    caseReducer = maybeReducerWithPrepare;\n  }\n  context.addCase(type, caseReducer).exposeCaseReducer(reducerName, caseReducer).exposeAction(reducerName, prepareCallback ? createAction(type, prepareCallback) : createAction(type));\n}\nfunction isAsyncThunkSliceReducerDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"asyncThunk\" /* asyncThunk */;\n}\nfunction isCaseReducerWithPrepareDefinition(reducerDefinition) {\n  return reducerDefinition._reducerDefinitionType === \"reducerWithPrepare\" /* reducerWithPrepare */;\n}\nfunction handleThunkCaseReducerDefinition({\n  type,\n  reducerName\n}, reducerDefinition, context, cAT) {\n  if (!cAT) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(18) : \"Cannot use `create.asyncThunk` in the built-in `createSlice`. Use `buildCreateSlice({ creators: { asyncThunk: asyncThunkCreator } })` to create a customised version of `createSlice`.\");\n  }\n  const {\n    payloadCreator,\n    fulfilled,\n    pending,\n    rejected,\n    settled,\n    options\n  } = reducerDefinition;\n  const thunk = cAT(type, payloadCreator, options);\n  context.exposeAction(reducerName, thunk);\n  if (fulfilled) {\n    context.addCase(thunk.fulfilled, fulfilled);\n  }\n  if (pending) {\n    context.addCase(thunk.pending, pending);\n  }\n  if (rejected) {\n    context.addCase(thunk.rejected, rejected);\n  }\n  if (settled) {\n    context.addMatcher(thunk.settled, settled);\n  }\n  context.exposeCaseReducer(reducerName, {\n    fulfilled: fulfilled || noop,\n    pending: pending || noop,\n    rejected: rejected || noop,\n    settled: settled || noop\n  });\n}\nfunction noop() {\n}\n\n// src/entities/entity_state.ts\nfunction getInitialEntityState() {\n  return {\n    ids: [],\n    entities: {}\n  };\n}\nfunction createInitialStateFactory(stateAdapter) {\n  function getInitialState(additionalState = {}, entities) {\n    const state = Object.assign(getInitialEntityState(), additionalState);\n    return entities ? stateAdapter.setAll(state, entities) : state;\n  }\n  return {\n    getInitialState\n  };\n}\n\n// src/entities/state_selectors.ts\nfunction createSelectorsFactory() {\n  function getSelectors(selectState, options = {}) {\n    const {\n      createSelector: createSelector2 = createDraftSafeSelector\n    } = options;\n    const selectIds = (state) => state.ids;\n    const selectEntities = (state) => state.entities;\n    const selectAll = createSelector2(selectIds, selectEntities, (ids, entities) => ids.map((id) => entities[id]));\n    const selectId = (_, id) => id;\n    const selectById = (entities, id) => entities[id];\n    const selectTotal = createSelector2(selectIds, (ids) => ids.length);\n    if (!selectState) {\n      return {\n        selectIds,\n        selectEntities,\n        selectAll,\n        selectTotal,\n        selectById: createSelector2(selectEntities, selectId, selectById)\n      };\n    }\n    const selectGlobalizedEntities = createSelector2(selectState, selectEntities);\n    return {\n      selectIds: createSelector2(selectState, selectIds),\n      selectEntities: selectGlobalizedEntities,\n      selectAll: createSelector2(selectState, selectAll),\n      selectTotal: createSelector2(selectState, selectTotal),\n      selectById: createSelector2(selectGlobalizedEntities, selectId, selectById)\n    };\n  }\n  return {\n    getSelectors\n  };\n}\n\n// src/entities/state_adapter.ts\nimport { produce as createNextState3, isDraft as isDraft3 } from \"immer\";\nvar isDraftTyped = isDraft3;\nfunction createSingleArgumentStateOperator(mutator) {\n  const operator = createStateOperator((_, state) => mutator(state));\n  return function operation(state) {\n    return operator(state, void 0);\n  };\n}\nfunction createStateOperator(mutator) {\n  return function operation(state, arg) {\n    function isPayloadActionArgument(arg2) {\n      return isFSA(arg2);\n    }\n    const runMutator = (draft) => {\n      if (isPayloadActionArgument(arg)) {\n        mutator(arg.payload, draft);\n      } else {\n        mutator(arg, draft);\n      }\n    };\n    if (isDraftTyped(state)) {\n      runMutator(state);\n      return state;\n    }\n    return createNextState3(state, runMutator);\n  };\n}\n\n// src/entities/utils.ts\nimport { current as current2, isDraft as isDraft4 } from \"immer\";\nfunction selectIdValue(entity, selectId) {\n  const key = selectId(entity);\n  if (process.env.NODE_ENV !== \"production\" && key === void 0) {\n    console.warn(\"The entity passed to the `selectId` implementation returned undefined.\", \"You should probably provide your own `selectId` implementation.\", \"The entity that was passed:\", entity, \"The `selectId` implementation:\", selectId.toString());\n  }\n  return key;\n}\nfunction ensureEntitiesArray(entities) {\n  if (!Array.isArray(entities)) {\n    entities = Object.values(entities);\n  }\n  return entities;\n}\nfunction getCurrent(value) {\n  return isDraft4(value) ? current2(value) : value;\n}\nfunction splitAddedUpdatedEntities(newEntities, selectId, state) {\n  newEntities = ensureEntitiesArray(newEntities);\n  const existingIdsArray = getCurrent(state.ids);\n  const existingIds = new Set(existingIdsArray);\n  const added = [];\n  const addedIds = /* @__PURE__ */ new Set([]);\n  const updated = [];\n  for (const entity of newEntities) {\n    const id = selectIdValue(entity, selectId);\n    if (existingIds.has(id) || addedIds.has(id)) {\n      updated.push({\n        id,\n        changes: entity\n      });\n    } else {\n      addedIds.add(id);\n      added.push(entity);\n    }\n  }\n  return [added, updated, existingIdsArray];\n}\n\n// src/entities/unsorted_state_adapter.ts\nfunction createUnsortedStateAdapter(selectId) {\n  function addOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (key in state.entities) {\n      return;\n    }\n    state.ids.push(key);\n    state.entities[key] = entity;\n  }\n  function addManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      addOneMutably(entity, state);\n    }\n  }\n  function setOneMutably(entity, state) {\n    const key = selectIdValue(entity, selectId);\n    if (!(key in state.entities)) {\n      state.ids.push(key);\n    }\n    ;\n    state.entities[key] = entity;\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    for (const entity of newEntities) {\n      setOneMutably(entity, state);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.ids = [];\n    state.entities = {};\n    addManyMutably(newEntities, state);\n  }\n  function removeOneMutably(key, state) {\n    return removeManyMutably([key], state);\n  }\n  function removeManyMutably(keys, state) {\n    let didMutate = false;\n    keys.forEach((key) => {\n      if (key in state.entities) {\n        delete state.entities[key];\n        didMutate = true;\n      }\n    });\n    if (didMutate) {\n      state.ids = state.ids.filter((id) => id in state.entities);\n    }\n  }\n  function removeAllMutably(state) {\n    Object.assign(state, {\n      ids: [],\n      entities: {}\n    });\n  }\n  function takeNewKey(keys, update, state) {\n    const original3 = state.entities[update.id];\n    if (original3 === void 0) {\n      return false;\n    }\n    const updated = Object.assign({}, original3, update.changes);\n    const newKey = selectIdValue(updated, selectId);\n    const hasNewKey = newKey !== update.id;\n    if (hasNewKey) {\n      keys[update.id] = newKey;\n      delete state.entities[update.id];\n    }\n    ;\n    state.entities[newKey] = updated;\n    return hasNewKey;\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    const newKeys = {};\n    const updatesPerEntity = {};\n    updates.forEach((update) => {\n      if (update.id in state.entities) {\n        updatesPerEntity[update.id] = {\n          id: update.id,\n          // Spreads ignore falsy values, so this works even if there isn't\n          // an existing update already at this key\n          changes: {\n            ...updatesPerEntity[update.id]?.changes,\n            ...update.changes\n          }\n        };\n      }\n    });\n    updates = Object.values(updatesPerEntity);\n    const didMutateEntities = updates.length > 0;\n    if (didMutateEntities) {\n      const didMutateIds = updates.filter((update) => takeNewKey(newKeys, update, state)).length > 0;\n      if (didMutateIds) {\n        state.ids = Object.values(state.entities).map((e) => selectIdValue(e, selectId));\n      }\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    addManyMutably(added, state);\n    updateManyMutably(updated, state);\n  }\n  return {\n    removeAll: createSingleArgumentStateOperator(removeAllMutably),\n    addOne: createStateOperator(addOneMutably),\n    addMany: createStateOperator(addManyMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    upsertMany: createStateOperator(upsertManyMutably),\n    removeOne: createStateOperator(removeOneMutably),\n    removeMany: createStateOperator(removeManyMutably)\n  };\n}\n\n// src/entities/sorted_state_adapter.ts\nfunction findInsertIndex(sortedItems, item, comparisonFunction) {\n  let lowIndex = 0;\n  let highIndex = sortedItems.length;\n  while (lowIndex < highIndex) {\n    let middleIndex = lowIndex + highIndex >>> 1;\n    const currentItem = sortedItems[middleIndex];\n    const res = comparisonFunction(item, currentItem);\n    if (res >= 0) {\n      lowIndex = middleIndex + 1;\n    } else {\n      highIndex = middleIndex;\n    }\n  }\n  return lowIndex;\n}\nfunction insert(sortedItems, item, comparisonFunction) {\n  const insertAtIndex = findInsertIndex(sortedItems, item, comparisonFunction);\n  sortedItems.splice(insertAtIndex, 0, item);\n  return sortedItems;\n}\nfunction createSortedStateAdapter(selectId, comparer) {\n  const {\n    removeOne,\n    removeMany,\n    removeAll\n  } = createUnsortedStateAdapter(selectId);\n  function addOneMutably(entity, state) {\n    return addManyMutably([entity], state);\n  }\n  function addManyMutably(newEntities, state, existingIds) {\n    newEntities = ensureEntitiesArray(newEntities);\n    const existingKeys = new Set(existingIds ?? getCurrent(state.ids));\n    const models = newEntities.filter((model) => !existingKeys.has(selectIdValue(model, selectId)));\n    if (models.length !== 0) {\n      mergeFunction(state, models);\n    }\n  }\n  function setOneMutably(entity, state) {\n    return setManyMutably([entity], state);\n  }\n  function setManyMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    if (newEntities.length !== 0) {\n      for (const item of newEntities) {\n        delete state.entities[selectId(item)];\n      }\n      mergeFunction(state, newEntities);\n    }\n  }\n  function setAllMutably(newEntities, state) {\n    newEntities = ensureEntitiesArray(newEntities);\n    state.entities = {};\n    state.ids = [];\n    addManyMutably(newEntities, state, []);\n  }\n  function updateOneMutably(update, state) {\n    return updateManyMutably([update], state);\n  }\n  function updateManyMutably(updates, state) {\n    let appliedUpdates = false;\n    let replacedIds = false;\n    for (let update of updates) {\n      const entity = state.entities[update.id];\n      if (!entity) {\n        continue;\n      }\n      appliedUpdates = true;\n      Object.assign(entity, update.changes);\n      const newId = selectId(entity);\n      if (update.id !== newId) {\n        replacedIds = true;\n        delete state.entities[update.id];\n        const oldIndex = state.ids.indexOf(update.id);\n        state.ids[oldIndex] = newId;\n        state.entities[newId] = entity;\n      }\n    }\n    if (appliedUpdates) {\n      mergeFunction(state, [], appliedUpdates, replacedIds);\n    }\n  }\n  function upsertOneMutably(entity, state) {\n    return upsertManyMutably([entity], state);\n  }\n  function upsertManyMutably(newEntities, state) {\n    const [added, updated, existingIdsArray] = splitAddedUpdatedEntities(newEntities, selectId, state);\n    if (added.length) {\n      addManyMutably(added, state, existingIdsArray);\n    }\n    if (updated.length) {\n      updateManyMutably(updated, state);\n    }\n  }\n  function areArraysEqual(a, b) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (let i = 0; i < a.length; i++) {\n      if (a[i] === b[i]) {\n        continue;\n      }\n      return false;\n    }\n    return true;\n  }\n  const mergeFunction = (state, addedItems, appliedUpdates, replacedIds) => {\n    const currentEntities = getCurrent(state.entities);\n    const currentIds = getCurrent(state.ids);\n    const stateEntities = state.entities;\n    let ids = currentIds;\n    if (replacedIds) {\n      ids = new Set(currentIds);\n    }\n    let sortedEntities = [];\n    for (const id of ids) {\n      const entity = currentEntities[id];\n      if (entity) {\n        sortedEntities.push(entity);\n      }\n    }\n    const wasPreviouslyEmpty = sortedEntities.length === 0;\n    for (const item of addedItems) {\n      stateEntities[selectId(item)] = item;\n      if (!wasPreviouslyEmpty) {\n        insert(sortedEntities, item, comparer);\n      }\n    }\n    if (wasPreviouslyEmpty) {\n      sortedEntities = addedItems.slice().sort(comparer);\n    } else if (appliedUpdates) {\n      sortedEntities.sort(comparer);\n    }\n    const newSortedIds = sortedEntities.map(selectId);\n    if (!areArraysEqual(currentIds, newSortedIds)) {\n      state.ids = newSortedIds;\n    }\n  };\n  return {\n    removeOne,\n    removeMany,\n    removeAll,\n    addOne: createStateOperator(addOneMutably),\n    updateOne: createStateOperator(updateOneMutably),\n    upsertOne: createStateOperator(upsertOneMutably),\n    setOne: createStateOperator(setOneMutably),\n    setMany: createStateOperator(setManyMutably),\n    setAll: createStateOperator(setAllMutably),\n    addMany: createStateOperator(addManyMutably),\n    updateMany: createStateOperator(updateManyMutably),\n    upsertMany: createStateOperator(upsertManyMutably)\n  };\n}\n\n// src/entities/create_adapter.ts\nfunction createEntityAdapter(options = {}) {\n  const {\n    selectId,\n    sortComparer\n  } = {\n    sortComparer: false,\n    selectId: (instance) => instance.id,\n    ...options\n  };\n  const stateAdapter = sortComparer ? createSortedStateAdapter(selectId, sortComparer) : createUnsortedStateAdapter(selectId);\n  const stateFactory = createInitialStateFactory(stateAdapter);\n  const selectorsFactory = createSelectorsFactory();\n  return {\n    selectId,\n    sortComparer,\n    ...stateFactory,\n    ...selectorsFactory,\n    ...stateAdapter\n  };\n}\n\n// src/listenerMiddleware/index.ts\nimport { isAction as isAction3 } from \"redux\";\n\n// src/listenerMiddleware/exceptions.ts\nvar task = \"task\";\nvar listener = \"listener\";\nvar completed = \"completed\";\nvar cancelled = \"cancelled\";\nvar taskCancelled = `task-${cancelled}`;\nvar taskCompleted = `task-${completed}`;\nvar listenerCancelled = `${listener}-${cancelled}`;\nvar listenerCompleted = `${listener}-${completed}`;\nvar TaskAbortError = class {\n  constructor(code) {\n    this.code = code;\n    this.message = `${task} ${cancelled} (reason: ${code})`;\n  }\n  name = \"TaskAbortError\";\n  message;\n};\n\n// src/listenerMiddleware/utils.ts\nvar assertFunction = (func, expected) => {\n  if (typeof func !== \"function\") {\n    throw new TypeError(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(32) : `${expected} is not a function`);\n  }\n};\nvar noop2 = () => {\n};\nvar catchRejection = (promise, onError = noop2) => {\n  promise.catch(onError);\n  return promise;\n};\nvar addAbortSignalListener = (abortSignal, callback) => {\n  abortSignal.addEventListener(\"abort\", callback, {\n    once: true\n  });\n  return () => abortSignal.removeEventListener(\"abort\", callback);\n};\nvar abortControllerWithReason = (abortController, reason) => {\n  const signal = abortController.signal;\n  if (signal.aborted) {\n    return;\n  }\n  if (!(\"reason\" in signal)) {\n    Object.defineProperty(signal, \"reason\", {\n      enumerable: true,\n      value: reason,\n      configurable: true,\n      writable: true\n    });\n  }\n  ;\n  abortController.abort(reason);\n};\n\n// src/listenerMiddleware/task.ts\nvar validateActive = (signal) => {\n  if (signal.aborted) {\n    const {\n      reason\n    } = signal;\n    throw new TaskAbortError(reason);\n  }\n};\nfunction raceWithSignal(signal, promise) {\n  let cleanup = noop2;\n  return new Promise((resolve, reject) => {\n    const notifyRejection = () => reject(new TaskAbortError(signal.reason));\n    if (signal.aborted) {\n      notifyRejection();\n      return;\n    }\n    cleanup = addAbortSignalListener(signal, notifyRejection);\n    promise.finally(() => cleanup()).then(resolve, reject);\n  }).finally(() => {\n    cleanup = noop2;\n  });\n}\nvar runTask = async (task2, cleanUp) => {\n  try {\n    await Promise.resolve();\n    const value = await task2();\n    return {\n      status: \"ok\",\n      value\n    };\n  } catch (error) {\n    return {\n      status: error instanceof TaskAbortError ? \"cancelled\" : \"rejected\",\n      error\n    };\n  } finally {\n    cleanUp?.();\n  }\n};\nvar createPause = (signal) => {\n  return (promise) => {\n    return catchRejection(raceWithSignal(signal, promise).then((output) => {\n      validateActive(signal);\n      return output;\n    }));\n  };\n};\nvar createDelay = (signal) => {\n  const pause = createPause(signal);\n  return (timeoutMs) => {\n    return pause(new Promise((resolve) => setTimeout(resolve, timeoutMs)));\n  };\n};\n\n// src/listenerMiddleware/index.ts\nvar {\n  assign\n} = Object;\nvar INTERNAL_NIL_TOKEN = {};\nvar alm = \"listenerMiddleware\";\nvar createFork = (parentAbortSignal, parentBlockingPromises) => {\n  const linkControllers = (controller) => addAbortSignalListener(parentAbortSignal, () => abortControllerWithReason(controller, parentAbortSignal.reason));\n  return (taskExecutor, opts) => {\n    assertFunction(taskExecutor, \"taskExecutor\");\n    const childAbortController = new AbortController();\n    linkControllers(childAbortController);\n    const result = runTask(async () => {\n      validateActive(parentAbortSignal);\n      validateActive(childAbortController.signal);\n      const result2 = await taskExecutor({\n        pause: createPause(childAbortController.signal),\n        delay: createDelay(childAbortController.signal),\n        signal: childAbortController.signal\n      });\n      validateActive(childAbortController.signal);\n      return result2;\n    }, () => abortControllerWithReason(childAbortController, taskCompleted));\n    if (opts?.autoJoin) {\n      parentBlockingPromises.push(result.catch(noop2));\n    }\n    return {\n      result: createPause(parentAbortSignal)(result),\n      cancel() {\n        abortControllerWithReason(childAbortController, taskCancelled);\n      }\n    };\n  };\n};\nvar createTakePattern = (startListening, signal) => {\n  const take = async (predicate, timeout) => {\n    validateActive(signal);\n    let unsubscribe = () => {\n    };\n    const tuplePromise = new Promise((resolve, reject) => {\n      let stopListening = startListening({\n        predicate,\n        effect: (action, listenerApi) => {\n          listenerApi.unsubscribe();\n          resolve([action, listenerApi.getState(), listenerApi.getOriginalState()]);\n        }\n      });\n      unsubscribe = () => {\n        stopListening();\n        reject();\n      };\n    });\n    const promises = [tuplePromise];\n    if (timeout != null) {\n      promises.push(new Promise((resolve) => setTimeout(resolve, timeout, null)));\n    }\n    try {\n      const output = await raceWithSignal(signal, Promise.race(promises));\n      validateActive(signal);\n      return output;\n    } finally {\n      unsubscribe();\n    }\n  };\n  return (predicate, timeout) => catchRejection(take(predicate, timeout));\n};\nvar getListenerEntryPropsFrom = (options) => {\n  let {\n    type,\n    actionCreator,\n    matcher,\n    predicate,\n    effect\n  } = options;\n  if (type) {\n    predicate = createAction(type).match;\n  } else if (actionCreator) {\n    type = actionCreator.type;\n    predicate = actionCreator.match;\n  } else if (matcher) {\n    predicate = matcher;\n  } else if (predicate) {\n  } else {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(21) : \"Creating or removing a listener requires one of the known fields for matching an action\");\n  }\n  assertFunction(effect, \"options.listener\");\n  return {\n    predicate,\n    type,\n    effect\n  };\n};\nvar createListenerEntry = /* @__PURE__ */ assign((options) => {\n  const {\n    type,\n    predicate,\n    effect\n  } = getListenerEntryPropsFrom(options);\n  const entry = {\n    id: nanoid(),\n    effect,\n    type,\n    predicate,\n    pending: /* @__PURE__ */ new Set(),\n    unsubscribe: () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(22) : \"Unsubscribe not initialized\");\n    }\n  };\n  return entry;\n}, {\n  withTypes: () => createListenerEntry\n});\nvar findListenerEntry = (listenerMap, options) => {\n  const {\n    type,\n    effect,\n    predicate\n  } = getListenerEntryPropsFrom(options);\n  return Array.from(listenerMap.values()).find((entry) => {\n    const matchPredicateOrType = typeof type === \"string\" ? entry.type === type : entry.predicate === predicate;\n    return matchPredicateOrType && entry.effect === effect;\n  });\n};\nvar cancelActiveListeners = (entry) => {\n  entry.pending.forEach((controller) => {\n    abortControllerWithReason(controller, listenerCancelled);\n  });\n};\nvar createClearListenerMiddleware = (listenerMap) => {\n  return () => {\n    listenerMap.forEach(cancelActiveListeners);\n    listenerMap.clear();\n  };\n};\nvar safelyNotifyError = (errorHandler, errorToNotify, errorInfo) => {\n  try {\n    errorHandler(errorToNotify, errorInfo);\n  } catch (errorHandlerError) {\n    setTimeout(() => {\n      throw errorHandlerError;\n    }, 0);\n  }\n};\nvar addListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/add`), {\n  withTypes: () => addListener\n});\nvar clearAllListeners = /* @__PURE__ */ createAction(`${alm}/removeAll`);\nvar removeListener = /* @__PURE__ */ assign(/* @__PURE__ */ createAction(`${alm}/remove`), {\n  withTypes: () => removeListener\n});\nvar defaultErrorHandler = (...args) => {\n  console.error(`${alm}/error`, ...args);\n};\nvar createListenerMiddleware = (middlewareOptions = {}) => {\n  const listenerMap = /* @__PURE__ */ new Map();\n  const {\n    extra,\n    onError = defaultErrorHandler\n  } = middlewareOptions;\n  assertFunction(onError, \"onError\");\n  const insertEntry = (entry) => {\n    entry.unsubscribe = () => listenerMap.delete(entry.id);\n    listenerMap.set(entry.id, entry);\n    return (cancelOptions) => {\n      entry.unsubscribe();\n      if (cancelOptions?.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    };\n  };\n  const startListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options) ?? createListenerEntry(options);\n    return insertEntry(entry);\n  };\n  assign(startListening, {\n    withTypes: () => startListening\n  });\n  const stopListening = (options) => {\n    const entry = findListenerEntry(listenerMap, options);\n    if (entry) {\n      entry.unsubscribe();\n      if (options.cancelActive) {\n        cancelActiveListeners(entry);\n      }\n    }\n    return !!entry;\n  };\n  assign(stopListening, {\n    withTypes: () => stopListening\n  });\n  const notifyListener = async (entry, action, api, getOriginalState) => {\n    const internalTaskController = new AbortController();\n    const take = createTakePattern(startListening, internalTaskController.signal);\n    const autoJoinPromises = [];\n    try {\n      entry.pending.add(internalTaskController);\n      await Promise.resolve(entry.effect(\n        action,\n        // Use assign() rather than ... to avoid extra helper functions added to bundle\n        assign({}, api, {\n          getOriginalState,\n          condition: (predicate, timeout) => take(predicate, timeout).then(Boolean),\n          take,\n          delay: createDelay(internalTaskController.signal),\n          pause: createPause(internalTaskController.signal),\n          extra,\n          signal: internalTaskController.signal,\n          fork: createFork(internalTaskController.signal, autoJoinPromises),\n          unsubscribe: entry.unsubscribe,\n          subscribe: () => {\n            listenerMap.set(entry.id, entry);\n          },\n          cancelActiveListeners: () => {\n            entry.pending.forEach((controller, _, set) => {\n              if (controller !== internalTaskController) {\n                abortControllerWithReason(controller, listenerCancelled);\n                set.delete(controller);\n              }\n            });\n          },\n          cancel: () => {\n            abortControllerWithReason(internalTaskController, listenerCancelled);\n            entry.pending.delete(internalTaskController);\n          },\n          throwIfCancelled: () => {\n            validateActive(internalTaskController.signal);\n          }\n        })\n      ));\n    } catch (listenerError) {\n      if (!(listenerError instanceof TaskAbortError)) {\n        safelyNotifyError(onError, listenerError, {\n          raisedBy: \"effect\"\n        });\n      }\n    } finally {\n      await Promise.all(autoJoinPromises);\n      abortControllerWithReason(internalTaskController, listenerCompleted);\n      entry.pending.delete(internalTaskController);\n    }\n  };\n  const clearListenerMiddleware = createClearListenerMiddleware(listenerMap);\n  const middleware = (api) => (next) => (action) => {\n    if (!isAction3(action)) {\n      return next(action);\n    }\n    if (addListener.match(action)) {\n      return startListening(action.payload);\n    }\n    if (clearAllListeners.match(action)) {\n      clearListenerMiddleware();\n      return;\n    }\n    if (removeListener.match(action)) {\n      return stopListening(action.payload);\n    }\n    let originalState = api.getState();\n    const getOriginalState = () => {\n      if (originalState === INTERNAL_NIL_TOKEN) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(23) : `${alm}: getOriginalState can only be called synchronously`);\n      }\n      return originalState;\n    };\n    let result;\n    try {\n      result = next(action);\n      if (listenerMap.size > 0) {\n        const currentState = api.getState();\n        const listenerEntries = Array.from(listenerMap.values());\n        for (const entry of listenerEntries) {\n          let runListener = false;\n          try {\n            runListener = entry.predicate(action, currentState, originalState);\n          } catch (predicateError) {\n            runListener = false;\n            safelyNotifyError(onError, predicateError, {\n              raisedBy: \"predicate\"\n            });\n          }\n          if (!runListener) {\n            continue;\n          }\n          notifyListener(entry, action, api, getOriginalState);\n        }\n      }\n    } finally {\n      originalState = INTERNAL_NIL_TOKEN;\n    }\n    return result;\n  };\n  return {\n    middleware,\n    startListening,\n    stopListening,\n    clearListeners: clearListenerMiddleware\n  };\n};\n\n// src/dynamicMiddleware/index.ts\nimport { compose as compose3 } from \"redux\";\nvar createMiddlewareEntry = (middleware) => ({\n  middleware,\n  applied: /* @__PURE__ */ new Map()\n});\nvar matchInstance = (instanceId) => (action) => action?.meta?.instanceId === instanceId;\nvar createDynamicMiddleware = () => {\n  const instanceId = nanoid();\n  const middlewareMap = /* @__PURE__ */ new Map();\n  const withMiddleware = Object.assign(createAction(\"dynamicMiddleware/add\", (...middlewares) => ({\n    payload: middlewares,\n    meta: {\n      instanceId\n    }\n  })), {\n    withTypes: () => withMiddleware\n  });\n  const addMiddleware = Object.assign(function addMiddleware2(...middlewares) {\n    middlewares.forEach((middleware2) => {\n      getOrInsertComputed(middlewareMap, middleware2, createMiddlewareEntry);\n    });\n  }, {\n    withTypes: () => addMiddleware\n  });\n  const getFinalMiddleware = (api) => {\n    const appliedMiddleware = Array.from(middlewareMap.values()).map((entry) => getOrInsertComputed(entry.applied, api, entry.middleware));\n    return compose3(...appliedMiddleware);\n  };\n  const isWithMiddleware = isAllOf(withMiddleware, matchInstance(instanceId));\n  const middleware = (api) => (next) => (action) => {\n    if (isWithMiddleware(action)) {\n      addMiddleware(...action.payload);\n      return api.dispatch;\n    }\n    return getFinalMiddleware(api)(next)(action);\n  };\n  return {\n    middleware,\n    addMiddleware,\n    withMiddleware,\n    instanceId\n  };\n};\n\n// src/combineSlices.ts\nimport { combineReducers as combineReducers2 } from \"redux\";\nvar isSliceLike = (maybeSliceLike) => \"reducerPath\" in maybeSliceLike && typeof maybeSliceLike.reducerPath === \"string\";\nvar getReducers = (slices) => slices.flatMap((sliceOrMap) => isSliceLike(sliceOrMap) ? [[sliceOrMap.reducerPath, sliceOrMap.reducer]] : Object.entries(sliceOrMap));\nvar ORIGINAL_STATE = Symbol.for(\"rtk-state-proxy-original\");\nvar isStateProxy = (value) => !!value && !!value[ORIGINAL_STATE];\nvar stateProxyMap = /* @__PURE__ */ new WeakMap();\nvar createStateProxy = (state, reducerMap, initialStateCache) => getOrInsertComputed(stateProxyMap, state, () => new Proxy(state, {\n  get: (target, prop, receiver) => {\n    if (prop === ORIGINAL_STATE) return target;\n    const result = Reflect.get(target, prop, receiver);\n    if (typeof result === \"undefined\") {\n      const cached = initialStateCache[prop];\n      if (typeof cached !== \"undefined\") return cached;\n      const reducer = reducerMap[prop];\n      if (reducer) {\n        const reducerResult = reducer(void 0, {\n          type: nanoid()\n        });\n        if (typeof reducerResult === \"undefined\") {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(24) : `The slice reducer for key \"${prop.toString()}\" returned undefined when called for selector(). If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n        }\n        initialStateCache[prop] = reducerResult;\n        return reducerResult;\n      }\n    }\n    return result;\n  }\n}));\nvar original = (state) => {\n  if (!isStateProxy(state)) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(25) : \"original must be used on state Proxy\");\n  }\n  return state[ORIGINAL_STATE];\n};\nvar emptyObject = {};\nvar noopReducer = (state = emptyObject) => state;\nfunction combineSlices(...slices) {\n  const reducerMap = Object.fromEntries(getReducers(slices));\n  const getReducer = () => Object.keys(reducerMap).length ? combineReducers2(reducerMap) : noopReducer;\n  let reducer = getReducer();\n  function combinedReducer(state, action) {\n    return reducer(state, action);\n  }\n  combinedReducer.withLazyLoadedSlices = () => combinedReducer;\n  const initialStateCache = {};\n  const inject = (slice, config = {}) => {\n    const {\n      reducerPath,\n      reducer: reducerToInject\n    } = slice;\n    const currentReducer = reducerMap[reducerPath];\n    if (!config.overrideExisting && currentReducer && currentReducer !== reducerToInject) {\n      if (typeof process !== \"undefined\" && process.env.NODE_ENV === \"development\") {\n        console.error(`called \\`inject\\` to override already-existing reducer ${reducerPath} without specifying \\`overrideExisting: true\\``);\n      }\n      return combinedReducer;\n    }\n    if (config.overrideExisting && currentReducer !== reducerToInject) {\n      delete initialStateCache[reducerPath];\n    }\n    reducerMap[reducerPath] = reducerToInject;\n    reducer = getReducer();\n    return combinedReducer;\n  };\n  const selector = Object.assign(function makeSelector(selectorFn, selectState) {\n    return function selector2(state, ...args) {\n      return selectorFn(createStateProxy(selectState ? selectState(state, ...args) : state, reducerMap, initialStateCache), ...args);\n    };\n  }, {\n    original\n  });\n  return Object.assign(combinedReducer, {\n    inject,\n    selector\n  });\n}\n\n// src/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux Toolkit error #${code}; visit https://redux-toolkit.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\nexport {\n  ReducerType,\n  SHOULD_AUTOBATCH,\n  TaskAbortError,\n  Tuple,\n  addListener,\n  asyncThunkCreator,\n  autoBatchEnhancer,\n  buildCreateSlice,\n  clearAllListeners,\n  combineSlices,\n  configureStore,\n  createAction,\n  createActionCreatorInvariantMiddleware,\n  createAsyncThunk,\n  createDraftSafeSelector,\n  createDraftSafeSelectorCreator,\n  createDynamicMiddleware,\n  createEntityAdapter,\n  createImmutableStateInvariantMiddleware,\n  createListenerMiddleware,\n  produce as createNextState,\n  createReducer,\n  createSelector,\n  createSelectorCreator2 as createSelectorCreator,\n  createSerializableStateInvariantMiddleware,\n  createSlice,\n  current3 as current,\n  findNonSerializableValue,\n  formatProdErrorMessage,\n  freeze,\n  isActionCreator,\n  isAllOf,\n  isAnyOf,\n  isAsyncThunkAction,\n  isDraft5 as isDraft,\n  isFSA as isFluxStandardAction,\n  isFulfilled,\n  isImmutableDefault,\n  isPending,\n  isPlain,\n  isRejected,\n  isRejectedWithValue,\n  lruMemoize,\n  miniSerializeError,\n  nanoid,\n  original2 as original,\n  prepareAutoBatched,\n  removeListener,\n  unwrapResult,\n  weakMapMemoize2 as weakMapMemoize\n};\n//# sourceMappingURL=redux-toolkit.modern.mjs.map", "!function(t,e){\"object\"==typeof exports&&\"object\"==typeof module?module.exports=e():\"function\"==typeof define&&define.amd?define([],e):\"object\"==typeof exports?exports.Defuddle=e():t.Defuddle=e()}(\"undefined\"!=typeof self?self:this,(()=>(()=>{\"use strict\";var t={0:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.mathRules=e.createCleanMathEl=void 0;const n=r(282);e.createCleanMathEl=(t,e,r,n)=>{const o=t.createElement(\"math\");if(o.setAttribute(\"xmlns\",\"http://www.w3.org/1998/Math/MathML\"),o.setAttribute(\"display\",n?\"block\":\"inline\"),o.setAttribute(\"data-latex\",r||\"\"),null==e?void 0:e.mathml){const r=t.createElement(\"div\");r.innerHTML=e.mathml;const n=r.querySelector(\"math\");n&&(o.innerHTML=n.innerHTML)}else r&&(o.textContent=r);return o},e.mathRules=[{selector:n.mathSelectors,element:\"math\",transform:(t,r)=>{if(!function(t){return\"classList\"in t&&\"getAttribute\"in t&&\"querySelector\"in t}(t))return t;const o=(0,n.getMathMLFromElement)(t),i=(0,n.getBasicLatexFromElement)(t),a=(0,n.isBlockDisplay)(t),s=(0,e.createCleanMathEl)(r,o,i,a);if(t.parentElement){t.parentElement.querySelectorAll('\\n\\t\\t\\t\\t\\t/* MathJax scripts and previews */\\n\\t\\t\\t\\t\\tscript[type^=\"math/\"],\\n\\t\\t\\t\\t\\t.MathJax_Preview,\\n\\n\\t\\t\\t\\t\\t/* External math library scripts */\\n\\t\\t\\t\\t\\tscript[type=\"text/javascript\"][src*=\"mathjax\"],\\n\\t\\t\\t\\t\\tscript[type=\"text/javascript\"][src*=\"katex\"]\\n\\t\\t\\t\\t').forEach((t=>t.remove()))}return s}}]},20:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.GrokExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.messageContainerSelector=\".relative.group.flex.flex-col.justify-center.w-full\",this.messageBubbles=t.querySelectorAll(this.messageContainerSelector),this.footnotes=[],this.footnoteCounter=0}canExtract(){return!!this.messageBubbles&&this.messageBubbles.length>0}extractMessages(){const t=[];return this.footnotes=[],this.footnoteCounter=0,this.messageBubbles&&0!==this.messageBubbles.length?(this.messageBubbles.forEach((e=>{var r;const n=e.classList.contains(\"items-end\"),o=e.classList.contains(\"items-start\");if(!n&&!o)return;const i=e.querySelector(\".message-bubble\");if(!i)return;let a=\"\",s=\"\",l=\"\";if(n)a=i.textContent||\"\",s=\"user\",l=\"You\";else if(o){s=\"assistant\",l=\"Grok\";const t=i.cloneNode(!0);null===(r=t.querySelector(\".relative.border.border-border-l1.bg-surface-base\"))||void 0===r||r.remove(),a=t.innerHTML,a=this.processFootnotes(a)}a.trim()&&t.push({author:l,content:a.trim(),metadata:{role:s}})})),t):t}getFootnotes(){return this.footnotes}getMetadata(){var t;const e=this.getTitle(),r=(null===(t=this.messageBubbles)||void 0===t?void 0:t.length)||0;return{title:e,site:\"Grok\",url:this.url,messageCount:r,description:`Grok conversation with ${r} messages`}}getTitle(){var t,e;const r=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(r&&\"Grok\"!==r&&!r.startsWith(\"Grok by \"))return r.replace(/\\s-\\s*Grok$/,\"\").trim();const n=this.document.querySelector(`${this.messageContainerSelector}.items-end`);if(n){const t=n.querySelector(\".message-bubble\");if(t){const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||\"\";return r.length>50?r.slice(0,50)+\"...\":r}}return\"Grok Conversation\"}processFootnotes(t){return t.replace(/<a\\s+(?:[^>]*?\\s+)?href=\"([^\"]*)\"[^>]*>(.*?)<\\/a>/gi,((t,e,r)=>{if(!e||e.startsWith(\"#\")||!e.match(/^https?:\\/\\//i))return t;let n;if(this.footnotes.find((t=>t.url===e)))n=this.footnotes.findIndex((t=>t.url===e))+1;else{this.footnoteCounter++,n=this.footnoteCounter;let t=e;try{const r=new URL(e).hostname.replace(/^www\\./,\"\");t=`<a href=\"${e}\" target=\"_blank\" rel=\"noopener noreferrer\">${r}</a>`}catch(r){t=`<a href=\"${e}\" target=\"_blank\" rel=\"noopener noreferrer\">${e}</a>`,console.warn(`GrokExtractor: Could not parse URL for footnote: ${e}`)}this.footnotes.push({url:e,text:t})}return`${r}<sup id=\"fnref:${n}\" class=\"footnote-ref\"><a href=\"#fn:${n}\" class=\"footnote-link\">${n}</a></sup>`}))}}e.GrokExtractor=o},181:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.ConversationExtractor=void 0;const n=r(279),o=r(628);class i extends n.BaseExtractor{getFootnotes(){return[]}extract(){var t;const e=this.extractMessages(),r=this.getMetadata(),n=this.getFootnotes(),i=this.createContentHtml(e,n),a=document.implementation.createHTMLDocument(),s=a.createElement(\"article\");s.innerHTML=i,a.body.appendChild(s);const l=new o.Defuddle(a).parse(),c=l.content;return{content:c,contentHtml:c,extractedContent:{messageCount:e.length.toString()},variables:{title:r.title||\"Conversation\",site:r.site,description:r.description||`${r.site} conversation with ${e.length} messages`,wordCount:(null===(t=l.wordCount)||void 0===t?void 0:t.toString())||\"\"}}}createContentHtml(t,e){return`${t.map(((e,r)=>{const n=e.timestamp?`<div class=\"message-timestamp\">${e.timestamp}</div>`:\"\",o=/<p[^>]*>[\\s\\S]*?<\\/p>/i.test(e.content)?e.content:`<p>${e.content}</p>`,i=e.metadata?Object.entries(e.metadata).map((([t,e])=>`data-${t}=\"${e}\"`)).join(\" \"):\"\";return`\\n\\t\\t\\t<div class=\"message message-${e.author.toLowerCase()}\" ${i}>\\n\\t\\t\\t\\t<div class=\"message-header\">\\n\\t\\t\\t\\t\\t<p class=\"message-author\"><strong>${e.author}</strong></p>\\n\\t\\t\\t\\t\\t${n}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t<div class=\"message-content\">\\n\\t\\t\\t\\t\\t${o}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t</div>${r<t.length-1?\"\\n<hr>\":\"\"}`})).join(\"\\n\").trim()}\\n${e.length>0?`\\n\\t\\t\\t<div id=\"footnotes\">\\n\\t\\t\\t\\t<ol>\\n\\t\\t\\t\\t\\t${e.map(((t,e)=>`\\n\\t\\t\\t\\t\\t\\t<li class=\"footnote\" id=\"fn:${e+1}\">\\n\\t\\t\\t\\t\\t\\t\\t<p>\\n\\t\\t\\t\\t\\t\\t\\t\\t<a href=\"${t.url}\" target=\"_blank\">${t.text}</a>&nbsp;<a href=\"#fnref:${e+1}\" class=\"footnote-backref\">\\u21a9</a>\\n\\t\\t\\t\\t\\t\\t\\t</p>\\n\\t\\t\\t\\t\\t\\t</li>\\n\\t\\t\\t\\t\\t`)).join(\"\")}\\n\\t\\t\\t\\t</ol>\\n\\t\\t\\t</div>`:\"\"}`.trim()}}e.ConversationExtractor=i},248:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.TwitterExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){var r;super(t,e),this.mainTweet=null,this.threadTweets=[];const n=t.querySelector('[aria-label=\"Timeline: Conversation\"]');if(!n){const e=t.querySelector('article[data-testid=\"tweet\"]');return void(e&&(this.mainTweet=e))}const o=Array.from(n.querySelectorAll('article[data-testid=\"tweet\"]')),i=null===(r=n.querySelector(\"section, h2\"))||void 0===r?void 0:r.parentElement;i&&o.forEach(((t,e)=>{if(i.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING)return o.splice(e),!1})),this.mainTweet=o[0]||null,this.threadTweets=o.slice(1)}canExtract(){return!!this.mainTweet}extract(){const t=this.extractTweet(this.mainTweet),e=this.threadTweets.map((t=>this.extractTweet(t))).join(\"\\n<hr>\\n\"),r=`\\n\\t\\t\\t<div class=\"tweet-thread\">\\n\\t\\t\\t\\t<div class=\"main-tweet\">\\n\\t\\t\\t\\t\\t${t}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t${e?`\\n\\t\\t\\t\\t\\t<hr>\\n\\t\\t\\t\\t\\t<div class=\"thread-tweets\">\\n\\t\\t\\t\\t\\t\\t${e}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t`:\"\"}\\n\\t\\t\\t</div>\\n\\t\\t`.trim(),n=this.getTweetId(),o=this.getTweetAuthor();return{content:r,contentHtml:r,extractedContent:{tweetId:n,tweetAuthor:o},variables:{title:`Thread by ${o}`,author:o,site:\"X (Twitter)\",description:this.createDescription(this.mainTweet)}}}formatTweetText(t){if(!t)return\"\";const e=document.createElement(\"div\");e.innerHTML=t,e.querySelectorAll(\"a\").forEach((t=>{var e;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||\"\";t.replaceWith(r)})),e.querySelectorAll(\"span, div\").forEach((t=>{t.replaceWith(...Array.from(t.childNodes))}));return e.innerHTML.split(\"\\n\").map((t=>t.trim())).filter((t=>t)).map((t=>`<p>${t}</p>`)).join(\"\\n\")}extractTweet(t){var e,r,n;if(!t)return\"\";const o=t.cloneNode(!0);o.querySelectorAll('img[src*=\"/emoji/\"]').forEach((t=>{if(\"img\"===t.tagName.toLowerCase()&&t.getAttribute(\"alt\")){const e=t.getAttribute(\"alt\");e&&t.replaceWith(e)}}));const i=(null===(e=o.querySelector('[data-testid=\"tweetText\"]'))||void 0===e?void 0:e.innerHTML)||\"\",a=this.formatTweetText(i),s=this.extractImages(t),l=this.extractUserInfo(t),c=null===(n=null===(r=t.querySelector('[aria-labelledby*=\"id__\"]'))||void 0===r?void 0:r.querySelector('[data-testid=\"User-Name\"]'))||void 0===n?void 0:n.closest('[aria-labelledby*=\"id__\"]'),u=c?this.extractTweet(c):\"\";return`\\n\\t\\t\\t<div class=\"tweet\">\\n\\t\\t\\t\\t<div class=\"tweet-header\">\\n\\t\\t\\t\\t\\t<span class=\"tweet-author\"><strong>${l.fullName}</strong> <span class=\"tweet-handle\">${l.handle}</span></span>\\n\\t\\t\\t\\t\\t${l.date?`<a href=\"${l.permalink}\" class=\"tweet-date\">${l.date}</a>`:\"\"}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t${a?`<div class=\"tweet-text\">${a}</div>`:\"\"}\\n\\t\\t\\t\\t${s.length?`\\n\\t\\t\\t\\t\\t<div class=\"tweet-media\">\\n\\t\\t\\t\\t\\t\\t${s.join(\"\\n\")}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t`:\"\"}\\n\\t\\t\\t\\t${u?`\\n\\t\\t\\t\\t\\t<blockquote class=\"quoted-tweet\">\\n\\t\\t\\t\\t\\t\\t${u}\\n\\t\\t\\t\\t\\t</blockquote>\\n\\t\\t\\t\\t`:\"\"}\\n\\t\\t\\t</div>\\n\\t\\t`.trim()}extractUserInfo(t){var e,r,n,o,i,a,s,l,c;const u=t.querySelector('[data-testid=\"User-Name\"]');if(!u)return{fullName:\"\",handle:\"\",date:\"\",permalink:\"\"};const d=u.querySelectorAll(\"a\");let m=(null===(r=null===(e=null==d?void 0:d[0])||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||\"\",h=(null===(o=null===(n=null==d?void 0:d[1])||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||\"\";m&&h||(m=(null===(a=null===(i=u.querySelector('span[style*=\"color: rgb(15, 20, 25)\"] span'))||void 0===i?void 0:i.textContent)||void 0===a?void 0:a.trim())||\"\",h=(null===(l=null===(s=u.querySelector('span[style*=\"color: rgb(83, 100, 113)\"]'))||void 0===s?void 0:s.textContent)||void 0===l?void 0:l.trim())||\"\");const p=t.querySelector(\"time\"),g=(null==p?void 0:p.getAttribute(\"datetime\"))||\"\";return{fullName:m,handle:h,date:g?new Date(g).toISOString().split(\"T\")[0]:\"\",permalink:(null===(c=null==p?void 0:p.closest(\"a\"))||void 0===c?void 0:c.href)||\"\"}}extractImages(t){var e,r;const n=['[data-testid=\"tweetPhoto\"]','[data-testid=\"tweet-image\"]','img[src*=\"media\"]'],o=[],i=null===(r=null===(e=t.querySelector('[aria-labelledby*=\"id__\"]'))||void 0===e?void 0:e.querySelector('[data-testid=\"User-Name\"]'))||void 0===r?void 0:r.closest('[aria-labelledby*=\"id__\"]');for(const e of n){t.querySelectorAll(e).forEach((t=>{var e,r;if(!(null==i?void 0:i.contains(t))&&\"img\"===t.tagName.toLowerCase()&&t.getAttribute(\"alt\")){const n=(null===(e=t.getAttribute(\"src\"))||void 0===e?void 0:e.replace(/&name=\\w+$/,\"&name=large\"))||\"\",i=(null===(r=t.getAttribute(\"alt\"))||void 0===r?void 0:r.replace(/\\s+/g,\" \").trim())||\"\";o.push(`<img src=\"${n}\" alt=\"${i}\" />`)}}))}return o}getTweetId(){const t=this.url.match(/status\\/(\\d+)/);return(null==t?void 0:t[1])||\"\"}getTweetAuthor(){var t,e,r;const n=null===(t=this.mainTweet)||void 0===t?void 0:t.querySelector('[data-testid=\"User-Name\"]'),o=null==n?void 0:n.querySelectorAll(\"a\"),i=(null===(r=null===(e=null==o?void 0:o[1])||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||\"\";return i.startsWith(\"@\")?i:`@${i}`}createDescription(t){var e;if(!t)return\"\";return((null===(e=t.querySelector('[data-testid=\"tweetText\"]'))||void 0===e?void 0:e.textContent)||\"\").trim().slice(0,140).replace(/\\s+/g,\" \")}}e.TwitterExtractor=o},258:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.YoutubeExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e,r){super(t,e,r),this.videoElement=t.querySelector(\"video\"),this.schemaOrgData=r}canExtract(){return!0}extract(){const t=this.getVideoData(),e=t.description||\"\",r=this.formatDescription(e),n=`<iframe width=\"560\" height=\"315\" src=\"https://www.youtube.com/embed/${this.getVideoId()}?si=_m0qv33lAuJFoGNh\" title=\"YouTube video player\" frameborder=\"0\" allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\" referrerpolicy=\"strict-origin-when-cross-origin\" allowfullscreen></iframe><br>${r}`;return{content:n,contentHtml:n,extractedContent:{videoId:this.getVideoId(),author:t.author||\"\"},variables:{title:t.name||\"\",author:t.author||\"\",site:\"YouTube\",image:Array.isArray(t.thumbnailUrl)&&t.thumbnailUrl[0]||\"\",published:t.uploadDate,description:e.slice(0,200).trim()}}}formatDescription(t){return`<p>${t.replace(/\\n/g,\"<br>\")}</p>`}getVideoData(){if(!this.schemaOrgData)return{};return(Array.isArray(this.schemaOrgData)?this.schemaOrgData.find((t=>\"VideoObject\"===t[\"@type\"])):\"VideoObject\"===this.schemaOrgData[\"@type\"]?this.schemaOrgData:null)||{}}getVideoId(){return new URLSearchParams(new URL(this.url).search).get(\"v\")||\"\"}}e.YoutubeExtractor=o},279:(t,e)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.BaseExtractor=void 0;e.BaseExtractor=class{constructor(t,e,r){this.document=t,this.url=e,this.schemaOrgData=r}}},282:(t,e)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.mathSelectors=e.isBlockDisplay=e.getBasicLatexFromElement=e.getMathMLFromElement=void 0;e.getMathMLFromElement=t=>{if(\"math\"===t.tagName.toLowerCase()){const e=\"block\"===t.getAttribute(\"display\");return{mathml:t.outerHTML,latex:t.getAttribute(\"alttext\")||null,isBlock:e}}const e=t.getAttribute(\"data-mathml\");if(e){const t=document.createElement(\"div\");t.innerHTML=e;const r=t.querySelector(\"math\");if(r){const t=\"block\"===r.getAttribute(\"display\");return{mathml:r.outerHTML,latex:r.getAttribute(\"alttext\")||null,isBlock:t}}}const r=t.querySelector(\".MJX_Assistive_MathML, mjx-assistive-mml\");if(r){const t=r.querySelector(\"math\");if(t){const e=t.getAttribute(\"display\"),n=r.getAttribute(\"display\"),o=\"block\"===e||\"block\"===n;return{mathml:t.outerHTML,latex:t.getAttribute(\"alttext\")||null,isBlock:o}}}const n=t.querySelector(\".katex-mathml math\");return n?{mathml:n.outerHTML,latex:null,isBlock:!1}:null};e.getBasicLatexFromElement=t=>{var e,r,n;const o=t.getAttribute(\"data-latex\");if(o)return o;if(\"img\"===t.tagName.toLowerCase()&&t.classList.contains(\"latex\")){const e=t.getAttribute(\"alt\");if(e)return e;const r=t.getAttribute(\"src\");if(r){const t=r.match(/latex\\.php\\?latex=([^&]+)/);if(t)return decodeURIComponent(t[1]).replace(/\\+/g,\" \").replace(/%5C/g,\"\\\\\")}}const i=t.querySelector('annotation[encoding=\"application/x-tex\"]');if(null==i?void 0:i.textContent)return i.textContent.trim();if(t.matches(\".katex\")){const e=t.querySelector('.katex-mathml annotation[encoding=\"application/x-tex\"]');if(null==e?void 0:e.textContent)return e.textContent.trim()}if(t.matches('script[type=\"math/tex\"]')||t.matches('script[type=\"math/tex; mode=display\"]'))return(null===(e=t.textContent)||void 0===e?void 0:e.trim())||null;if(t.parentElement){const e=t.parentElement.querySelector('script[type=\"math/tex\"], script[type=\"math/tex; mode=display\"]');if(e)return(null===(r=e.textContent)||void 0===r?void 0:r.trim())||null}return t.getAttribute(\"alt\")||(null===(n=t.textContent)||void 0===n?void 0:n.trim())||null};e.isBlockDisplay=t=>{if(\"block\"===t.getAttribute(\"display\"))return!0;const e=t.className.toLowerCase();if(e.includes(\"display\")||e.includes(\"block\"))return!0;if(t.closest('.katex-display, .MathJax_Display, [data-display=\"block\"]'))return!0;const r=t.previousElementSibling;if(\"p\"===(null==r?void 0:r.tagName.toLowerCase()))return!0;if(t.matches(\".mwe-math-fallback-image-display\"))return!0;if(t.matches(\".katex\"))return null!==t.closest(\".katex-display\");if(t.hasAttribute(\"display\"))return\"true\"===t.getAttribute(\"display\");if(t.matches('script[type=\"math/tex; mode=display\"]'))return!0;if(t.hasAttribute(\"display\"))return\"true\"===t.getAttribute(\"display\");const n=t.closest(\"[display]\");return!!n&&\"true\"===n.getAttribute(\"display\")},e.mathSelectors=['img.latex[src*=\"latex.php\"]',\"span.MathJax\",\"mjx-container\",'script[type=\"math/tex\"]','script[type=\"math/tex; mode=display\"]','.MathJax_Preview + script[type=\"math/tex\"]',\".MathJax_Display\",\".MathJax_SVG\",\".MathJax_MathML\",\".mwe-math-element\",\".mwe-math-fallback-image-inline\",\".mwe-math-fallback-image-display\",\".mwe-math-mathml-inline\",\".mwe-math-mathml-display\",\".katex\",\".katex-display\",\".katex-mathml\",\".katex-html\",\"[data-katex]\",'script[type=\"math/katex\"]',\"math\",\"[data-math]\",\"[data-latex]\",\"[data-tex]\",'script[type^=\"math/\"]','annotation[encoding=\"application/x-tex\"]'].join(\",\")},397:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.ClaudeExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.articles=t.querySelectorAll('div[data-testid=\"user-message\"], div[data-testid=\"assistant-message\"], div.font-claude-message')}canExtract(){return!!this.articles&&this.articles.length>0}extractMessages(){const t=[];return this.articles?(this.articles.forEach((e=>{let r,n;if(e.hasAttribute(\"data-testid\")){if(\"user-message\"!==e.getAttribute(\"data-testid\"))return;r=\"you\",n=e.innerHTML}else{if(!e.classList.contains(\"font-claude-message\"))return;r=\"assistant\",n=e.innerHTML}n&&t.push({author:\"you\"===r?\"You\":\"Claude\",content:n.trim(),metadata:{role:r}})})),t):t}getMetadata(){const t=this.getTitle(),e=this.extractMessages();return{title:t,site:\"Claude\",url:this.url,messageCount:e.length,description:`Claude conversation with ${e.length} messages`}}getTitle(){var t,e,r,n,o;const i=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(i&&\"Claude\"!==i)return i.replace(/ - Claude$/,\"\");const a=null===(r=null===(e=this.document.querySelector(\"header .font-tiempos\"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim();if(a)return a;const s=null===(o=null===(n=this.articles)||void 0===n?void 0:n.item(0))||void 0===o?void 0:o.querySelector('[data-testid=\"user-message\"]');if(s){const t=s.textContent||\"\";return t.length>50?t.slice(0,50)+\"...\":t}return\"Claude Conversation\"}}e.ClaudeExtractor=o},458:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.HackerNewsExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){super(t,e),this.mainPost=t.querySelector(\".fatitem\"),this.isCommentPage=this.detectCommentPage(),this.mainComment=this.isCommentPage?this.findMainComment():null}detectCommentPage(){var t;return!!(null===(t=this.mainPost)||void 0===t?void 0:t.querySelector('.navs a[href*=\"parent\"]'))}findMainComment(){var t;return(null===(t=this.mainPost)||void 0===t?void 0:t.querySelector(\".comment\"))||null}canExtract(){return!!this.mainPost}extract(){const t=this.getPostContent(),e=this.extractComments(),r=this.createContentHtml(t,e),n=this.getPostTitle(),o=this.getPostAuthor(),i=this.createDescription(),a=this.getPostDate();return{content:r,contentHtml:r,extractedContent:{postId:this.getPostId(),postAuthor:o},variables:{title:n,author:o,site:\"Hacker News\",description:i,published:a}}}createContentHtml(t,e){return`\\n\\t\\t\\t<div class=\"hackernews-post\">\\n\\t\\t\\t\\t<div class=\"post-content\">\\n\\t\\t\\t\\t\\t${t}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t${e?`\\n\\t\\t\\t\\t\\t<hr>\\n\\t\\t\\t\\t\\t<h2>Comments</h2>\\n\\t\\t\\t\\t\\t<div class=\"hackernews-comments\">\\n\\t\\t\\t\\t\\t\\t${e}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t`:\"\"}\\n\\t\\t\\t</div>\\n\\t\\t`.trim()}getPostContent(){var t,e,r,n,o,i;if(!this.mainPost)return\"\";if(this.isCommentPage&&this.mainComment){const i=(null===(t=this.mainComment.querySelector(\".hnuser\"))||void 0===t?void 0:t.textContent)||\"[deleted]\",a=(null===(e=this.mainComment.querySelector(\".commtext\"))||void 0===e?void 0:e.innerHTML)||\"\",s=this.mainComment.querySelector(\".age\"),l=((null==s?void 0:s.getAttribute(\"title\"))||\"\").split(\"T\")[0]||\"\",c=(null===(n=null===(r=this.mainComment.querySelector(\".score\"))||void 0===r?void 0:r.textContent)||void 0===n?void 0:n.trim())||\"\",u=(null===(o=this.mainPost.querySelector('.navs a[href*=\"parent\"]'))||void 0===o?void 0:o.getAttribute(\"href\"))||\"\";return`\\n\\t\\t\\t\\t<div class=\"comment main-comment\">\\n\\t\\t\\t\\t\\t<div class=\"comment-metadata\">\\n\\t\\t\\t\\t\\t\\t<span class=\"comment-author\"><strong>${i}</strong></span> \\u2022\\n\\t\\t\\t\\t\\t\\t<span class=\"comment-date\">${l}</span>\\n\\t\\t\\t\\t\\t\\t${c?` \\u2022 <span class=\"comment-points\">${c}</span>`:\"\"}\\n\\t\\t\\t\\t\\t\\t${u?` \\u2022 <a href=\"https://news.ycombinator.com/${u}\" class=\"parent-link\">parent</a>`:\"\"}\\n\\t\\t\\t\\t\\t</div>\\n\\t\\t\\t\\t\\t<div class=\"comment-content\">${a}</div>\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t`.trim()}const a=this.mainPost.querySelector(\"tr.athing\"),s=(null==a||a.nextElementSibling,(null===(i=null==a?void 0:a.querySelector(\".titleline a\"))||void 0===i?void 0:i.getAttribute(\"href\"))||\"\");let l=\"\";s&&(l+=`<p><a href=\"${s}\" target=\"_blank\">${s}</a></p>`);const c=this.mainPost.querySelector(\".toptext\");return c&&(l+=`<div class=\"post-text\">${c.innerHTML}</div>`),l}extractComments(){const t=Array.from(this.document.querySelectorAll(\"tr.comtr\"));return this.processComments(t)}processComments(t){var e,r,n,o;let i=\"\";const a=new Set;let s=-1,l=[];for(const c of t){const t=c.getAttribute(\"id\");if(!t||a.has(t))continue;a.add(t);const u=(null===(e=c.querySelector(\".ind img\"))||void 0===e?void 0:e.getAttribute(\"width\"))||\"0\",d=parseInt(u)/40,m=c.querySelector(\".commtext\"),h=(null===(r=c.querySelector(\".hnuser\"))||void 0===r?void 0:r.textContent)||\"[deleted]\",p=c.querySelector(\".age\"),g=(null===(o=null===(n=c.querySelector(\".score\"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||\"\";if(!m)continue;const f=`https://news.ycombinator.com/item?id=${t}`,v=((null==p?void 0:p.getAttribute(\"title\"))||\"\").split(\"T\")[0]||\"\";if(0===d){for(;l.length>0;)i+=\"</blockquote>\",l.pop();i+=\"<blockquote>\",l=[0],s=0}else if(d<s)for(;l.length>0&&l[l.length-1]>=d;)i+=\"</blockquote>\",l.pop();else d>s&&(i+=\"<blockquote>\",l.push(d));i+=`<div class=\"comment\">\\n\\t<div class=\"comment-metadata\">\\n\\t\\t<span class=\"comment-author\"><strong>${h}</strong></span> \\u2022\\n\\t\\t<a href=\"${f}\" class=\"comment-link\">${v}</a>\\n\\t\\t${g?` \\u2022 <span class=\"comment-points\">${g}</span>`:\"\"}\\n\\t</div>\\n\\t<div class=\"comment-content\">${m.innerHTML}</div>\\n</div>`,s=d}for(;l.length>0;)i+=\"</blockquote>\",l.pop();return i}getPostId(){const t=this.url.match(/id=(\\d+)/);return(null==t?void 0:t[1])||\"\"}getPostTitle(){var t,e,r,n,o;if(this.isCommentPage&&this.mainComment){const r=(null===(t=this.mainComment.querySelector(\".hnuser\"))||void 0===t?void 0:t.textContent)||\"[deleted]\",n=(null===(e=this.mainComment.querySelector(\".commtext\"))||void 0===e?void 0:e.textContent)||\"\";return`Comment by ${r}: ${n.trim().slice(0,50)+(n.length>50?\"...\":\"\")}`}return(null===(o=null===(n=null===(r=this.mainPost)||void 0===r?void 0:r.querySelector(\".titleline\"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||\"\"}getPostAuthor(){var t,e,r;return(null===(r=null===(e=null===(t=this.mainPost)||void 0===t?void 0:t.querySelector(\".hnuser\"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||\"\"}createDescription(){const t=this.getPostTitle(),e=this.getPostAuthor();return this.isCommentPage?`Comment by ${e} on Hacker News`:`${t} - by ${e} on Hacker News`}getPostDate(){if(!this.mainPost)return\"\";const t=this.mainPost.querySelector(\".age\");return((null==t?void 0:t.getAttribute(\"title\"))||\"\").split(\"T\")[0]||\"\"}}e.HackerNewsExtractor=o},552:(t,e)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.isElement=function(t){return t.nodeType===r.ELEMENT_NODE},e.isTextNode=function(t){return t.nodeType===r.TEXT_NODE},e.isCommentNode=function(t){return t.nodeType===r.COMMENT_NODE},e.getComputedStyle=function(t){const e=n(t.ownerDocument);return e?e.getComputedStyle(t):null},e.getWindow=n,e.logDebug=function(t,...e){\"undefined\"!=typeof window&&window.defuddleDebug&&console.log(\"Defuddle:\",t,...e)};const r={ELEMENT_NODE:1,ATTRIBUTE_NODE:2,TEXT_NODE:3,CDATA_SECTION_NODE:4,ENTITY_REFERENCE_NODE:5,ENTITY_NODE:6,PROCESSING_INSTRUCTION_NODE:7,COMMENT_NODE:8,DOCUMENT_NODE:9,DOCUMENT_TYPE_NODE:10,DOCUMENT_FRAGMENT_NODE:11,NOTATION_NODE:12};function n(t){return t.defaultView?t.defaultView:t.ownerWindow?t.ownerWindow:t.window?t.window:null}},608:(t,e)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.MetadataExtractor=void 0;e.MetadataExtractor=class{static extract(t,e,r){var n,o;let i=\"\",a=\"\";try{if(a=(null===(n=t.location)||void 0===n?void 0:n.href)||\"\",a||(a=this.getMetaContent(r,\"property\",\"og:url\")||this.getMetaContent(r,\"property\",\"twitter:url\")||this.getSchemaProperty(e,\"url\")||this.getSchemaProperty(e,\"mainEntityOfPage.url\")||this.getSchemaProperty(e,\"mainEntity.url\")||this.getSchemaProperty(e,\"WebSite.url\")||(null===(o=t.querySelector('link[rel=\"canonical\"]'))||void 0===o?void 0:o.getAttribute(\"href\"))||\"\"),a)try{i=new URL(a).hostname.replace(/^www\\./,\"\")}catch(t){console.warn(\"Failed to parse URL:\",t)}}catch(e){const r=t.querySelector(\"base[href]\");if(r)try{a=r.getAttribute(\"href\")||\"\",i=new URL(a).hostname.replace(/^www\\./,\"\")}catch(t){console.warn(\"Failed to parse base URL:\",t)}}return{title:this.getTitle(t,e,r),description:this.getDescription(t,e,r),domain:i,favicon:this.getFavicon(t,a,r),image:this.getImage(t,e,r),published:this.getPublished(t,e,r),author:this.getAuthor(t,e,r),site:this.getSite(t,e,r),schemaOrgData:e,wordCount:0,parseTime:0}}static getAuthor(t,e,r){let n;if(n=this.getMetaContent(r,\"name\",\"sailthru.author\")||this.getMetaContent(r,\"property\",\"author\")||this.getMetaContent(r,\"name\",\"author\")||this.getMetaContent(r,\"name\",\"byl\")||this.getMetaContent(r,\"name\",\"authorList\"),n)return n;let o=this.getSchemaProperty(e,\"author.name\")||this.getSchemaProperty(e,\"author.[].name\");if(o){const t=o.split(\",\").map((t=>t.trim().replace(/,$/,\"\").trim())).filter(Boolean);if(t.length>0){let e=[...new Set(t)];return e.length>10&&(e=e.slice(0,10)),e.join(\", \")}}const i=[];if(['[itemprop=\"author\"]',\".author\",'[href*=\"author\"]',\".authors a\"].forEach((e=>{t.querySelectorAll(e).forEach((t=>{var e;(e=t.textContent)&&e.split(\",\").forEach((t=>{const e=t.trim().replace(/,$/,\"\").trim(),r=e.toLowerCase();e&&\"author\"!==r&&\"authors\"!==r&&i.push(e)}))}))})),i.length>0){let t=[...new Set(i.map((t=>t.trim())).filter(Boolean))];if(t.length>0)return t.length>10&&(t=t.slice(0,10)),t.join(\", \")}return n=this.getMetaContent(r,\"name\",\"copyright\")||this.getSchemaProperty(e,\"copyrightHolder.name\")||this.getMetaContent(r,\"property\",\"og:site_name\")||this.getSchemaProperty(e,\"publisher.name\")||this.getSchemaProperty(e,\"sourceOrganization.name\")||this.getSchemaProperty(e,\"isPartOf.name\")||this.getMetaContent(r,\"name\",\"twitter:creator\")||this.getMetaContent(r,\"name\",\"application-name\"),n||\"\"}static getSite(t,e,r){return this.getSchemaProperty(e,\"publisher.name\")||this.getMetaContent(r,\"property\",\"og:site_name\")||this.getSchemaProperty(e,\"WebSite.name\")||this.getSchemaProperty(e,\"sourceOrganization.name\")||this.getMetaContent(r,\"name\",\"copyright\")||this.getSchemaProperty(e,\"copyrightHolder.name\")||this.getSchemaProperty(e,\"isPartOf.name\")||this.getMetaContent(r,\"name\",\"application-name\")||this.getAuthor(t,e,r)||\"\"}static getTitle(t,e,r){var n,o;const i=this.getMetaContent(r,\"property\",\"og:title\")||this.getMetaContent(r,\"name\",\"twitter:title\")||this.getSchemaProperty(e,\"headline\")||this.getMetaContent(r,\"name\",\"title\")||this.getMetaContent(r,\"name\",\"sailthru.title\")||(null===(o=null===(n=t.querySelector(\"title\"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||\"\";return this.cleanTitle(i,this.getSite(t,e,r))}static cleanTitle(t,e){if(!t||!e)return t;const r=e.replace(/[.*+?^${}()|[\\]\\\\]/g,\"\\\\$&\"),n=[`\\\\s*[\\\\|\\\\-\\u2013\\u2014]\\\\s*${r}\\\\s*$`,`^\\\\s*${r}\\\\s*[\\\\|\\\\-\\u2013\\u2014]\\\\s*`];for(const e of n){const r=new RegExp(e,\"i\");if(r.test(t)){t=t.replace(r,\"\");break}}return t.trim()}static getDescription(t,e,r){return this.getMetaContent(r,\"name\",\"description\")||this.getMetaContent(r,\"property\",\"description\")||this.getMetaContent(r,\"property\",\"og:description\")||this.getSchemaProperty(e,\"description\")||this.getMetaContent(r,\"name\",\"twitter:description\")||this.getMetaContent(r,\"name\",\"sailthru.description\")||\"\"}static getImage(t,e,r){return this.getMetaContent(r,\"property\",\"og:image\")||this.getMetaContent(r,\"name\",\"twitter:image\")||this.getSchemaProperty(e,\"image.url\")||this.getMetaContent(r,\"name\",\"sailthru.image.full\")||\"\"}static getFavicon(t,e,r){var n,o;const i=this.getMetaContent(r,\"property\",\"og:image:favicon\");if(i)return i;const a=null===(n=t.querySelector(\"link[rel='icon']\"))||void 0===n?void 0:n.getAttribute(\"href\");if(a)return a;const s=null===(o=t.querySelector(\"link[rel='shortcut icon']\"))||void 0===o?void 0:o.getAttribute(\"href\");if(s)return s;if(e)try{return new URL(\"/favicon.ico\",e).href}catch(t){console.warn(\"Failed to construct favicon URL:\",t)}return\"\"}static getPublished(t,e,r){var n,o;return this.getSchemaProperty(e,\"datePublished\")||this.getMetaContent(r,\"name\",\"publishDate\")||this.getMetaContent(r,\"property\",\"article:published_time\")||(null===(o=null===(n=t.querySelector('abbr[itemprop=\"datePublished\"]'))||void 0===n?void 0:n.title)||void 0===o?void 0:o.trim())||this.getTimeElement(t)||this.getMetaContent(r,\"name\",\"sailthru.date\")||\"\"}static getMetaContent(t,e,r){var n,o;const i=t.find((t=>{const n=\"name\"===e?t.name:t.property;return(null==n?void 0:n.toLowerCase())===r.toLowerCase()}));return i&&null!==(o=null===(n=i.content)||void 0===n?void 0:n.trim())&&void 0!==o?o:\"\"}static getTimeElement(t){var e,r,n,o;const i=Array.from(t.querySelectorAll(\"time\"))[0];return i&&null!==(o=null!==(r=null===(e=i.getAttribute(\"datetime\"))||void 0===e?void 0:e.trim())&&void 0!==r?r:null===(n=i.textContent)||void 0===n?void 0:n.trim())&&void 0!==o?o:\"\"}static getSchemaProperty(t,e,r=\"\"){if(!t)return r;const n=(t,e,r,o=!0)=>{if(\"string\"==typeof t)return 0===e.length?[t]:[];if(!t||\"object\"!=typeof t)return[];if(Array.isArray(t)){const i=e[0];if(/^\\\\[\\\\d+\\\\]$/.test(i)){const a=parseInt(i.slice(1,-1));return t[a]?n(t[a],e.slice(1),r,o):[]}return 0===e.length&&t.every((t=>\"string\"==typeof t||\"number\"==typeof t))?t.map(String):t.flatMap((t=>n(t,e,r,o)))}const[i,...a]=e;if(!i)return\"string\"==typeof t?[t]:\"object\"==typeof t&&t.name?[t.name]:[];if(t.hasOwnProperty(i))return n(t[i],a,r?`${r}.${i}`:i,!0);if(!o){const o=[];for(const i in t)if(\"object\"==typeof t[i]){const a=n(t[i],e,r?`${r}.${i}`:i,!1);o.push(...a)}if(o.length>0)return o}return[]};try{let o=n(t,e.split(\".\"),\"\",!0);0===o.length&&(o=n(t,e.split(\".\"),\"\",!1));return o.length>0?o.filter(Boolean).join(\", \"):r}catch(t){return console.error(`Error in getSchemaProperty for ${e}:`,t),r}}}},610:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.standardizeFootnotes=function(t){const e=t.ownerDocument;if(!e)return void console.warn(\"standardizeFootnotes: No document available\");new o(e).standardizeFootnotes(t)};const n=r(640);class o{constructor(t){this.doc=t}createFootnoteItem(t,e,r){const n=\"string\"==typeof e?this.doc:e.ownerDocument,o=n.createElement(\"li\");if(o.className=\"footnote\",o.id=`fn:${t}`,\"string\"==typeof e){const t=n.createElement(\"p\");t.innerHTML=e,o.appendChild(t)}else{const t=Array.from(e.querySelectorAll(\"p\"));if(0===t.length){const t=n.createElement(\"p\");t.innerHTML=e.innerHTML,o.appendChild(t)}else t.forEach((t=>{const e=n.createElement(\"p\");e.innerHTML=t.innerHTML,o.appendChild(e)}))}const i=o.querySelector(\"p:last-of-type\")||o;return r.forEach(((t,e)=>{const o=n.createElement(\"a\");o.href=`#${t}`,o.title=\"return to article\",o.className=\"footnote-backref\",o.innerHTML=\"\\u21a9\",e<r.length-1&&(o.innerHTML+=\" \"),i.appendChild(o)})),o}collectFootnotes(t){const e={};let r=1;const o=new Set;return t.querySelectorAll(n.FOOTNOTE_LIST_SELECTORS).forEach((t=>{if(t.matches('div.footnote[data-component-name=\"FootnoteToDOM\"]')){const n=t.querySelector(\"a.footnote-number\"),i=t.querySelector(\".footnote-content\");if(n&&i){const t=n.id.replace(\"footnote-\",\"\").toLowerCase();t&&!o.has(t)&&(e[r]={content:i,originalId:t,refs:[]},o.add(t),r++)}return}t.querySelectorAll('li, div[role=\"listitem\"]').forEach((t=>{var n,i,a,s;let l=\"\",c=null;const u=t.querySelector(\".citations\");if(null===(n=null==u?void 0:u.id)||void 0===n?void 0:n.toLowerCase().startsWith(\"r\")){l=u.id.toLowerCase();const t=u.querySelector(\".citation-content\");t&&(c=t)}else{if(t.id.toLowerCase().startsWith(\"bib.bib\"))l=t.id.replace(\"bib.bib\",\"\").toLowerCase();else if(t.id.toLowerCase().startsWith(\"fn:\"))l=t.id.replace(\"fn:\",\"\").toLowerCase();else if(t.id.toLowerCase().startsWith(\"fn\"))l=t.id.replace(\"fn\",\"\").toLowerCase();else if(t.hasAttribute(\"data-counter\"))l=(null===(a=null===(i=t.getAttribute(\"data-counter\"))||void 0===i?void 0:i.replace(/\\.$/,\"\"))||void 0===a?void 0:a.toLowerCase())||\"\";else{const e=null===(s=t.id.split(\"/\").pop())||void 0===s?void 0:s.match(/cite_note-(.+)/);l=e?e[1].toLowerCase():t.id.toLowerCase()}c=t}l&&!o.has(l)&&(e[r]={content:c||t,originalId:l,refs:[]},o.add(l),r++)}))})),e}findOuterFootnoteContainer(t){let e=t,r=t.parentElement;for(;r&&(\"span\"===r.tagName.toLowerCase()||\"sup\"===r.tagName.toLowerCase());)e=r,r=r.parentElement;return e}createFootnoteReference(t,e){const r=this.doc.createElement(\"sup\");r.id=e;const n=this.doc.createElement(\"a\");return n.href=`#fn:${t}`,n.textContent=t,r.appendChild(n),r}standardizeFootnotes(t){const e=this.collectFootnotes(t),r=t.querySelectorAll(n.FOOTNOTE_INLINE_REFERENCES),o=new Map;r.forEach((t=>{var r,n,i,a;if(!t)return;let s=\"\",l=\"\";if(t.matches('a[id^=\"ref-link\"]'))s=(null===(r=t.textContent)||void 0===r?void 0:r.trim())||\"\";else if(t.matches('a[role=\"doc-biblioref\"]')){const e=t.getAttribute(\"data-xml-rid\");if(e)s=e;else{const e=t.getAttribute(\"href\");(null==e?void 0:e.startsWith(\"#core-R\"))&&(s=e.replace(\"#core-\",\"\"))}}else if(t.matches(\"a.footnote-anchor, span.footnote-hovercard-target a\")){const e=(null===(n=t.id)||void 0===n?void 0:n.replace(\"footnote-anchor-\",\"\"))||\"\";e&&(s=e.toLowerCase())}else if(t.matches(\"cite.ltx_cite\")){const e=t.querySelector(\"a\");if(e){const t=e.getAttribute(\"href\");if(t){const e=null===(i=t.split(\"/\").pop())||void 0===i?void 0:i.match(/bib\\.bib(\\d+)/);e&&(s=e[1].toLowerCase())}}}else if(t.matches(\"sup.reference\")){const e=t.querySelectorAll(\"a\");Array.from(e).forEach((t=>{var e;const r=t.getAttribute(\"href\");if(r){const t=null===(e=r.split(\"/\").pop())||void 0===e?void 0:e.match(/(?:cite_note|cite_ref)-(.+)/);t&&(s=t[1].toLowerCase())}}))}else if(t.matches('sup[id^=\"fnref:\"]'))s=t.id.replace(\"fnref:\",\"\").toLowerCase();else if(t.matches('sup[id^=\"fnr\"]'))s=t.id.replace(\"fnr\",\"\").toLowerCase();else if(t.matches(\"span.footnote-reference\"))s=t.getAttribute(\"data-footnote-id\")||\"\";else if(t.matches(\"span.footnote-link\"))s=t.getAttribute(\"data-footnote-id\")||\"\",l=t.getAttribute(\"data-footnote-content\")||\"\";else if(t.matches(\"a.citation\"))s=(null===(a=t.textContent)||void 0===a?void 0:a.trim())||\"\",l=t.getAttribute(\"href\")||\"\";else if(t.matches('a[id^=\"fnref\"]'))s=t.id.replace(\"fnref\",\"\").toLowerCase();else{const e=t.getAttribute(\"href\");if(e){const t=e.replace(/^[#]/,\"\");s=t.toLowerCase()}}if(s){const r=Object.entries(e).find((([t,e])=>e.originalId===s.toLowerCase()));if(r){const[e,n]=r,i=n.refs.length>0?`fnref:${e}-${n.refs.length+1}`:`fnref:${e}`;n.refs.push(i);const a=this.findOuterFootnoteContainer(t);if(\"sup\"===a.tagName.toLowerCase()){o.has(a)||o.set(a,[]);o.get(a).push(this.createFootnoteReference(e,i))}else a.replaceWith(this.createFootnoteReference(e,i))}}})),o.forEach(((t,e)=>{if(t.length>0){const r=this.doc.createDocumentFragment();t.forEach((t=>{const e=t.querySelector(\"a\");if(e){const n=this.doc.createElement(\"sup\");n.id=t.id,n.appendChild(e.cloneNode(!0)),r.appendChild(n)}})),e.replaceWith(r)}}));const i=this.doc.createElement(\"div\");i.id=\"footnotes\";const a=this.doc.createElement(\"ol\");Object.entries(e).forEach((([t,e])=>{const r=this.createFootnoteItem(parseInt(t),e.content,e.refs);a.appendChild(r)}));t.querySelectorAll(n.FOOTNOTE_LIST_SELECTORS).forEach((t=>t.remove())),a.children.length>0&&(i.appendChild(a),t.appendChild(i))}}},628:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.Defuddle=void 0;const n=r(608),o=r(917),i=r(640),a=r(840),s=r(968),l=r(552);e.Defuddle=class{constructor(t,e={}){this.doc=t,this.options=e,this.debug=e.debug||!1}parse(){const t=this.parseInternal();if(t.wordCount<200){console.log(\"Initial parse returned very little content, trying again\");const e=this.parseInternal({removePartialSelectors:!1});if(e.wordCount>t.wordCount)return this._log(\"Retry produced more content\"),e}return t}parseInternal(t={}){var e,r,i;const l=Date.now(),c=Object.assign(Object.assign({removeExactSelectors:!0,removePartialSelectors:!0},this.options),t),u=this._extractSchemaOrgData(this.doc),d=[];this.doc.querySelectorAll(\"meta\").forEach((t=>{const e=t.getAttribute(\"name\"),r=t.getAttribute(\"property\");let n=t.getAttribute(\"content\");n&&d.push({name:e,property:r,content:this._decodeHTMLEntities(n)})}));const m=n.MetadataExtractor.extract(this.doc,u,d);try{const t=c.url||this.doc.URL,n=o.ExtractorRegistry.findExtractor(this.doc,t,u);if(n&&n.canExtract()){const t=n.extract(),o=Date.now();return{content:t.contentHtml,title:(null===(e=t.variables)||void 0===e?void 0:e.title)||m.title,description:m.description,domain:m.domain,favicon:m.favicon,image:m.image,published:(null===(r=t.variables)||void 0===r?void 0:r.published)||m.published,author:(null===(i=t.variables)||void 0===i?void 0:i.author)||m.author,site:m.site,schemaOrgData:m.schemaOrgData,wordCount:this.countWords(t.contentHtml),parseTime:Math.round(o-l),extractorType:n.constructor.name.replace(\"Extractor\",\"\").toLowerCase(),metaTags:d}}const h=this._evaluateMediaQueries(this.doc),p=this.findSmallImages(this.doc),g=this.doc.cloneNode(!0);this.applyMobileStyles(g,h);const f=this.findMainContent(g);if(!f){const t=Date.now();return Object.assign(Object.assign({content:this.doc.body.innerHTML},m),{wordCount:this.countWords(this.doc.body.innerHTML),parseTime:Math.round(t-l),metaTags:d})}this.removeSmallImages(g,p),this.removeHiddenElements(g),s.ContentScorer.scoreAndRemove(g,this.debug),(c.removeExactSelectors||c.removePartialSelectors)&&this.removeBySelector(g,c.removeExactSelectors,c.removePartialSelectors),(0,a.standardizeContent)(f,m,this.doc,this.debug);const v=f.outerHTML,b=Date.now();return Object.assign(Object.assign({content:v},m),{wordCount:this.countWords(v),parseTime:Math.round(b-l),metaTags:d})}catch(t){console.error(\"Defuddle\",\"Error processing document:\",t);const e=Date.now();return Object.assign(Object.assign({content:this.doc.body.innerHTML},m),{wordCount:this.countWords(this.doc.body.innerHTML),parseTime:Math.round(e-l),metaTags:d})}}countWords(t){const e=this.doc.createElement(\"div\");e.innerHTML=t;return(e.textContent||\"\").trim().replace(/\\s+/g,\" \").split(\" \").filter((t=>t.length>0)).length}_log(...t){this.debug&&console.log(\"Defuddle:\",...t)}_evaluateMediaQueries(t){const e=[],r=/max-width[^:]*:\\s*(\\d+)/;try{const n=Array.from(t.styleSheets).filter((t=>{try{return t.cssRules,!0}catch(t){return t instanceof DOMException&&t.name,!1}}));n.flatMap((t=>{try{return\"undefined\"==typeof CSSMediaRule?[]:Array.from(t.cssRules).filter((t=>t instanceof CSSMediaRule&&t.conditionText.includes(\"max-width\")))}catch(t){return this.debug&&console.warn(\"Defuddle: Failed to process stylesheet:\",t),[]}})).forEach((t=>{const n=t.conditionText.match(r);if(n){const r=parseInt(n[1]);if(i.MOBILE_WIDTH<=r){Array.from(t.cssRules).filter((t=>t instanceof CSSStyleRule)).forEach((t=>{try{e.push({selector:t.selectorText,styles:t.style.cssText})}catch(t){this.debug&&console.warn(\"Defuddle: Failed to process CSS rule:\",t)}}))}}}))}catch(t){console.error(\"Defuddle: Error evaluating media queries:\",t)}return e}applyMobileStyles(t,e){e.forEach((({selector:e,styles:r})=>{try{t.querySelectorAll(e).forEach((t=>{t.setAttribute(\"style\",(t.getAttribute(\"style\")||\"\")+r)}))}catch(t){console.error(\"Defuddle\",\"Error applying styles for selector:\",e,t)}}))}removeHiddenElements(t){let e=0;const r=new Set,n=Array.from(t.getElementsByTagName(\"*\"));for(let o=0;o<n.length;o+=100){const i=n.slice(o,o+100),a=i.map((e=>{var r,n;try{return null===(r=e.ownerDocument.defaultView)||void 0===r?void 0:r.getComputedStyle(e)}catch(r){const o=e.getAttribute(\"style\");if(!o)return null;const i=t.createElement(\"style\");i.textContent=`* { ${o} }`,t.head.appendChild(i);const a=null===(n=e.ownerDocument.defaultView)||void 0===n?void 0:n.getComputedStyle(e);return t.head.removeChild(i),a}}));i.forEach(((t,n)=>{const o=a[n];!o||\"none\"!==o.display&&\"hidden\"!==o.visibility&&\"0\"!==o.opacity||(r.add(t),e++)}))}this._log(\"Removed hidden elements:\",e)}removeBySelector(t,e=!0,r=!0){const n=Date.now();let o=0,a=0;const s=new Set;if(e){t.querySelectorAll(i.EXACT_SELECTORS.join(\",\")).forEach((t=>{(null==t?void 0:t.parentNode)&&(s.add(t),o++)}))}if(r){const e=i.PARTIAL_SELECTORS.join(\"|\"),r=new RegExp(e,\"i\"),n=i.TEST_ATTRIBUTES.map((t=>`[${t}]`)).join(\",\");t.querySelectorAll(n).forEach((t=>{if(s.has(t))return;const e=i.TEST_ATTRIBUTES.map((e=>\"class\"===e?t.className&&\"string\"==typeof t.className?t.className:\"\":\"id\"===e?t.id||\"\":t.getAttribute(e)||\"\")).join(\" \").toLowerCase();e.trim()&&r.test(e)&&(s.add(t),a++)}))}s.forEach((t=>t.remove()));const l=Date.now();this._log(\"Removed clutter elements:\",{exactSelectors:o,partialSelectors:a,total:s.size,processingTime:`${(l-n).toFixed(2)}ms`})}findSmallImages(t){const e=new Set,r=/scale\\(([\\d.]+)\\)/,n=Date.now();let o=0;const i=[...Array.from(t.getElementsByTagName(\"img\")),...Array.from(t.getElementsByTagName(\"svg\"))];if(0===i.length)return e;const a=i.map((t=>({element:t,naturalWidth:\"img\"===t.tagName.toLowerCase()&&parseInt(t.getAttribute(\"width\")||\"0\")||0,naturalHeight:\"img\"===t.tagName.toLowerCase()&&parseInt(t.getAttribute(\"height\")||\"0\")||0,attrWidth:parseInt(t.getAttribute(\"width\")||\"0\"),attrHeight:parseInt(t.getAttribute(\"height\")||\"0\")})));for(let t=0;t<a.length;t+=50){const n=a.slice(t,t+50);try{const t=n.map((({element:t})=>{var e;try{return null===(e=t.ownerDocument.defaultView)||void 0===e?void 0:e.getComputedStyle(t)}catch(t){return null}})),i=n.map((({element:t})=>{try{return t.getBoundingClientRect()}catch(t){return null}}));n.forEach(((n,a)=>{var s;try{const l=t[a],c=i[a];if(!l)return;const u=l.transform,d=u?parseFloat((null===(s=u.match(r))||void 0===s?void 0:s[1])||\"1\"):1,m=[n.naturalWidth,n.attrWidth,parseInt(l.width)||0,c?c.width*d:0].filter((t=>\"number\"==typeof t&&t>0)),h=[n.naturalHeight,n.attrHeight,parseInt(l.height)||0,c?c.height*d:0].filter((t=>\"number\"==typeof t&&t>0));if(m.length>0&&h.length>0){const t=Math.min(...m),r=Math.min(...h);if(t<33||r<33){const t=this.getElementIdentifier(n.element);t&&(e.add(t),o++)}}}catch(t){this.debug&&console.warn(\"Defuddle: Failed to process element dimensions:\",t)}}))}catch(t){this.debug&&console.warn(\"Defuddle: Failed to process batch:\",t)}}const s=Date.now();return this._log(\"Found small elements:\",{count:o,processingTime:`${(s-n).toFixed(2)}ms`}),e}removeSmallImages(t,e){let r=0;[\"img\",\"svg\"].forEach((n=>{const o=t.getElementsByTagName(n);Array.from(o).forEach((t=>{const n=this.getElementIdentifier(t);n&&e.has(n)&&(t.remove(),r++)}))})),this._log(\"Removed small elements:\",r)}getElementIdentifier(t){if(\"img\"===t.tagName.toLowerCase()){const e=t.getAttribute(\"data-src\");if(e)return`src:${e}`;const r=t.getAttribute(\"src\")||\"\",n=t.getAttribute(\"srcset\")||\"\",o=t.getAttribute(\"data-srcset\");if(r)return`src:${r}`;if(n)return`srcset:${n}`;if(o)return`srcset:${o}`}const e=t.id||\"\",r=t.className||\"\",n=\"svg\"===t.tagName.toLowerCase()&&t.getAttribute(\"viewBox\")||\"\";return e?`id:${e}`:n?`viewBox:${n}`:r?`class:${r}`:null}findMainContent(t){const e=[];if(i.ENTRY_POINT_ELEMENTS.forEach(((r,n)=>{t.querySelectorAll(r).forEach((t=>{let r=40*(i.ENTRY_POINT_ELEMENTS.length-n);r+=s.ContentScorer.scoreElement(t),e.push({element:t,score:r})}))})),0===e.length)return this.findContentByScoring(t);if(e.sort(((t,e)=>e.score-t.score)),this.debug&&this._log(\"Content candidates:\",e.map((t=>({element:t.element.tagName,selector:this.getElementSelector(t.element),score:t.score})))),1===e.length&&\"body\"===e[0].element.tagName.toLowerCase()){const e=this.findTableBasedContent(t);if(e)return e}return e[0].element}findTableBasedContent(t){if(!Array.from(t.getElementsByTagName(\"table\")).some((t=>{const e=parseInt(t.getAttribute(\"width\")||\"0\"),r=this.getComputedStyle(t);return e>400||(null==r?void 0:r.width.includes(\"px\"))&&parseInt(r.width)>400||\"center\"===t.getAttribute(\"align\")||t.className.toLowerCase().includes(\"content\")||t.className.toLowerCase().includes(\"article\")})))return null;const e=Array.from(t.getElementsByTagName(\"td\"));return s.ContentScorer.findBestElement(e)}findContentByScoring(t){const e=[];return i.BLOCK_ELEMENTS.forEach((r=>{Array.from(t.getElementsByTagName(r)).forEach((t=>{const r=s.ContentScorer.scoreElement(t);r>0&&e.push({score:r,element:t})}))})),e.length>0?e.sort(((t,e)=>e.score-t.score))[0].element:null}getElementSelector(t){const e=[];let r=t;for(;r&&r!==this.doc.documentElement;){let t=r.tagName.toLowerCase();r.id?t+=\"#\"+r.id:r.className&&\"string\"==typeof r.className&&(t+=\".\"+r.className.trim().split(/\\s+/).join(\".\")),e.unshift(t),r=r.parentElement}return e.join(\" > \")}getComputedStyle(t){return(0,l.getComputedStyle)(t)}_extractSchemaOrgData(t){const e=t.querySelectorAll('script[type=\"application/ld+json\"]'),r=[];e.forEach((t=>{let e=t.textContent||\"\";try{e=e.replace(/\\/\\*[\\s\\S]*?\\*\\/|^\\s*\\/\\/.*$/gm,\"\").replace(/^\\s*<!\\[CDATA\\[([\\s\\S]*?)\\]\\]>\\s*$/,\"$1\").replace(/^\\s*(\\*\\/|\\/\\*)\\s*|\\s*(\\*\\/|\\/\\*)\\s*$/g,\"\").trim();const t=JSON.parse(e);t[\"@graph\"]&&Array.isArray(t[\"@graph\"])?r.push(...t[\"@graph\"]):r.push(t)}catch(t){console.error(\"Defuddle: Error parsing schema.org data:\",t),this.debug&&console.error(\"Defuddle: Problematic JSON content:\",e)}}));const n=t=>{if(\"string\"==typeof t)return this._decodeHTMLEntities(t);if(Array.isArray(t))return t.map(n);if(\"object\"==typeof t&&null!==t){const e={};for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=n(t[r]));return e}return t};return r.map(n)}_decodeHTMLEntities(t){const e=this.doc.createElement(\"textarea\");return e.innerHTML=t,e.value}}},632:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.ChatGPTExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.articles=t.querySelectorAll('article[data-testid^=\"conversation-turn-\"]'),this.footnotes=[],this.footnoteCounter=0}canExtract(){return!!this.articles&&this.articles.length>0}extractMessages(){const t=[];return this.footnotes=[],this.footnoteCounter=0,this.articles?(this.articles.forEach((e=>{var r,n;const o=e.querySelector(\"h5.sr-only, h6.sr-only\"),i=(null===(n=null===(r=null==o?void 0:o.textContent)||void 0===r?void 0:r.trim())||void 0===n?void 0:n.replace(/:\\s*$/,\"\"))||\"\";let a=\"\";const s=e.getAttribute(\"data-message-author-role\");s&&(a=s);let l=e.innerHTML||\"\";l=l.replace(/\\u200B/g,\"\");const c=document.createElement(\"div\");c.innerHTML=l,c.querySelectorAll('h5.sr-only, h6.sr-only, span[data-state=\"closed\"]').forEach((t=>t.remove())),l=c.innerHTML;l=l.replace(/(&ZeroWidthSpace;)?(<span[^>]*?>\\s*<a(?=[^>]*?href=\"([^\"]+)\")(?=[^>]*?target=\"_blank\")(?=[^>]*?rel=\"noopener\")[^>]*?>[\\s\\S]*?<\\/a>\\s*<\\/span>)/gi,((t,e,r,n)=>{let o=\"\",i=\"\";try{o=new URL(n).hostname.replace(/^www\\./,\"\");const t=n.split(\"#:~:text=\");if(t.length>1){i=decodeURIComponent(t[1]),i=i.replace(/%2C/g,\",\");const e=i.split(\",\");i=e.length>1&&e[0].trim()?` \\u2014 ${e[0].trim()}...`:e[0].trim()?` \\u2014 ${i.trim()}`:\"\"}}catch(t){console.error(`Failed to parse URL: ${n}`,t),o=n}let a,s=this.footnotes.findIndex((t=>t.url===n));return-1===s?(this.footnoteCounter++,a=this.footnoteCounter,this.footnotes.push({url:n,text:`<a href=\"${n}\">${o}</a>${i}`})):a=s+1,`<sup id=\"fnref:${a}\"><a href=\"#fn:${a}\">${a}</a></sup>`})),l=l.replace(/<p[^>]*>\\s*<\\/p>/g,\"\"),t.push({author:i,content:l.trim(),metadata:{role:a||\"unknown\"}})})),t):t}getFootnotes(){return this.footnotes}getMetadata(){const t=this.getTitle(),e=this.extractMessages();return{title:t,site:\"ChatGPT\",url:this.url,messageCount:e.length,description:`ChatGPT conversation with ${e.length} messages`}}getTitle(){var t,e,r;const n=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(n&&\"ChatGPT\"!==n)return n;const o=null===(r=null===(e=this.articles)||void 0===e?void 0:e.item(0))||void 0===r?void 0:r.querySelector(\".text-message\");if(o){const t=o.textContent||\"\";return t.length>50?t.slice(0,50)+\"...\":t}return\"ChatGPT Conversation\"}}e.ChatGPTExtractor=o},640:(t,e)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.ALLOWED_ATTRIBUTES_DEBUG=e.ALLOWED_ATTRIBUTES=e.ALLOWED_EMPTY_ELEMENTS=e.FOOTNOTE_LIST_SELECTORS=e.FOOTNOTE_INLINE_REFERENCES=e.PARTIAL_SELECTORS=e.TEST_ATTRIBUTES=e.EXACT_SELECTORS=e.INLINE_ELEMENTS=e.PRESERVE_ELEMENTS=e.BLOCK_ELEMENTS=e.MOBILE_WIDTH=e.ENTRY_POINT_ELEMENTS=void 0,e.ENTRY_POINT_ELEMENTS=[\"#post\",\".post-content\",\".article-content\",\"#article-content\",\".article_post\",\".article-wrapper\",\".entry-content\",\".content-article\",\".post\",\".markdown-body\",\"article\",'[role=\"article\"]',\"main\",'[role=\"main\"]',\"body\"],e.MOBILE_WIDTH=600,e.BLOCK_ELEMENTS=[\"div\",\"section\",\"article\",\"main\",\"aside\",\"header\",\"footer\",\"nav\",\"content\"],e.PRESERVE_ELEMENTS=new Set([\"pre\",\"code\",\"table\",\"thead\",\"tbody\",\"tr\",\"td\",\"th\",\"ul\",\"ol\",\"li\",\"dl\",\"dt\",\"dd\",\"figure\",\"figcaption\",\"picture\",\"details\",\"summary\",\"blockquote\",\"form\",\"fieldset\"]),e.INLINE_ELEMENTS=new Set([\"a\",\"span\",\"strong\",\"em\",\"i\",\"b\",\"u\",\"code\",\"br\",\"small\",\"sub\",\"sup\",\"mark\",\"date\",\"del\",\"ins\",\"q\",\"abbr\",\"cite\",\"relative-time\",\"time\",\"font\"]),e.EXACT_SELECTORS=[\"noscript\",'script:not([type^=\"math/\"])',\"style\",\"meta\",\"link\",'.ad:not([class*=\"gradient\"])','[class^=\"ad-\" i]','[class$=\"-ad\" i]','[id^=\"ad-\" i]','[id$=\"-ad\" i]','[role=\"banner\" i]','[alt*=\"advert\" i]',\".promo\",\".Promo\",\"#barrier-page\",\".alert\",'[id=\"comments\" i]','[id=\"comment\" i]',\"header\",\".header:not(.banner)\",\"#header\",\"#Header\",\"#banner\",\"#Banner\",\"nav\",\".navigation\",\"#navigation\",\".hero\",'[role=\"navigation\" i]','[role=\"dialog\" i]','[role*=\"complementary\" i]','[class*=\"pagination\" i]',\".menu\",\"#menu\",\"#siteSub\",\".previous\",\".author\",\".Author\",'[class$=\"_bio\"]',\"#categories\",\".contributor\",\".date\",\"#date\",\"[data-date]\",\".entry-meta\",\".meta\",\".tags\",\"#tags\",\".toc\",\".Toc\",\"#toc\",\".headline\",\"#headline\",\"#title\",\"#Title\",\"#articleTag\",'[href*=\"/category\"]','[href*=\"/categories\"]','[href*=\"/tag/\"]','[href*=\"/tags/\"]','[href*=\"/topics\"]','[href*=\"author\"]','[href*=\"#toc\"]','[href=\"#top\"]','[href=\"#Top\"]','[href=\"#page-header\"]','[href=\"#content\"]','[href=\"#site-content\"]','[href=\"#main-content\"]','[href^=\"#main\"]','[src*=\"author\"]',\"footer\",\".aside\",\"aside\",\"button\",\"canvas\",\"date\",\"dialog\",\"fieldset\",\"form\",'input:not([type=\"checkbox\"])',\"label\",\"option\",\"select\",\"textarea\",\"time\",\"relative-time\",\"[hidden]\",'[aria-hidden=\"true\"]:not([class*=\"math\"])','[style*=\"display: none\"]:not([class*=\"math\"])','[style*=\"display:none\"]:not([class*=\"math\"])','[style*=\"visibility: hidden\"]','[style*=\"visibility:hidden\"]',\".hidden\",\".invisible\",\"instaread-player\",'iframe:not([src*=\"youtube\"]):not([src*=\"youtu.be\"]):not([src*=\"vimeo\"]):not([src*=\"twitter\"]):not([src*=\"x.com\"]):not([src*=\"datawrapper\"])','[class=\"logo\" i]',\"#logo\",\"#Logo\",\"#newsletter\",\"#Newsletter\",\".subscribe\",\".noprint\",'[data-print-layout=\"hide\" i]','[data-block=\"donotprint\" i]','[class*=\"clickable-icon\" i]','li span[class*=\"ltx_tag\" i][class*=\"ltx_tag_item\" i]','a[href^=\"#\"][class*=\"anchor\" i]','a[href^=\"#\"][class*=\"ref\" i]','[data-container*=\"most-viewed\" i]',\".sidebar\",\".Sidebar\",\"#sidebar\",\"#Sidebar\",\"#sitesub\",'[data-link-name*=\"skip\" i]','[aria-label*=\"skip\" i]',\"#skip-link\",\".copyright\",\"#copyright\",\"#rss\",\"#feed\",\".gutter\",\"#primaryaudio\",\"#NYT_ABOVE_MAIN_CONTENT_REGION\",'[data-testid=\"photoviewer-children-figure\"] > span',\"table.infobox\",\".pencraft:not(.pc-display-contents)\",'[data-optimizely=\"related-articles-section\" i]','[data-orientation=\"vertical\"]'],e.TEST_ATTRIBUTES=[\"class\",\"id\",\"data-test\",\"data-testid\",\"data-test-id\",\"data-qa\",\"data-cy\"],e.PARTIAL_SELECTORS=[\"a-statement\",\"access-wall\",\"activitypub\",\"actioncall\",\"addcomment\",\"advert\",\"adlayout\",\"ad-tldr\",\"ad-placement\",\"ads-container\",\"_ad_\",\"after_content\",\"after_main_article\",\"afterpost\",\"allterms\",\"-alert-\",\"alert-box\",\"appendix\",\"_archive\",\"around-the-web\",\"aroundpages\",\"article-author\",\"article-badges\",\"article-banner\",\"article-bottom-section\",\"article-bottom\",\"article-category\",\"article-card\",\"article-citation\",\"article__copy\",\"article_date\",\"article-date\",\"article-end \",\"article_header\",\"article-header\",\"article__header\",\"article__hero\",\"article__info\",\"article-info\",\"article-meta\",\"article_meta\",\"article__meta\",\"articlename\",\"article-subject\",\"article_subject\",\"article-snippet\",\"article-separator\",\"article--share\",\"article--topics\",\"articletags\",\"article-tags\",\"article_tags\",\"articletitle\",\"article-title\",\"article_title\",\"articletopics\",\"article-topics\",\"article--lede\",\"articlewell\",\"associated-people\",\"audio-card\",\"author-bio\",\"author-box\",\"author-info\",\"author_info\",\"authorm\",\"author-mini-bio\",\"author-name\",\"author-publish-info\",\"authored-by\",\"avatar\",\"back-to-top\",\"backlink_container\",\"backlinks-section\",\"bio-block\",\"biobox\",\"blog-pager\",\"bookmark-\",\"-bookmark\",\"bottominfo\",\"bottomnav\",\"bottom-of-article\",\"bottom-wrapper\",\"brand-bar\",\"breadcrumb\",\"brdcrumb\",\"button-wrapper\",\"buttons-container\",\"btn-\",\"-btn\",\"byline\",\"captcha\",\"card-text\",\"card-media\",\"card-post\",\"carouselcontainer\",\"carousel-container\",\"cat_header\",\"catlinks\",\"_categories\",\"card-author\",\"card-content\",\"chapter-list\",\"collections\",\"comments\",\"commentbox\",\"comment-button\",\"commentcomp\",\"comment-content\",\"comment-count\",\"comment-form\",\"comment-number\",\"comment-respond\",\"comment-thread\",\"comment-wrap\",\"complementary\",\"consent\",\"contact-\",\"content-card\",\"content-topics\",\"contentpromo\",\"context-bar\",\"context-widget\",\"core-collateral\",\"cover-\",\"created-date\",\"creative-commons_\",\"c-subscribe\",\"_cta\",\"-cta\",\"cta-\",\"cta_\",\"current-issue\",\"custom-list-number\",\"dateline\",\"dateheader\",\"date-header\",\"date-pub\",\"disclaimer\",\"disclosure\",\"discussion\",\"discuss_\",\"disqus\",\"donate\",\"donation\",\"dropdown\",\"eletters\",\"emailsignup\",\"engagement-widget\",\"enhancement\",\"entry-author-info\",\"entry-categories\",\"entry-date\",\"entry-title\",\"entry-utility\",\"-error\",\"error-\",\"eyebrow\",\"expand-reduce\",\"external-anchor\",\"externallinkembedwrapper\",\"extra-services\",\"extra-title\",\"facebook\",\"fancy-box\",\"favorite\",\"featured-content\",\"feature_feed\",\"feedback\",\"feed-links\",\"field-site-sections\",\"fixheader\",\"floating-vid\",\"follower\",\"footer\",\"footnote-back\",\"footnoteback\",\"form-group\",\"for-you\",\"frontmatter\",\"further-reading\",\"fullbleedheader\",\"gated-\",\"gh-feed\",\"gist-meta\",\"goog-\",\"graph-view\",\"hamburger\",\"header_logo\",\"header-logo\",\"header-pattern\",\"hero-list\",\"hide-for-print\",\"hide-print\",\"hide-when-no-script\",\"hidden-print\",\"hidden-sidenote\",\"hidden-accessibility\",\"infoline\",\"instacartIntegration\",\"interlude\",\"interaction\",\"itemendrow\",\"invisible\",\"jumplink\",\"jump-to-\",\"keepreading\",\"keep-reading\",\"keep_reading\",\"keyword_wrap\",\"kicker\",\"labstab\",\"-labels\",\"language-name\",\"lastupdated\",\"latest-content\",\"-ledes-\",\"-license\",\"license-\",\"lightbox-popup\",\"like-button\",\"link-box\",\"links-grid\",\"links-title\",\"listing-dynamic-terms\",\"list-tags\",\"listinks\",\"loading\",\"loa-info\",\"logo_container\",\"ltx_role_refnum\",\"ltx_tag_bibitem\",\"ltx_error\",\"masthead\",\"marketing\",\"media-inquiry\",\"-menu\",\"menu-\",\"metadata\",\"might-like\",\"minibio\",\"more-about\",\"_modal\",\"-modal\",\"more-\",\"morenews\",\"morestories\",\"more_wrapper\",\"most-read\",\"move-helper\",\"mw-editsection\",\"mw-cite-backlink\",\"mw-indicators\",\"mw-jump-link\",\"nav-\",\"nav_\",\"navigation-post\",\"next-\",\"newsgallery\",\"news-story-title\",\"newsletter_\",\"newsletterbanner\",\"newslettercontainer\",\"newsletter-form\",\"newsletter-signup\",\"newslettersignup\",\"newsletterwidget\",\"newsletterwrapper\",\"not-found\",\"notessection\",\"nomobile\",\"noprint\",\"open-slideshow\",\"originally-published\",\"other-blogs\",\"outline-view\",\"pagehead\",\"page-header\",\"page-title\",\"paywall_message\",\"-partners\",\"permission-\",\"plea\",\"popular\",\"popup_links\",\"pop_stories\",\"pop-up\",\"post-author\",\"post-bottom\",\"post__category\",\"postcomment\",\"postdate\",\"post-date\",\"post_date\",\"post-details\",\"post-feeds\",\"postinfo\",\"post-info\",\"post_info\",\"post-inline-date\",\"post-links\",\"postlist\",\"post_list\",\"post_meta\",\"post-meta\",\"postmeta\",\"post_more\",\"postnavi\",\"post-navigation\",\"postpath\",\"post-preview\",\"postsnippet\",\"post_snippet\",\"post-snippet\",\"post-subject\",\"posttax\",\"post-tax\",\"post_tax\",\"posttag\",\"post_tag\",\"post-tag\",\"post_time\",\"posttitle\",\"post-title\",\"post_title\",\"post__title\",\"post-ufi-button\",\"prev-post\",\"prevnext\",\"prev_next\",\"prev-next\",\"previousnext\",\"press-inquiries\",\"print-none\",\"print-header\",\"print:hidden\",\"privacy-notice\",\"privacy-settings\",\"profile\",\"promo_article\",\"promo-bar\",\"promo-box\",\"pubdate\",\"pub_date\",\"pub-date\",\"publish_date\",\"publish-date\",\"publication-date\",\"publicationName\",\"qr-code\",\"qr_code\",\"quick_up\",\"_rail\",\"ratingssection\",\"read_also\",\"readmore\",\"read-next\",\"read_next\",\"read_time\",\"read-time\",\"reading_time\",\"reading-time\",\"reading-list\",\"recent-\",\"recent-articles\",\"recentpost\",\"recent_post\",\"recent-post\",\"recommend\",\"redirectedfrom\",\"recirc\",\"register\",\"related\",\"relevant\",\"reversefootnote\",\"_rss\",\"rss-link\",\"screen-reader-text\",\"scroll_to\",\"scroll-to\",\"_search\",\"-search\",\"section-nav\",\"series-banner\",\"share-box\",\"sharedaddy\",\"share-icons\",\"sharelinks\",\"share-post\",\"share-print\",\"share-section\",\"show-for-print\",\"sidebartitle\",\"sidebar-content\",\"sidebar-wrapper\",\"sideitems\",\"sidebar-author\",\"sidebar-item\",\"side-box\",\"side-logo\",\"sign-in-gate\",\"similar-\",\"similar_\",\"similars-\",\"site-index\",\"site-header\",\"siteheader\",\"site-logo\",\"site-name\",\"site-wordpress\",\"skip-content\",\"skip-to-content\",\"c-skip-link\",\"_skip-link\",\"-slider\",\"slug-wrap\",\"social-author\",\"social-shar\",\"social-date\",\"speechify-ignore\",\"speedbump\",\"sponsor\",\"springercitation\",\"sr-only\",\"_stats\",\"story-date\",\"story-navigation\",\"storyreadtime\",\"storysmall\",\"storypublishdate\",\"subject-label\",\"subhead\",\"submenu\",\"-subscribe-\",\"subscriber-drive\",\"subscription-\",\"_tags\",\"tags__item\",\"tag_list\",\"taxonomy\",\"table-of-contents\",\"tabs-\",\"terminaltout\",\"time-rubric\",\"timestamp\",\"time-read\",\"time-to-read\",\"tip_off\",\"tiptout\",\"-tout-\",\"toc-container\",\"toggle-caption\",\"tooltip\",\"topbar\",\"topic-list\",\"topic-subnav\",\"top-wrapper\",\"tree-item\",\"trending\",\"trust-feat\",\"trust-badge\",\"trust-project\",\"twitter\",\"u-hide\",\"upsell\",\"viewbottom\",\"visually-hidden\",\"welcomebox\",\"widget_pages\"],e.FOOTNOTE_INLINE_REFERENCES=[\"sup.reference\",\"cite.ltx_cite\",'sup[id^=\"fnr\"]','span[id^=\"fnr\"]','span[class*=\"footnote_ref\"]',\"span.footnote-link\",\"a.citation\",'a[id^=\"ref-link\"]','a[href^=\"#fn\"]','a[href^=\"#cite\"]','a[href^=\"#reference\"]','a[href^=\"#footnote\"]','a[href^=\"#r\"]','a[href^=\"#b\"]','a[href*=\"cite_note\"]','a[href*=\"cite_ref\"]',\"a.footnote-anchor\",\"span.footnote-hovercard-target a\",'a[role=\"doc-biblioref\"]','a[id^=\"fnref\"]','a[id^=\"ref-link\"]'].join(\",\"),e.FOOTNOTE_LIST_SELECTORS=[\"div.footnote ol\",\"div.footnotes ol\",'div[role=\"doc-endnotes\"]','div[role=\"doc-footnotes\"]',\"ol.footnotes-list\",\"ol.footnotes\",\"ol.references\",'ol[class*=\"article-references\"]',\"section.footnotes ol\",'section[role=\"doc-endnotes\"]','section[role=\"doc-footnotes\"]','section[role=\"doc-bibliography\"]',\"ul.footnotes-list\",\"ul.ltx_biblist\",'div.footnote[data-component-name=\"FootnoteToDOM\"]'].join(\",\"),e.ALLOWED_EMPTY_ELEMENTS=new Set([\"area\",\"audio\",\"base\",\"br\",\"circle\",\"col\",\"defs\",\"ellipse\",\"embed\",\"figure\",\"g\",\"hr\",\"iframe\",\"img\",\"input\",\"line\",\"link\",\"mask\",\"meta\",\"object\",\"param\",\"path\",\"pattern\",\"picture\",\"polygon\",\"polyline\",\"rect\",\"source\",\"stop\",\"svg\",\"td\",\"th\",\"track\",\"use\",\"video\",\"wbr\"]),e.ALLOWED_ATTRIBUTES=new Set([\"alt\",\"allow\",\"allowfullscreen\",\"aria-label\",\"checked\",\"colspan\",\"controls\",\"data-latex\",\"data-src\",\"data-srcset\",\"data-lang\",\"dir\",\"display\",\"frameborder\",\"headers\",\"height\",\"href\",\"lang\",\"role\",\"rowspan\",\"src\",\"srcset\",\"title\",\"type\",\"width\",\"accent\",\"accentunder\",\"align\",\"columnalign\",\"columnlines\",\"columnspacing\",\"columnspan\",\"data-mjx-texclass\",\"depth\",\"displaystyle\",\"fence\",\"frame\",\"framespacing\",\"linethickness\",\"lspace\",\"mathsize\",\"mathvariant\",\"maxsize\",\"minsize\",\"movablelimits\",\"notation\",\"rowalign\",\"rowlines\",\"rowspacing\",\"rowspan\",\"rspace\",\"scriptlevel\",\"separator\",\"stretchy\",\"symmetric\",\"voffset\",\"xmlns\"]),e.ALLOWED_ATTRIBUTES_DEBUG=new Set([\"class\",\"id\"])},649:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.imageRules=void 0;const n=r(552),o=/^data:image\\/([^;]+);base64,/,i=/\\.(jpg|jpeg|png|webp)\\s+\\d/,a=/^\\s*\\S+\\.(jpg|jpeg|png|webp)\\S*\\s*$/,s=/\\.(jpg|jpeg|png|webp|gif|avif)(\\?.*)?$/i,l=/\\s(\\d+)w/,c=/dpr=(\\d+(?:\\.\\d+)?)/,u=/^([^\\s]+)/,d=/^[\\w\\-\\.\\/\\\\]+\\.(jpg|jpeg|png|gif|webp|svg)$/i,m=/^\\d{4}-\\d{2}-\\d{2}$/;function h(t,e,r){const o=r.createElement(\"figure\");o.appendChild(t.cloneNode(!0));const i=r.createElement(\"figcaption\"),a=function(t){const e=[],r=new Set,o=t=>{var i;if((0,n.isTextNode)(t)){const n=(null===(i=t.textContent)||void 0===i?void 0:i.trim())||\"\";n&&!r.has(n)&&(e.push(n),r.add(n))}else if((0,n.isElement)(t)){const e=t.childNodes;for(let t=0;t<e.length;t++)o(e[t])}},i=t.childNodes;for(let t=0;t<i.length;t++)o(i[t]);if(e.length>0)return e.join(\" \");return t.innerHTML}(e);return i.innerHTML=a,o.appendChild(i),o}function p(t,e){e.setAttribute(\"srcset\",t);const r=A(t);r&&b(r)&&e.setAttribute(\"src\",r)}function g(t,e,r){for(let n=0;n<t.attributes.length;n++){const o=t.attributes[n];r.includes(o.name)||e.setAttribute(o.name,o.value)}}function f(t){const e=t.match(o);if(!e)return!1;if(\"svg+xml\"===e[1])return!1;const r=e[0].length;return t.length-r<133}function v(t){return t.startsWith(\"data:image/svg+xml\")}function b(t){return!t.startsWith(\"data:\")&&(!(!t||\"\"===t.trim())&&(s.test(t)||t.includes(\"image\")||t.includes(\"img\")||t.includes(\"photo\")))}function y(t){if(E(t))return!0;return t.querySelectorAll(\"img, video, picture, source\").length>0}function E(t){const e=t.tagName.toLowerCase();return\"img\"===e||\"video\"===e||\"picture\"===e||\"source\"===e}function C(t){if(E(t))return t;const e=t.querySelectorAll(\"picture\");if(e.length>0)return e[0];const r=t.querySelectorAll(\"img\"),n=[];for(let t=0;t<r.length;t++){const e=r[t],o=e.getAttribute(\"src\")||\"\",i=e.getAttribute(\"alt\")||\"\";o.includes(\"data:image/svg+xml\")||(f(o)||!i.trim()&&r.length>1||n.push(e))}if(n.length>0)return n[0];const o=t.querySelectorAll(\"video\");if(o.length>0)return o[0];const i=t.querySelectorAll(\"source\");if(i.length>0)return i[0];const a=t.querySelectorAll(\"img, picture, source, video\");return a.length>0?a[0]:null}function w(t){var e,r,n,o;const i=t.querySelector(\"figcaption\");if(i)return i;const a=new Set,s=['[class*=\"caption\"]','[class*=\"description\"]','[class*=\"alt\"]','[class*=\"title\"]','[class*=\"credit\"]','[class*=\"text\"]','[class*=\"post-thumbnail-text\"]','[class*=\"image-caption\"]','[class*=\"photo-caption\"]',\"[aria-label]\",\"[title]\"].join(\", \"),l=t.querySelectorAll(s);for(let t=0;t<l.length;t++){const r=l[t];if(E(r))continue;const n=null===(e=r.textContent)||void 0===e?void 0:e.trim();if(n&&n.length>0&&!a.has(n))return a.add(n),r}const c=t.querySelector(\"img\");if(c&&c.hasAttribute(\"alt\")){const e=c.getAttribute(\"alt\");if(e&&e.trim().length>0){const r=t.ownerDocument.createElement(\"div\");return r.textContent=e,r}}if(t.parentElement){const e=t.parentElement.children;for(let n=0;n<e.length;n++){const o=e[n];if(o===t)continue;if(Array.from(o.classList).some((t=>t.includes(\"caption\")||t.includes(\"credit\")||t.includes(\"text\")||t.includes(\"description\")))){const t=null===(r=o.textContent)||void 0===r?void 0:r.trim();if(t&&t.length>0)return o}}}const u=t.querySelectorAll(\"img\");for(let t=0;t<u.length;t++){const e=u[t];if(!e.parentElement)continue;let r=e.nextElementSibling;for(;r;){if([\"EM\",\"STRONG\",\"SPAN\",\"I\",\"B\",\"SMALL\",\"CITE\"].includes(r.tagName)){const t=null===(n=r.textContent)||void 0===n?void 0:n.trim();if(t&&t.length>0)return r}r=r.nextElementSibling}}for(let t=0;t<u.length;t++){const e=u[t],r=e.parentElement;if(!r)continue;const n=r.querySelectorAll(\"em, strong, span, i, b, small, cite\");for(let t=0;t<n.length;t++){const r=n[t];if(r===e)continue;const i=null===(o=r.textContent)||void 0===o?void 0:o.trim();if(i&&i.length>0)return r}}return null}function x(t){var e;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||\"\";return!(r.length<10||r.startsWith(\"http://\")||r.startsWith(\"https://\"))&&(!d.test(r)&&(!r.match(/^\\d+$/)&&!m.test(r)))}function S(t,e){const r=t.tagName.toLowerCase();if(\"img\"===r)return T(t,e);if(\"picture\"===r){const r=t.querySelector(\"img\");return r?T(r,e):t.cloneNode(!0)}return\"source\"===r?function(t,e){const r=e.createElement(\"img\"),n=t.getAttribute(\"srcset\");n&&p(n,r);const o=t.parentElement;if(o){const t=o.querySelectorAll(\"img\"),e=[];for(let r=0;r<t.length;r++){const n=t[r],o=n.getAttribute(\"src\")||\"\";f(o)||v(o)||\"\"===o||e.push(n)}if(e.length>0){if(g(e[0],r,[\"src\",\"srcset\"]),!r.hasAttribute(\"src\")||!b(r.getAttribute(\"src\")||\"\")){const t=e[0].getAttribute(\"src\");t&&b(t)&&r.setAttribute(\"src\",t)}}else{const t=o.querySelector(\"img[data-src]\");if(t&&(g(t,r,[\"src\",\"srcset\"]),!r.hasAttribute(\"src\")||!b(r.getAttribute(\"src\")||\"\"))){const e=t.getAttribute(\"data-src\");e&&b(e)&&r.setAttribute(\"src\",e)}}}return r}(t,e):t.cloneNode(!0)}function T(t,e){const r=t.getAttribute(\"src\")||\"\";if(f(r)||v(r)){const r=t.parentElement;if(r){const n=r.querySelectorAll(\"source\"),o=[];for(let t=0;t<n.length;t++){const e=n[t];e.hasAttribute(\"data-srcset\")&&\"\"!==e.getAttribute(\"data-srcset\")&&o.push(e)}if(o.length>0){const r=e.createElement(\"img\"),n=t.getAttribute(\"data-src\");return n&&!v(n)&&r.setAttribute(\"src\",n),g(t,r,[\"src\"]),r}}}return t.cloneNode(!0)}function A(t){const e=t.split(\",\");if(0===e.length)return null;const r=e[0].trim().match(u);if(r&&r[1]){const t=r[1];if(v(t)){for(let t=1;t<e.length;t++){const r=e[t].trim().match(u);if(r&&r[1]&&!v(r[1]))return r[1]}return null}return t}return null}function L(t){if(0===t.length)return null;if(1===t.length)return t[0];for(let e=0;e<t.length;e++)if(!t[e].hasAttribute(\"media\"))return t[e];let e=null,r=0;for(let n=0;n<t.length;n++){const o=t[n],i=o.getAttribute(\"srcset\");if(!i)continue;const a=i.match(l),s=i.match(c);if(a&&a[1]){const t=parseInt(a[1],10)*(s?parseFloat(s[1]):1);t>r&&(r=t,e=o)}}return e||t[0]}e.imageRules=[{selector:\"picture\",element:\"picture\",transform:(t,e)=>{const r=t.querySelectorAll(\"source\"),n=t.querySelector(\"img\");if(!n){console.warn(\"Picture element without img fallback:\",t.outerHTML);const n=L(r);if(n){const r=n.getAttribute(\"srcset\");if(r){const n=e.createElement(\"img\");return p(r,n),t.innerHTML=\"\",t.appendChild(n),t}}return t}let o=null,i=null;if(r.length>0){const t=L(r);t&&(o=t.getAttribute(\"srcset\"),o&&(i=A(o)))}if(o&&n.setAttribute(\"srcset\",o),i&&b(i))n.setAttribute(\"src\",i);else if(!n.hasAttribute(\"src\")||!b(n.getAttribute(\"src\")||\"\")){const t=A(n.getAttribute(\"srcset\")||o||\"\");t&&b(t)&&n.setAttribute(\"src\",t)}return r.forEach((t=>t.remove())),t}},{selector:\"uni-image-full-width\",element:\"figure\",transform:(t,e)=>{var r;const n=e.createElement(\"figure\"),o=e.createElement(\"img\"),i=t.querySelector(\"img\");if(!i)return console.warn(\"uni-image-full-width without img:\",t.outerHTML),n;let a=i.getAttribute(\"src\");const s=i.getAttribute(\"data-loading\");if(s)try{const t=JSON.parse(s);t.desktop&&b(t.desktop)&&(a=t.desktop)}catch(t){console.warn(\"Failed to parse data-loading attribute:\",s,t)}if(!a||!b(a))return console.warn(\"Could not find valid src for uni-image-full-width:\",t.outerHTML),n;o.setAttribute(\"src\",a);let l=i.getAttribute(\"alt\");l||(l=t.getAttribute(\"alt-text\")),l&&o.setAttribute(\"alt\",l),n.appendChild(o);const c=t.querySelector(\"figcaption\");if(c){const t=null===(r=c.textContent)||void 0===r?void 0:r.trim();if(t&&t.length>5){const r=e.createElement(\"figcaption\"),o=c.querySelector(\".rich-text p\");o?r.innerHTML=o.innerHTML:r.textContent=t,n.appendChild(r)}}return n}},{selector:'img[data-src], img[data-srcset], img[loading=\"lazy\"], img.lazy, img.lazyload',element:\"img\",transform:(t,e)=>{const r=t.getAttribute(\"src\")||\"\",n=function(t){if(t.hasAttribute(\"data-src\")||t.hasAttribute(\"data-srcset\"))return!0;for(let e=0;e<t.attributes.length;e++){const r=t.attributes[e];if(\"src\"!==r.name){if(r.name.startsWith(\"data-\")&&/\\.(jpg|jpeg|png|webp|gif)(\\?.*)?$/i.test(r.value))return!0;if(/\\.(jpg|jpeg|png|webp|gif)(\\?.*)?$/i.test(r.value))return!0}}return!1}(t);f(r)&&n&&t.removeAttribute(\"src\");const o=t.getAttribute(\"data-src\");o&&!t.getAttribute(\"src\")&&t.setAttribute(\"src\",o);const s=t.getAttribute(\"data-srcset\");s&&!t.getAttribute(\"srcset\")&&t.setAttribute(\"srcset\",s);for(let e=0;e<t.attributes.length;e++){const r=t.attributes[e];\"src\"!==r.name&&\"srcset\"!==r.name&&\"alt\"!==r.name&&(i.test(r.value)?t.setAttribute(\"srcset\",r.value):a.test(r.value)&&t.setAttribute(\"src\",r.value))}return t.classList.remove(\"lazy\",\"lazyload\"),t.removeAttribute(\"data-ll-status\"),t.removeAttribute(\"data-src\"),t.removeAttribute(\"data-srcset\"),t.removeAttribute(\"loading\"),t}},{selector:\"span:has(img)\",element:\"span\",transform:(t,e)=>{try{if(!y(t))return t;const r=C(t);if(!r)return t;const n=w(t),o=S(r,e);if(n&&x(n)){const t=h(o,n,e);return n.parentNode&&n.parentNode.removeChild(n),t}return o}catch(e){return console.warn(\"Error processing span with image:\",e),t}}},{selector:'figure, p:has([class*=\"caption\"])',element:\"figure\",transform:(t,e)=>{try{if(!y(t))return t;const r=C(t);if(!r)return t;const n=w(t);if(n&&x(n)){const o=C(t);let i;return o?i=o:(console.warn(\"Figure rule couldn't find current image element in:\",t.outerHTML),i=S(r,e)),h(i,n,e)}return t}catch(e){return console.warn(\"Error processing complex image element:\",e),t}}}]},732:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.GeminiExtractor=void 0;const n=r(181);class o extends n.ConversationExtractor{constructor(t,e){super(t,e),this.messageCount=null,this.conversationContainers=t.querySelectorAll(\"div.conversation-container\"),this.footnotes=[]}canExtract(){return!!this.conversationContainers&&this.conversationContainers.length>0}extractMessages(){this.messageCount=0;const t=[];return this.conversationContainers?(this.extractSources(),this.conversationContainers.forEach((e=>{const r=e.querySelector(\"user-query\");if(r){const e=r.querySelector(\".query-text\");if(e){const r=e.innerHTML||\"\";t.push({author:\"You\",content:r.trim(),metadata:{role:\"user\"}})}}const n=e.querySelector(\"model-response\");if(n){const e=n.querySelector(\".model-response-text .markdown\"),r=n.querySelector(\"#extended-response-markdown-content\")||e;if(r){let e=r.innerHTML||\"\";const n=document.createElement(\"div\");n.innerHTML=e,n.querySelectorAll(\".table-content\").forEach((t=>{t.classList.remove(\"table-content\")})),e=n.innerHTML,t.push({author:\"Gemini\",content:e.trim(),metadata:{role:\"assistant\"}})}}})),this.messageCount=t.length,t):t}extractSources(){const t=this.document.querySelectorAll(\"browse-item\");t&&t.length>0&&t.forEach((t=>{var e,r,n,o;const i=t.querySelector(\"a\");if(i instanceof HTMLAnchorElement){const t=i.href,a=(null===(r=null===(e=i.querySelector(\".domain\"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim())||\"\",s=(null===(o=null===(n=i.querySelector(\".title\"))||void 0===n?void 0:n.textContent)||void 0===o?void 0:o.trim())||\"\";t&&(a||s)&&this.footnotes.push({url:t,text:s?`${a}: ${s}`:a})}}))}getFootnotes(){return this.footnotes}getMetadata(){var t;const e=this.getTitle(),r=null!==(t=this.messageCount)&&void 0!==t?t:this.extractMessages().length;return{title:e,site:\"Gemini\",url:this.url,messageCount:r,description:`Gemini conversation with ${r} messages`}}getTitle(){var t,e,r,n,o;const i=null===(t=this.document.title)||void 0===t?void 0:t.trim();if(i&&\"Gemini\"!==i&&!i.includes(\"Gemini\"))return i;const a=null===(r=null===(e=this.document.querySelector(\".title-text\"))||void 0===e?void 0:e.textContent)||void 0===r?void 0:r.trim();if(a)return a;const s=null===(o=null===(n=this.conversationContainers)||void 0===n?void 0:n.item(0))||void 0===o?void 0:o.querySelector(\".query-text\");if(s){const t=s.textContent||\"\";return t.length>50?t.slice(0,50)+\"...\":t}return\"Gemini Conversation\"}}e.GeminiExtractor=o},754:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.codeBlockRules=void 0;const n=r(552),o=[/^language-(\\w+)$/,/^lang-(\\w+)$/,/^(\\w+)-code$/,/^code-(\\w+)$/,/^syntax-(\\w+)$/,/^code-snippet__(\\w+)$/,/^highlight-(\\w+)$/,/^(\\w+)-snippet$/,/(?:^|\\s)(?:language|lang|brush|syntax)-(\\w+)(?:\\s|$)/i],i=new Set([\"abap\",\"actionscript\",\"ada\",\"adoc\",\"agda\",\"antlr4\",\"applescript\",\"arduino\",\"armasm\",\"asciidoc\",\"aspnet\",\"atom\",\"bash\",\"batch\",\"c\",\"clojure\",\"cmake\",\"cobol\",\"coffeescript\",\"cpp\",\"c++\",\"crystal\",\"csharp\",\"cs\",\"dart\",\"django\",\"dockerfile\",\"dotnet\",\"elixir\",\"elm\",\"erlang\",\"fortran\",\"fsharp\",\"gdscript\",\"gitignore\",\"glsl\",\"golang\",\"gradle\",\"graphql\",\"groovy\",\"haskell\",\"hs\",\"haxe\",\"hlsl\",\"html\",\"idris\",\"java\",\"javascript\",\"js\",\"jsx\",\"jsdoc\",\"json\",\"jsonp\",\"julia\",\"kotlin\",\"latex\",\"lisp\",\"elisp\",\"livescript\",\"lua\",\"makefile\",\"markdown\",\"md\",\"markup\",\"masm\",\"mathml\",\"matlab\",\"mongodb\",\"mysql\",\"nasm\",\"nginx\",\"nim\",\"nix\",\"objc\",\"ocaml\",\"pascal\",\"perl\",\"php\",\"postgresql\",\"powershell\",\"prolog\",\"puppet\",\"python\",\"regex\",\"rss\",\"ruby\",\"rb\",\"rust\",\"scala\",\"scheme\",\"shell\",\"sh\",\"solidity\",\"sparql\",\"sql\",\"ssml\",\"svg\",\"swift\",\"tcl\",\"terraform\",\"tex\",\"toml\",\"typescript\",\"ts\",\"tsx\",\"unrealscript\",\"verilog\",\"vhdl\",\"webassembly\",\"wasm\",\"xml\",\"yaml\",\"yml\",\"zig\"]);e.codeBlockRules=[{selector:[\"pre\",'div[class*=\"prismjs\"]',\".syntaxhighlighter\",\".highlight\",\".highlight-source\",\".wp-block-syntaxhighlighter-code\",\".wp-block-code\",'div[class*=\"language-\"]'].join(\", \"),element:\"pre\",transform:(t,e)=>{if(!(t=>\"classList\"in t&&\"getAttribute\"in t&&\"querySelector\"in t)(t))return t;const r=t=>{var e;const r=t.getAttribute(\"data-lang\")||t.getAttribute(\"data-language\");if(r)return r.toLowerCase();const n=Array.from(t.classList||[]);if(null===(e=t.classList)||void 0===e?void 0:e.contains(\"syntaxhighlighter\")){const t=n.find((t=>![\"syntaxhighlighter\",\"nogutter\"].includes(t)));if(t&&i.has(t.toLowerCase()))return t.toLowerCase()}for(const t of n)for(const e of o){const r=t.toLowerCase().match(e);if(r&&r[1]&&i.has(r[1].toLowerCase()))return r[1].toLowerCase()}for(const t of n)if(i.has(t.toLowerCase()))return t.toLowerCase();return\"\"};let a=\"\",s=t;for(;s&&!a;){a=r(s);const t=s.querySelector(\"code\");!a&&t&&(a=r(t)),s=s.parentElement}const l=t=>{if((0,n.isTextNode)(t))return t.textContent||\"\";let e=\"\";if((0,n.isElement)(t)){if(\"BR\"===t.tagName)return\"\\n\";if(t.matches('div[class*=\"line\"], span[class*=\"line\"], .ec-line, [data-line-number], [data-line]')){const e=t.querySelector('.code, .content, [class*=\"code-\"], [class*=\"content-\"]');if(e)return(e.textContent||\"\")+\"\\n\";const r=t.querySelector('.line-number, .gutter, [class*=\"line-number\"], [class*=\"gutter\"]');if(r){return Array.from(t.childNodes).filter((t=>!r.contains(t))).map((t=>l(t))).join(\"\")+\"\\n\"}return t.textContent+\"\\n\"}t.childNodes.forEach((t=>{e+=l(t)}))}return e};let c=\"\";t.matches(\".syntaxhighlighter, .wp-block-syntaxhighlighter-code\")&&(c=(t=>{const e=t.querySelector(\".syntaxhighlighter table .code .container\");if(e)return Array.from(e.children).map((t=>{const e=Array.from(t.querySelectorAll(\"code\")).map((t=>{var e;let r=t.textContent||\"\";return(null===(e=t.classList)||void 0===e?void 0:e.contains(\"spaces\"))&&(r=\" \".repeat(r.length)),r})).join(\"\");return e||t.textContent||\"\"})).join(\"\\n\");const r=t.querySelectorAll(\".code .line\");return r.length>0?Array.from(r).map((t=>{const e=Array.from(t.querySelectorAll(\"code\")).map((t=>t.textContent||\"\")).join(\"\");return e||t.textContent||\"\"})).join(\"\\n\"):\"\"})(t)),c||(c=l(t)),c=c.replace(/^\\s+|\\s+$/g,\"\").replace(/\\t/g,\"    \").replace(/\\n{3,}/g,\"\\n\\n\").replace(/\\u00a0/g,\" \").replace(/^\\n+/,\"\").replace(/\\n+$/,\"\");const u=e.createElement(\"pre\"),d=e.createElement(\"code\");return a&&(d.setAttribute(\"data-lang\",a),d.setAttribute(\"class\",`language-${a}`)),d.textContent=c,u.appendChild(d),u}}]},840:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.standardizeContent=function(t,e,r,o=!1){(function(t){const e=t=>{if((0,c.isElement)(t)){const e=t.tagName.toLowerCase();if(\"pre\"===e||\"code\"===e)return}if((0,c.isTextNode)(t)){const e=t.textContent||\"\",r=e.replace(/\\xA0+/g,(e=>{var r,n,o,i;if(1===e.length){const e=null===(n=null===(r=t.previousSibling)||void 0===r?void 0:r.textContent)||void 0===n?void 0:n.slice(-1),a=null===(i=null===(o=t.nextSibling)||void 0===o?void 0:o.textContent)||void 0===i?void 0:i.charAt(0);if((null==e?void 0:e.match(/\\w/))&&(null==a?void 0:a.match(/\\w/)))return\"\\xa0\"}return\" \".repeat(e.length)}));r!==e&&(t.textContent=r)}t.hasChildNodes()&&Array.from(t.childNodes).forEach(e)};e(t)})(t),function(t){let e=0;Array.from(t.getElementsByTagName(\"*\")).forEach((t=>{Array.from(t.childNodes).forEach((t=>{(0,c.isCommentNode)(t)&&(t.remove(),e++)}))})),(0,c.logDebug)(\"Removed HTML comments:\",e)}(t),function(t,e,r){const o=t=>t.replace(/\\u00A0/g,\" \").replace(/\\s+/g,\" \").trim().toLowerCase(),i=t.getElementsByTagName(\"h1\");Array.from(i).forEach((t=>{var e;const o=r.createElement(\"h2\");o.innerHTML=t.innerHTML,Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&o.setAttribute(t.name,t.value)})),null===(e=t.parentNode)||void 0===e||e.replaceChild(o,t)}));const a=t.getElementsByTagName(\"h2\");if(a.length>0){const t=a[0],r=o(t.textContent||\"\"),n=o(e);n&&n===r&&t.remove()}}(t,e.title,r),(0,a.standardizeFootnotes)(t),function(t,e){let r=0;u.forEach((n=>{t.querySelectorAll(n.selector).forEach((t=>{if(n.transform){const o=n.transform(t,e);t.replaceWith(o),r++}}))}));t.querySelectorAll(\"lite-youtube\").forEach((t=>{const n=t.getAttribute(\"videoid\");if(!n)return;const o=e.createElement(\"iframe\");o.width=\"560\",o.height=\"315\",o.src=`https://www.youtube.com/embed/${n}`,o.title=t.getAttribute(\"videotitle\")||\"YouTube video player\",o.frameBorder=\"0\",o.allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\",o.setAttribute(\"allowfullscreen\",\"\"),t.replaceWith(o),r++})),(0,c.logDebug)(\"Converted embedded elements:\",r)}(t,r),o?(m(t,o),d(t),h(t),(0,c.logDebug)(\"Debug mode: Skipping div flattening to preserve structure\")):(p(t,r),m(t,o),function(t){let e=0,r=0,o=!0;for(;o;){r++,o=!1;const i=Array.from(t.getElementsByTagName(\"*\")).filter((t=>{if(n.ALLOWED_EMPTY_ELEMENTS.has(t.tagName.toLowerCase()))return!1;const e=t.textContent||\"\",r=0===e.trim().length,o=e.includes(\"\\xa0\"),i=!t.hasChildNodes()||Array.from(t.childNodes).every((t=>{if((0,c.isTextNode)(t)){const e=t.textContent||\"\";return 0===e.trim().length&&!e.includes(\"\\xa0\")}return!1}));if(\"div\"===t.tagName.toLowerCase()){const e=Array.from(t.children);if(e.length>0&&e.every((t=>{var e;if(\"span\"!==t.tagName.toLowerCase())return!1;const r=(null===(e=t.textContent)||void 0===e?void 0:e.trim())||\"\";return\",\"===r||\"\"===r||\" \"===r})))return!0}return r&&!o&&i}));i.length>0&&(i.forEach((t=>{t.remove(),e++})),o=!0)}(0,c.logDebug)(\"Removed empty elements:\",e,\"iterations:\",r)}(t),d(t),p(t,r),h(t),function(t,e){let r=0;const n=Date.now(),o=t=>{var e;if((0,c.isElement)(t)){const e=t.tagName.toLowerCase();if(\"pre\"===e||\"code\"===e)return}if(Array.from(t.childNodes).forEach(o),(0,c.isTextNode)(t)){const n=t.textContent||\"\";if(!n||n.match(/^[\\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF\\xA0\\s]*$/))null===(e=t.parentNode)||void 0===e||e.removeChild(t),r++;else{const e=n.replace(/\\n{3,}/g,\"\\n\\n\").replace(/^[\\n\\r\\t]+/,\"\").replace(/[\\n\\r\\t]+$/,\"\").replace(/[ \\t]*\\n[ \\t]*/g,\"\\n\").replace(/[ \\t]{3,}/g,\" \").replace(/^[ ]+$/,\" \").replace(/\\s+([,.!?:;])/g,\"$1\").replace(/[\\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF]+/g,\"\").replace(/(?:\\xA0){2,}/g,\"\\xa0\");e!==n&&(t.textContent=e,r+=n.length-e.length)}}},i=t=>{var n;if(!(0,c.isElement)(t))return;const o=t.tagName.toLowerCase();if(\"pre\"===o||\"code\"===o)return;Array.from(t.childNodes).filter(c.isElement).forEach(i),t.normalize();const a=\"block\"===(null===(n=(0,c.getComputedStyle)(t))||void 0===n?void 0:n.display),s=a?/^[\\n\\r\\t \\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF\\xA0]*$/:/^[\\n\\r\\t\\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF]*$/,l=a?/^[\\n\\r\\t \\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF\\xA0]*$/:/^[\\n\\r\\t\\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF]*$/;for(;t.firstChild&&(0,c.isTextNode)(t.firstChild)&&(t.firstChild.textContent||\"\").match(s);)t.removeChild(t.firstChild),r++;for(;t.lastChild&&(0,c.isTextNode)(t.lastChild)&&(t.lastChild.textContent||\"\").match(l);)t.removeChild(t.lastChild),r++;if(!a){const r=Array.from(t.childNodes);for(let n=0;n<r.length-1;n++){const o=r[n],i=r[n+1];if((0,c.isElement)(o)||(0,c.isElement)(i)){const r=i.textContent||\"\",n=o.textContent||\"\",a=r.match(/^[,.!?:;)\\]]/),s=n.match(/[,.!?:;(\\[]\\s*$/),l=(0,c.isTextNode)(o)&&(o.textContent||\"\").endsWith(\" \")||(0,c.isTextNode)(i)&&(i.textContent||\"\").startsWith(\" \");if(!a&&!s&&!l){const r=e.createTextNode(\" \");t.insertBefore(r,i)}}}}};o(t),i(t);const a=Date.now();(0,c.logDebug)(\"Removed empty lines:\",{charactersRemoved:r,processingTime:`${(a-n).toFixed(2)}ms`})}(t,r))};const n=r(640),o=r(0),i=r(754),a=r(610),s=r(864),l=r(649),c=r(552),u=[...o.mathRules,...i.codeBlockRules,...s.headingRules,...l.imageRules,{selector:'div[data-testid^=\"paragraph\"], div[role=\"paragraph\"]',element:\"p\",transform:(t,e)=>{const r=e.createElement(\"p\");return r.innerHTML=t.innerHTML,Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&r.setAttribute(t.name,t.value)})),r}},{selector:'div[role=\"list\"]',element:\"ul\",transform:(t,e)=>{var r;const n=t.querySelector('div[role=\"listitem\"] .label'),o=((null===(r=null==n?void 0:n.textContent)||void 0===r?void 0:r.trim())||\"\").match(/^\\d+\\)/),i=e.createElement(o?\"ol\":\"ul\");return t.querySelectorAll('div[role=\"listitem\"]').forEach((t=>{const r=e.createElement(\"li\"),n=t.querySelector(\".content\");if(n){n.querySelectorAll('div[role=\"paragraph\"]').forEach((t=>{const r=e.createElement(\"p\");r.innerHTML=t.innerHTML,t.replaceWith(r)}));n.querySelectorAll('div[role=\"list\"]').forEach((t=>{var r;const n=t.querySelector('div[role=\"listitem\"] .label'),o=((null===(r=null==n?void 0:n.textContent)||void 0===r?void 0:r.trim())||\"\").match(/^\\d+\\)/),i=e.createElement(o?\"ol\":\"ul\");t.querySelectorAll('div[role=\"listitem\"]').forEach((t=>{const r=e.createElement(\"li\"),n=t.querySelector(\".content\");if(n){n.querySelectorAll('div[role=\"paragraph\"]').forEach((t=>{const r=e.createElement(\"p\");r.innerHTML=t.innerHTML,t.replaceWith(r)})),r.innerHTML=n.innerHTML}i.appendChild(r)})),t.replaceWith(i)})),r.innerHTML=n.innerHTML}i.appendChild(r)})),i}},{selector:'div[role=\"listitem\"]',element:\"li\",transform:(t,e)=>{const r=t.querySelector(\".content\");if(!r)return t;return r.querySelectorAll('div[role=\"paragraph\"]').forEach((t=>{const r=e.createElement(\"p\");r.innerHTML=t.innerHTML,t.replaceWith(r)})),r}}];function d(t){let e=0;const r=e=>{let n=\"\",o=e.nextSibling;for(;o;)((0,c.isTextNode)(o)||(0,c.isElement)(o))&&(n+=o.textContent||\"\"),o=o.nextSibling;if(n.trim())return!0;const i=e.parentElement;return!(!i||i===t)&&r(i)};Array.from(t.querySelectorAll(\"h1, h2, h3, h4, h5, h6\")).reverse().forEach((t=>{r(t)||(t.remove(),e++)})),e>0&&(0,c.logDebug)(\"Removed trailing headings:\",e)}function m(t,e){let r=0;const o=t=>{if(\"svg\"===t.tagName.toLowerCase()||\"http://www.w3.org/2000/svg\"===t.namespaceURI)return;const o=Array.from(t.attributes),i=t.tagName.toLowerCase();o.forEach((o=>{const a=o.name.toLowerCase(),s=o.value;\"id\"===a&&(s.startsWith(\"fnref:\")||s.startsWith(\"fn:\")||\"footnotes\"===s)||\"class\"===a&&(\"code\"===i&&s.startsWith(\"language-\")||\"footnote-backref\"===s)||(e?n.ALLOWED_ATTRIBUTES.has(a)||n.ALLOWED_ATTRIBUTES_DEBUG.has(a)||a.startsWith(\"data-\")||(t.removeAttribute(o.name),r++):n.ALLOWED_ATTRIBUTES.has(a)||(t.removeAttribute(o.name),r++))}))};o(t),t.querySelectorAll(\"*\").forEach(o),(0,c.logDebug)(\"Stripped attributes:\",r)}function h(t){let e=0;const r=Date.now(),n=Array.from(t.getElementsByTagName(\"br\"));let o=[];const i=()=>{if(o.length>2)for(let t=2;t<o.length;t++)o[t].remove(),e++;o=[]};n.forEach((t=>{var e;let r=!1;if(o.length>0){const n=o[o.length-1];let i=t.previousSibling;for(;i&&(0,c.isTextNode)(i)&&!(null===(e=i.textContent)||void 0===e?void 0:e.trim());)i=i.previousSibling;i===n&&(r=!0)}r?o.push(t):(i(),o=[t])})),i();const a=Date.now();(0,c.logDebug)(\"Standardized br elements:\",{removed:e,processingTime:`${(a-r).toFixed(2)}ms`})}function p(t,e){let r=0;const o=Date.now();let i=!0;function a(t){var e;for(const r of t.childNodes){if((0,c.isTextNode)(r)&&(null===(e=r.textContent)||void 0===e?void 0:e.trim()))return!0;if((0,c.isElement)(r)&&n.INLINE_ELEMENTS.has(r.nodeName.toLowerCase()))return!0}return!1}const s=t=>{const e=t.tagName.toLowerCase();if(n.PRESERVE_ELEMENTS.has(e))return!0;const r=t.getAttribute(\"role\");if(r&&[\"article\",\"main\",\"navigation\",\"banner\",\"contentinfo\"].includes(r))return!0;const o=t.className;if(\"string\"==typeof o&&o.toLowerCase().match(/(?:article|main|content|footnote|reference|bibliography)/))return!0;return!!Array.from(t.children).some((t=>n.PRESERVE_ELEMENTS.has(t.tagName.toLowerCase())||\"article\"===t.getAttribute(\"role\")||t.className&&\"string\"==typeof t.className&&t.className.toLowerCase().match(/(?:article|main|content|footnote|reference|bibliography)/)))},l=t=>{var e;if(a(t))return!1;if(!(null===(e=t.textContent)||void 0===e?void 0:e.trim()))return!0;const r=Array.from(t.children);if(0===r.length)return!0;if(r.every((t=>{const e=t.tagName.toLowerCase();return n.BLOCK_ELEMENTS.includes(e)||\"p\"===e||\"h1\"===e||\"h2\"===e||\"h3\"===e||\"h4\"===e||\"h5\"===e||\"h6\"===e||\"ul\"===e||\"ol\"===e||\"pre\"===e||\"blockquote\"===e||\"figure\"===e})))return!0;const o=t.className.toLowerCase();if(/(?:wrapper|container|layout|row|col|grid|flex|outer|inner|content-area)/i.test(o))return!0;const i=Array.from(t.childNodes).filter((t=>{var e;return(0,c.isTextNode)(t)&&(null===(e=t.textContent)||void 0===e?void 0:e.trim())}));if(0===i.length)return!0;return!(!(r.length>0)||r.some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)})))},u=o=>{var i,u;if(!o.isConnected||s(o))return!1;const d=o.tagName.toLowerCase();if(!n.ALLOWED_EMPTY_ELEMENTS.has(d)&&!o.children.length&&!(null===(i=o.textContent)||void 0===i?void 0:i.trim()))return o.remove(),r++,!0;if(o.parentElement===t){const t=Array.from(o.children);if(t.length>0&&!t.some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)}))){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}}if(l(o)){if(!Array.from(o.children).some((t=>{const e=t.tagName.toLowerCase();return n.INLINE_ELEMENTS.has(e)}))){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}const m=Array.from(o.childNodes);if(m.length>0&&m.every((t=>(0,c.isTextNode)(t)||(0,c.isElement)(t)&&n.INLINE_ELEMENTS.has(t.nodeName.toLowerCase())))&&(null===(u=o.textContent)||void 0===u?void 0:u.trim())){const t=e.createElement(\"p\");for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}if(1===o.children.length){const t=o.firstElementChild,e=t.tagName.toLowerCase();if(n.BLOCK_ELEMENTS.includes(e)&&!s(t))return o.replaceWith(t),r++,!0}let h=0,p=o.parentElement;for(;p;){const t=p.tagName.toLowerCase();n.BLOCK_ELEMENTS.includes(t)&&h++,p=p.parentElement}if(h>0&&!a(o)){const t=e.createDocumentFragment();for(;o.firstChild;)t.appendChild(o.firstChild);return o.replaceWith(t),r++,!0}return!1},d=()=>{const e=Array.from(t.children).filter((t=>n.BLOCK_ELEMENTS.includes(t.tagName.toLowerCase())));let r=!1;return e.forEach((t=>{u(t)&&(r=!0)})),r},m=()=>{const e=Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(\",\"))).sort(((t,e)=>{const r=t=>{let e=0,r=t.parentElement;for(;r;){const t=r.tagName.toLowerCase();n.BLOCK_ELEMENTS.includes(t)&&e++,r=r.parentElement}return e};return r(e)-r(t)}));let r=!1;return e.forEach((t=>{u(t)&&(r=!0)})),r},h=()=>{const o=Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(\",\")));let i=!1;return o.forEach((t=>{const n=Array.from(t.children);if(n.length>0&&n.every((t=>\"p\"===t.tagName.toLowerCase()))||!s(t)&&l(t)){const n=e.createDocumentFragment();for(;t.firstChild;)n.appendChild(t.firstChild);t.replaceWith(n),r++,i=!0}})),i};do{i=!1,d()&&(i=!0),m()&&(i=!0),h()&&(i=!0)}while(i);const p=Date.now();(0,c.logDebug)(\"Flattened wrapper elements:\",{count:r,processingTime:`${(p-o).toFixed(2)}ms`})}},864:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.headingRules=void 0;const n=r(640);e.headingRules=[{selector:\"h1, h2, h3, h4, h5, h6\",element:\"keep\",transform:t=>{var e;const r=t.ownerDocument;if(!r)return console.warn(\"No document available\"),t;const o=r.createElement(t.tagName);Array.from(t.attributes).forEach((t=>{n.ALLOWED_ATTRIBUTES.has(t.name)&&o.setAttribute(t.name,t.value)}));const i=t.cloneNode(!0),a=new Map;Array.from(i.querySelectorAll(\"*\")).forEach((t=>{var e,r,n,o,s,l;let c=!1;if(\"a\"===t.tagName.toLowerCase()){const r=t.getAttribute(\"href\");((null==r?void 0:r.includes(\"#\"))||(null==r?void 0:r.startsWith(\"#\")))&&(a.set(t,(null===(e=t.textContent)||void 0===e?void 0:e.trim())||\"\"),c=!0)}if(t.classList.contains(\"anchor\")&&(a.set(t,(null===(r=t.textContent)||void 0===r?void 0:r.trim())||\"\"),c=!0),\"button\"===t.tagName.toLowerCase()&&(c=!0),(\"span\"===t.tagName.toLowerCase()||\"div\"===t.tagName.toLowerCase())&&t.querySelector('a[href^=\"#\"]')){const e=t.querySelector('a[href^=\"#\"]');e&&a.set(t,(null===(n=e.textContent)||void 0===n?void 0:n.trim())||\"\"),c=!0}if(c){const e=t.parentElement;e&&e!==i&&(null===(o=e.textContent)||void 0===o?void 0:o.trim())===(null===(s=t.textContent)||void 0===s?void 0:s.trim())&&a.set(e,(null===(l=t.textContent)||void 0===l?void 0:l.trim())||\"\")}}));Array.from(i.querySelectorAll(\"*\")).filter((t=>{if(\"a\"===t.tagName.toLowerCase()){const e=t.getAttribute(\"href\");return(null==e?void 0:e.includes(\"#\"))||(null==e?void 0:e.startsWith(\"#\"))}return!!t.classList.contains(\"anchor\")||(\"button\"===t.tagName.toLowerCase()||!(\"span\"!==t.tagName.toLowerCase()&&\"div\"!==t.tagName.toLowerCase()||!t.querySelector('a[href^=\"#\"]')))})).forEach((t=>t.remove()));let s=(null===(e=i.textContent)||void 0===e?void 0:e.trim())||\"\";return!s&&a.size>0&&(s=Array.from(a.values())[0]),o.textContent=s,o}}]},917:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.ExtractorRegistry=void 0;const n=r(959),o=r(248),i=r(258),a=r(458),s=r(632),l=r(397),c=r(20),u=r(732);class d{static initialize(){this.register({patterns:[\"twitter.com\",/\\/x\\.com\\/.*/],extractor:o.TwitterExtractor}),this.register({patterns:[\"reddit.com\",\"old.reddit.com\",\"new.reddit.com\",/^https:\\/\\/[^\\/]+\\.reddit\\.com/],extractor:n.RedditExtractor}),this.register({patterns:[\"youtube.com\",\"youtu.be\",/youtube\\.com\\/watch\\?v=.*/,/youtu\\.be\\/.*/],extractor:i.YoutubeExtractor}),this.register({patterns:[/news\\.ycombinator\\.com\\/item\\?id=.*/],extractor:a.HackerNewsExtractor}),this.register({patterns:[/^https?:\\/\\/chatgpt\\.com\\/(c|share)\\/.*/],extractor:s.ChatGPTExtractor}),this.register({patterns:[/^https?:\\/\\/claude\\.ai\\/(chat|share)\\/.*/],extractor:l.ClaudeExtractor}),this.register({patterns:[/^https?:\\/\\/grok\\.com\\/(chat|share)(\\/.*)?$/],extractor:c.GrokExtractor}),this.register({patterns:[/^https?:\\/\\/gemini\\.google\\.com\\/app\\/.*/],extractor:u.GeminiExtractor})}static register(t){this.mappings.push(t)}static findExtractor(t,e,r){try{const n=new URL(e).hostname;if(this.domainCache.has(n)){const o=this.domainCache.get(n);return o?new o(t,e,r):null}for(const{patterns:o,extractor:i}of this.mappings){if(o.some((t=>t instanceof RegExp?t.test(e):n.includes(t))))return this.domainCache.set(n,i),new i(t,e,r)}return this.domainCache.set(n,null),null}catch(t){return console.error(\"Error in findExtractor:\",t),null}}static clearCache(){this.domainCache.clear()}}e.ExtractorRegistry=d,d.mappings=[],d.domainCache=new Map,d.initialize()},959:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.RedditExtractor=void 0;const n=r(279);class o extends n.BaseExtractor{constructor(t,e){super(t,e),this.shredditPost=t.querySelector(\"shreddit-post\")}canExtract(){return!!this.shredditPost}extract(){var t,e;const r=this.getPostContent(),n=this.extractComments(),o=this.createContentHtml(r,n),i=(null===(e=null===(t=this.document.querySelector(\"h1\"))||void 0===t?void 0:t.textContent)||void 0===e?void 0:e.trim())||\"\",a=this.getSubreddit(),s=this.getPostAuthor(),l=this.createDescription(r);return{content:o,contentHtml:o,extractedContent:{postId:this.getPostId(),subreddit:a,postAuthor:s},variables:{title:i,author:s,site:`r/${a}`,description:l}}}getPostContent(){var t,e,r,n;return((null===(e=null===(t=this.shredditPost)||void 0===t?void 0:t.querySelector('[slot=\"text-body\"]'))||void 0===e?void 0:e.innerHTML)||\"\")+((null===(n=null===(r=this.shredditPost)||void 0===r?void 0:r.querySelector(\"#post-image\"))||void 0===n?void 0:n.outerHTML)||\"\")}createContentHtml(t,e){return`\\n\\t\\t\\t<div class=\"reddit-post\">\\n\\t\\t\\t\\t<div class=\"post-content\">\\n\\t\\t\\t\\t\\t${t}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t</div>\\n\\t\\t\\t${e?`\\n\\t\\t\\t\\t<hr>\\n\\t\\t\\t\\t<h2>Comments</h2>\\n\\t\\t\\t\\t<div class=\"reddit-comments\">\\n\\t\\t\\t\\t\\t${e}\\n\\t\\t\\t\\t</div>\\n\\t\\t\\t`:\"\"}\\n\\t\\t`.trim()}extractComments(){const t=Array.from(this.document.querySelectorAll(\"shreddit-comment\"));return this.processComments(t)}getPostId(){const t=this.url.match(/comments\\/([a-zA-Z0-9]+)/);return(null==t?void 0:t[1])||\"\"}getSubreddit(){const t=this.url.match(/\\/r\\/([^/]+)/);return(null==t?void 0:t[1])||\"\"}getPostAuthor(){var t;return(null===(t=this.shredditPost)||void 0===t?void 0:t.getAttribute(\"author\"))||\"\"}createDescription(t){var e;if(!t)return\"\";const r=document.createElement(\"div\");return r.innerHTML=t,(null===(e=r.textContent)||void 0===e?void 0:e.trim().slice(0,140).replace(/\\s+/g,\" \"))||\"\"}processComments(t){var e;let r=\"\",n=-1,o=[];for(const i of t){const t=parseInt(i.getAttribute(\"depth\")||\"0\"),a=i.getAttribute(\"author\")||\"\",s=i.getAttribute(\"score\")||\"0\",l=i.getAttribute(\"permalink\")||\"\",c=(null===(e=i.querySelector('[slot=\"comment\"]'))||void 0===e?void 0:e.innerHTML)||\"\",u=i.querySelector(\"faceplate-timeago\"),d=(null==u?void 0:u.getAttribute(\"ts\"))||\"\",m=d?new Date(d).toISOString().split(\"T\")[0]:\"\";if(0===t){for(;o.length>0;)r+=\"</blockquote>\",o.pop();r+=\"<blockquote>\",o=[0],n=0}else if(t<n)for(;o.length>0&&o[o.length-1]>=t;)r+=\"</blockquote>\",o.pop();else t>n&&(r+=\"<blockquote>\",o.push(t));r+=`<div class=\"comment\">\\n\\t<div class=\"comment-metadata\">\\n\\t\\t<span class=\"comment-author\"><strong>${a}</strong></span> \\u2022\\n\\t\\t<a href=\"https://reddit.com${l}\" class=\"comment-link\">${s} points</a> \\u2022\\n\\t\\t<span class=\"comment-date\">${m}</span>\\n\\t</div>\\n\\t<div class=\"comment-content\">${c}</div>\\n</div>`,n=t}for(;o.length>0;)r+=\"</blockquote>\",o.pop();return r}}e.RedditExtractor=o},968:(t,e,r)=>{Object.defineProperty(e,\"__esModule\",{value:!0}),e.ContentScorer=void 0;const n=r(640),o=[\"admonition\",\"article\",\"content\",\"entry\",\"image\",\"img\",\"font\",\"figure\",\"figcaption\",\"pre\",\"main\",\"post\",\"story\",\"table\"],i=[\"advertisement\",\"all rights reserved\",\"banner\",\"cookie\",\"comments\",\"copyright\",\"follow me\",\"follow us\",\"footer\",\"header\",\"homepage\",\"login\",\"menu\",\"more articles\",\"more like this\",\"most read\",\"nav\",\"navigation\",\"newsletter\",\"newsletter\",\"popular\",\"privacy\",\"recommended\",\"register\",\"related\",\"responses\",\"share\",\"sidebar\",\"sign in\",\"sign up\",\"signup\",\"social\",\"sponsored\",\"subscribe\",\"subscribe\",\"terms\",\"trending\"],a=[\"ad\",\"banner\",\"cookie\",\"copyright\",\"footer\",\"header\",\"homepage\",\"menu\",\"nav\",\"newsletter\",\"popular\",\"privacy\",\"recommended\",\"related\",\"rights\",\"share\",\"sidebar\",\"social\",\"sponsored\",\"subscribe\",\"terms\",\"trending\",\"widget\"];class s{constructor(t,e=!1){this.doc=t,this.debug=e}static scoreElement(t){let e=0;const r=t.textContent||\"\",o=r.split(/\\s+/).length;e+=o;e+=10*t.getElementsByTagName(\"p\").length;e-=5*(t.getElementsByTagName(\"a\").length/(o||1));e-=3*(t.getElementsByTagName(\"img\").length/(o||1));try{const r=t.getAttribute(\"style\")||\"\",n=t.getAttribute(\"align\")||\"\";(r.includes(\"float: right\")||r.includes(\"text-align: right\")||\"right\"===n)&&(e+=5)}catch(t){}/\\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\\s+\\d{1,2},?\\s+\\d{4}\\b/i.test(r)&&(e+=10);/\\b(?:by|written by|author:)\\s+[A-Za-z\\s]+\\b/i.test(r)&&(e+=10);const i=t.className.toLowerCase();(i.includes(\"content\")||i.includes(\"article\")||i.includes(\"post\"))&&(e+=15);t.querySelector(n.FOOTNOTE_INLINE_REFERENCES)&&(e+=10);t.querySelector(n.FOOTNOTE_LIST_SELECTORS)&&(e+=10);if(e-=5*t.getElementsByTagName(\"table\").length,\"td\"===t.tagName.toLowerCase()){const r=t.closest(\"table\");if(r){const n=parseInt(r.getAttribute(\"width\")||\"0\"),o=r.getAttribute(\"align\")||\"\",i=r.className.toLowerCase();if(n>400||\"center\"===o||i.includes(\"content\")||i.includes(\"article\")){const n=Array.from(r.getElementsByTagName(\"td\")),o=n.indexOf(t);o>0&&o<n.length-1&&(e+=10)}}}return e}static findBestElement(t,e=50){let r=null,n=0;return t.forEach((t=>{const e=this.scoreElement(t);e>n&&(n=e,r=t)})),n>e?r:null}static scoreAndRemove(t,e=!1){const r=Date.now();let o=0;const i=new Set;Array.from(t.querySelectorAll(n.BLOCK_ELEMENTS.join(\",\"))).forEach((t=>{if(i.has(t))return;if(s.isLikelyContent(t))return;s.scoreNonContentBlock(t)<0&&(i.add(t),o++)})),i.forEach((t=>t.remove()));const a=Date.now();e&&console.log(\"Defuddle\",\"Removed non-content blocks:\",{count:o,processingTime:`${(a-r).toFixed(2)}ms`})}static isLikelyContent(t){const e=t.getAttribute(\"role\");if(e&&[\"article\",\"main\",\"contentinfo\"].includes(e))return!0;const r=t.className.toLowerCase(),n=t.id.toLowerCase();for(const t of o)if(r.includes(t)||n.includes(t))return!0;const i=(t.textContent||\"\").split(/\\s+/).length,a=t.getElementsByTagName(\"p\").length;return i>50&&a>1||(i>100||i>30&&a>0)}static scoreNonContentBlock(t){if(t.querySelector(n.FOOTNOTE_LIST_SELECTORS))return 0;let e=0;const r=t.textContent||\"\",o=r.split(/\\s+/).length;if(o<3)return 0;for(const t of i)r.toLowerCase().includes(t)&&(e-=10);const s=t.getElementsByTagName(\"a\").length;s/(o||1)>.5&&(e-=15);const l=t.getElementsByTagName(\"ul\").length+t.getElementsByTagName(\"ol\").length;l>0&&s>3*l&&(e-=10);const c=t.className.toLowerCase(),u=t.id.toLowerCase();for(const t of a)(c.includes(t)||u.includes(t))&&(e-=8);return e}}e.ContentScorer=s}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,r),i.exports}var n={};return(()=>{var t=n;const e=r(628);t.default=e.Defuddle})(),n=n.default})()));", "// src/utils/env.ts\nvar NOTHING = Symbol.for(\"immer-nothing\");\nvar DRAFTABLE = Symbol.for(\"immer-draftable\");\nvar DRAFT_STATE = Symbol.for(\"immer-state\");\n\n// src/utils/errors.ts\nvar errors = process.env.NODE_ENV !== \"production\" ? [\n  // All error codes, starting by 0:\n  function(plugin) {\n    return `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`;\n  },\n  function(thing) {\n    return `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`;\n  },\n  \"This object has been frozen and should not be mutated\",\n  function(data) {\n    return \"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" + data;\n  },\n  \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n  \"Immer forbids circular references\",\n  \"The first or second argument to `produce` must be a function\",\n  \"The third argument to `produce` must be a function or undefined\",\n  \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n  \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n  function(thing) {\n    return `'current' expects a draft, got: ${thing}`;\n  },\n  \"Object.defineProperty() cannot be used on an Immer draft\",\n  \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n  \"Immer only supports deleting array indices\",\n  \"Immer only supports setting array indices and the 'length' property\",\n  function(thing) {\n    return `'original' expects a draft, got: ${thing}`;\n  }\n  // Note: if more errors are added, the errorOffset in Patches.ts should be increased\n  // See Patches.ts for additional errors\n] : [];\nfunction die(error, ...args) {\n  if (process.env.NODE_ENV !== \"production\") {\n    const e = errors[error];\n    const msg = typeof e === \"function\" ? e.apply(null, args) : e;\n    throw new Error(`[Immer] ${msg}`);\n  }\n  throw new Error(\n    `[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`\n  );\n}\n\n// src/utils/common.ts\nvar getPrototypeOf = Object.getPrototypeOf;\nfunction isDraft(value) {\n  return !!value && !!value[DRAFT_STATE];\n}\nfunction isDraftable(value) {\n  if (!value)\n    return false;\n  return isPlainObject(value) || Array.isArray(value) || !!value[DRAFTABLE] || !!value.constructor?.[DRAFTABLE] || isMap(value) || isSet(value);\n}\nvar objectCtorString = Object.prototype.constructor.toString();\nfunction isPlainObject(value) {\n  if (!value || typeof value !== \"object\")\n    return false;\n  const proto = getPrototypeOf(value);\n  if (proto === null) {\n    return true;\n  }\n  const Ctor = Object.hasOwnProperty.call(proto, \"constructor\") && proto.constructor;\n  if (Ctor === Object)\n    return true;\n  return typeof Ctor == \"function\" && Function.toString.call(Ctor) === objectCtorString;\n}\nfunction original(value) {\n  if (!isDraft(value))\n    die(15, value);\n  return value[DRAFT_STATE].base_;\n}\nfunction each(obj, iter) {\n  if (getArchtype(obj) === 0 /* Object */) {\n    Reflect.ownKeys(obj).forEach((key) => {\n      iter(key, obj[key], obj);\n    });\n  } else {\n    obj.forEach((entry, index) => iter(index, entry, obj));\n  }\n}\nfunction getArchtype(thing) {\n  const state = thing[DRAFT_STATE];\n  return state ? state.type_ : Array.isArray(thing) ? 1 /* Array */ : isMap(thing) ? 2 /* Map */ : isSet(thing) ? 3 /* Set */ : 0 /* Object */;\n}\nfunction has(thing, prop) {\n  return getArchtype(thing) === 2 /* Map */ ? thing.has(prop) : Object.prototype.hasOwnProperty.call(thing, prop);\n}\nfunction get(thing, prop) {\n  return getArchtype(thing) === 2 /* Map */ ? thing.get(prop) : thing[prop];\n}\nfunction set(thing, propOrOldValue, value) {\n  const t = getArchtype(thing);\n  if (t === 2 /* Map */)\n    thing.set(propOrOldValue, value);\n  else if (t === 3 /* Set */) {\n    thing.add(value);\n  } else\n    thing[propOrOldValue] = value;\n}\nfunction is(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\nfunction isMap(target) {\n  return target instanceof Map;\n}\nfunction isSet(target) {\n  return target instanceof Set;\n}\nfunction latest(state) {\n  return state.copy_ || state.base_;\n}\nfunction shallowCopy(base, strict) {\n  if (isMap(base)) {\n    return new Map(base);\n  }\n  if (isSet(base)) {\n    return new Set(base);\n  }\n  if (Array.isArray(base))\n    return Array.prototype.slice.call(base);\n  const isPlain = isPlainObject(base);\n  if (strict === true || strict === \"class_only\" && !isPlain) {\n    const descriptors = Object.getOwnPropertyDescriptors(base);\n    delete descriptors[DRAFT_STATE];\n    let keys = Reflect.ownKeys(descriptors);\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const desc = descriptors[key];\n      if (desc.writable === false) {\n        desc.writable = true;\n        desc.configurable = true;\n      }\n      if (desc.get || desc.set)\n        descriptors[key] = {\n          configurable: true,\n          writable: true,\n          // could live with !!desc.set as well here...\n          enumerable: desc.enumerable,\n          value: base[key]\n        };\n    }\n    return Object.create(getPrototypeOf(base), descriptors);\n  } else {\n    const proto = getPrototypeOf(base);\n    if (proto !== null && isPlain) {\n      return { ...base };\n    }\n    const obj = Object.create(proto);\n    return Object.assign(obj, base);\n  }\n}\nfunction freeze(obj, deep = false) {\n  if (isFrozen(obj) || isDraft(obj) || !isDraftable(obj))\n    return obj;\n  if (getArchtype(obj) > 1) {\n    obj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections;\n  }\n  Object.freeze(obj);\n  if (deep)\n    Object.entries(obj).forEach(([key, value]) => freeze(value, true));\n  return obj;\n}\nfunction dontMutateFrozenCollections() {\n  die(2);\n}\nfunction isFrozen(obj) {\n  return Object.isFrozen(obj);\n}\n\n// src/utils/plugins.ts\nvar plugins = {};\nfunction getPlugin(pluginKey) {\n  const plugin = plugins[pluginKey];\n  if (!plugin) {\n    die(0, pluginKey);\n  }\n  return plugin;\n}\nfunction loadPlugin(pluginKey, implementation) {\n  if (!plugins[pluginKey])\n    plugins[pluginKey] = implementation;\n}\n\n// src/core/scope.ts\nvar currentScope;\nfunction getCurrentScope() {\n  return currentScope;\n}\nfunction createScope(parent_, immer_) {\n  return {\n    drafts_: [],\n    parent_,\n    immer_,\n    // Whenever the modified draft contains a draft from another scope, we\n    // need to prevent auto-freezing so the unowned draft can be finalized.\n    canAutoFreeze_: true,\n    unfinalizedDrafts_: 0\n  };\n}\nfunction usePatchesInScope(scope, patchListener) {\n  if (patchListener) {\n    getPlugin(\"Patches\");\n    scope.patches_ = [];\n    scope.inversePatches_ = [];\n    scope.patchListener_ = patchListener;\n  }\n}\nfunction revokeScope(scope) {\n  leaveScope(scope);\n  scope.drafts_.forEach(revokeDraft);\n  scope.drafts_ = null;\n}\nfunction leaveScope(scope) {\n  if (scope === currentScope) {\n    currentScope = scope.parent_;\n  }\n}\nfunction enterScope(immer2) {\n  return currentScope = createScope(currentScope, immer2);\n}\nfunction revokeDraft(draft) {\n  const state = draft[DRAFT_STATE];\n  if (state.type_ === 0 /* Object */ || state.type_ === 1 /* Array */)\n    state.revoke_();\n  else\n    state.revoked_ = true;\n}\n\n// src/core/finalize.ts\nfunction processResult(result, scope) {\n  scope.unfinalizedDrafts_ = scope.drafts_.length;\n  const baseDraft = scope.drafts_[0];\n  const isReplaced = result !== void 0 && result !== baseDraft;\n  if (isReplaced) {\n    if (baseDraft[DRAFT_STATE].modified_) {\n      revokeScope(scope);\n      die(4);\n    }\n    if (isDraftable(result)) {\n      result = finalize(scope, result);\n      if (!scope.parent_)\n        maybeFreeze(scope, result);\n    }\n    if (scope.patches_) {\n      getPlugin(\"Patches\").generateReplacementPatches_(\n        baseDraft[DRAFT_STATE].base_,\n        result,\n        scope.patches_,\n        scope.inversePatches_\n      );\n    }\n  } else {\n    result = finalize(scope, baseDraft, []);\n  }\n  revokeScope(scope);\n  if (scope.patches_) {\n    scope.patchListener_(scope.patches_, scope.inversePatches_);\n  }\n  return result !== NOTHING ? result : void 0;\n}\nfunction finalize(rootScope, value, path) {\n  if (isFrozen(value))\n    return value;\n  const state = value[DRAFT_STATE];\n  if (!state) {\n    each(\n      value,\n      (key, childValue) => finalizeProperty(rootScope, state, value, key, childValue, path)\n    );\n    return value;\n  }\n  if (state.scope_ !== rootScope)\n    return value;\n  if (!state.modified_) {\n    maybeFreeze(rootScope, state.base_, true);\n    return state.base_;\n  }\n  if (!state.finalized_) {\n    state.finalized_ = true;\n    state.scope_.unfinalizedDrafts_--;\n    const result = state.copy_;\n    let resultEach = result;\n    let isSet2 = false;\n    if (state.type_ === 3 /* Set */) {\n      resultEach = new Set(result);\n      result.clear();\n      isSet2 = true;\n    }\n    each(\n      resultEach,\n      (key, childValue) => finalizeProperty(rootScope, state, result, key, childValue, path, isSet2)\n    );\n    maybeFreeze(rootScope, result, false);\n    if (path && rootScope.patches_) {\n      getPlugin(\"Patches\").generatePatches_(\n        state,\n        path,\n        rootScope.patches_,\n        rootScope.inversePatches_\n      );\n    }\n  }\n  return state.copy_;\n}\nfunction finalizeProperty(rootScope, parentState, targetObject, prop, childValue, rootPath, targetIsSet) {\n  if (process.env.NODE_ENV !== \"production\" && childValue === targetObject)\n    die(5);\n  if (isDraft(childValue)) {\n    const path = rootPath && parentState && parentState.type_ !== 3 /* Set */ && // Set objects are atomic since they have no keys.\n    !has(parentState.assigned_, prop) ? rootPath.concat(prop) : void 0;\n    const res = finalize(rootScope, childValue, path);\n    set(targetObject, prop, res);\n    if (isDraft(res)) {\n      rootScope.canAutoFreeze_ = false;\n    } else\n      return;\n  } else if (targetIsSet) {\n    targetObject.add(childValue);\n  }\n  if (isDraftable(childValue) && !isFrozen(childValue)) {\n    if (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n      return;\n    }\n    finalize(rootScope, childValue);\n    if ((!parentState || !parentState.scope_.parent_) && typeof prop !== \"symbol\" && Object.prototype.propertyIsEnumerable.call(targetObject, prop))\n      maybeFreeze(rootScope, childValue);\n  }\n}\nfunction maybeFreeze(scope, value, deep = false) {\n  if (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n    freeze(value, deep);\n  }\n}\n\n// src/core/proxy.ts\nfunction createProxyProxy(base, parent) {\n  const isArray = Array.isArray(base);\n  const state = {\n    type_: isArray ? 1 /* Array */ : 0 /* Object */,\n    // Track which produce call this is associated with.\n    scope_: parent ? parent.scope_ : getCurrentScope(),\n    // True for both shallow and deep changes.\n    modified_: false,\n    // Used during finalization.\n    finalized_: false,\n    // Track which properties have been assigned (true) or deleted (false).\n    assigned_: {},\n    // The parent draft state.\n    parent_: parent,\n    // The base state.\n    base_: base,\n    // The base proxy.\n    draft_: null,\n    // set below\n    // The base copy with any updated values.\n    copy_: null,\n    // Called by the `produce` function.\n    revoke_: null,\n    isManual_: false\n  };\n  let target = state;\n  let traps = objectTraps;\n  if (isArray) {\n    target = [state];\n    traps = arrayTraps;\n  }\n  const { revoke, proxy } = Proxy.revocable(target, traps);\n  state.draft_ = proxy;\n  state.revoke_ = revoke;\n  return proxy;\n}\nvar objectTraps = {\n  get(state, prop) {\n    if (prop === DRAFT_STATE)\n      return state;\n    const source = latest(state);\n    if (!has(source, prop)) {\n      return readPropFromProto(state, source, prop);\n    }\n    const value = source[prop];\n    if (state.finalized_ || !isDraftable(value)) {\n      return value;\n    }\n    if (value === peek(state.base_, prop)) {\n      prepareCopy(state);\n      return state.copy_[prop] = createProxy(value, state);\n    }\n    return value;\n  },\n  has(state, prop) {\n    return prop in latest(state);\n  },\n  ownKeys(state) {\n    return Reflect.ownKeys(latest(state));\n  },\n  set(state, prop, value) {\n    const desc = getDescriptorFromProto(latest(state), prop);\n    if (desc?.set) {\n      desc.set.call(state.draft_, value);\n      return true;\n    }\n    if (!state.modified_) {\n      const current2 = peek(latest(state), prop);\n      const currentState = current2?.[DRAFT_STATE];\n      if (currentState && currentState.base_ === value) {\n        state.copy_[prop] = value;\n        state.assigned_[prop] = false;\n        return true;\n      }\n      if (is(value, current2) && (value !== void 0 || has(state.base_, prop)))\n        return true;\n      prepareCopy(state);\n      markChanged(state);\n    }\n    if (state.copy_[prop] === value && // special case: handle new props with value 'undefined'\n    (value !== void 0 || prop in state.copy_) || // special case: NaN\n    Number.isNaN(value) && Number.isNaN(state.copy_[prop]))\n      return true;\n    state.copy_[prop] = value;\n    state.assigned_[prop] = true;\n    return true;\n  },\n  deleteProperty(state, prop) {\n    if (peek(state.base_, prop) !== void 0 || prop in state.base_) {\n      state.assigned_[prop] = false;\n      prepareCopy(state);\n      markChanged(state);\n    } else {\n      delete state.assigned_[prop];\n    }\n    if (state.copy_) {\n      delete state.copy_[prop];\n    }\n    return true;\n  },\n  // Note: We never coerce `desc.value` into an Immer draft, because we can't make\n  // the same guarantee in ES5 mode.\n  getOwnPropertyDescriptor(state, prop) {\n    const owner = latest(state);\n    const desc = Reflect.getOwnPropertyDescriptor(owner, prop);\n    if (!desc)\n      return desc;\n    return {\n      writable: true,\n      configurable: state.type_ !== 1 /* Array */ || prop !== \"length\",\n      enumerable: desc.enumerable,\n      value: owner[prop]\n    };\n  },\n  defineProperty() {\n    die(11);\n  },\n  getPrototypeOf(state) {\n    return getPrototypeOf(state.base_);\n  },\n  setPrototypeOf() {\n    die(12);\n  }\n};\nvar arrayTraps = {};\neach(objectTraps, (key, fn) => {\n  arrayTraps[key] = function() {\n    arguments[0] = arguments[0][0];\n    return fn.apply(this, arguments);\n  };\n});\narrayTraps.deleteProperty = function(state, prop) {\n  if (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop)))\n    die(13);\n  return arrayTraps.set.call(this, state, prop, void 0);\n};\narrayTraps.set = function(state, prop, value) {\n  if (process.env.NODE_ENV !== \"production\" && prop !== \"length\" && isNaN(parseInt(prop)))\n    die(14);\n  return objectTraps.set.call(this, state[0], prop, value, state[0]);\n};\nfunction peek(draft, prop) {\n  const state = draft[DRAFT_STATE];\n  const source = state ? latest(state) : draft;\n  return source[prop];\n}\nfunction readPropFromProto(state, source, prop) {\n  const desc = getDescriptorFromProto(source, prop);\n  return desc ? `value` in desc ? desc.value : (\n    // This is a very special case, if the prop is a getter defined by the\n    // prototype, we should invoke it with the draft as context!\n    desc.get?.call(state.draft_)\n  ) : void 0;\n}\nfunction getDescriptorFromProto(source, prop) {\n  if (!(prop in source))\n    return void 0;\n  let proto = getPrototypeOf(source);\n  while (proto) {\n    const desc = Object.getOwnPropertyDescriptor(proto, prop);\n    if (desc)\n      return desc;\n    proto = getPrototypeOf(proto);\n  }\n  return void 0;\n}\nfunction markChanged(state) {\n  if (!state.modified_) {\n    state.modified_ = true;\n    if (state.parent_) {\n      markChanged(state.parent_);\n    }\n  }\n}\nfunction prepareCopy(state) {\n  if (!state.copy_) {\n    state.copy_ = shallowCopy(\n      state.base_,\n      state.scope_.immer_.useStrictShallowCopy_\n    );\n  }\n}\n\n// src/core/immerClass.ts\nvar Immer2 = class {\n  constructor(config) {\n    this.autoFreeze_ = true;\n    this.useStrictShallowCopy_ = false;\n    /**\n     * The `produce` function takes a value and a \"recipe function\" (whose\n     * return value often depends on the base state). The recipe function is\n     * free to mutate its first argument however it wants. All mutations are\n     * only ever applied to a __copy__ of the base state.\n     *\n     * Pass only a function to create a \"curried producer\" which relieves you\n     * from passing the recipe function every time.\n     *\n     * Only plain objects and arrays are made mutable. All other objects are\n     * considered uncopyable.\n     *\n     * Note: This function is __bound__ to its `Immer` instance.\n     *\n     * @param {any} base - the initial state\n     * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n     * @param {Function} patchListener - optional function that will be called with all the patches produced here\n     * @returns {any} a new state, or the initial state if nothing was modified\n     */\n    this.produce = (base, recipe, patchListener) => {\n      if (typeof base === \"function\" && typeof recipe !== \"function\") {\n        const defaultBase = recipe;\n        recipe = base;\n        const self = this;\n        return function curriedProduce(base2 = defaultBase, ...args) {\n          return self.produce(base2, (draft) => recipe.call(this, draft, ...args));\n        };\n      }\n      if (typeof recipe !== \"function\")\n        die(6);\n      if (patchListener !== void 0 && typeof patchListener !== \"function\")\n        die(7);\n      let result;\n      if (isDraftable(base)) {\n        const scope = enterScope(this);\n        const proxy = createProxy(base, void 0);\n        let hasError = true;\n        try {\n          result = recipe(proxy);\n          hasError = false;\n        } finally {\n          if (hasError)\n            revokeScope(scope);\n          else\n            leaveScope(scope);\n        }\n        usePatchesInScope(scope, patchListener);\n        return processResult(result, scope);\n      } else if (!base || typeof base !== \"object\") {\n        result = recipe(base);\n        if (result === void 0)\n          result = base;\n        if (result === NOTHING)\n          result = void 0;\n        if (this.autoFreeze_)\n          freeze(result, true);\n        if (patchListener) {\n          const p = [];\n          const ip = [];\n          getPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip);\n          patchListener(p, ip);\n        }\n        return result;\n      } else\n        die(1, base);\n    };\n    this.produceWithPatches = (base, recipe) => {\n      if (typeof base === \"function\") {\n        return (state, ...args) => this.produceWithPatches(state, (draft) => base(draft, ...args));\n      }\n      let patches, inversePatches;\n      const result = this.produce(base, recipe, (p, ip) => {\n        patches = p;\n        inversePatches = ip;\n      });\n      return [result, patches, inversePatches];\n    };\n    if (typeof config?.autoFreeze === \"boolean\")\n      this.setAutoFreeze(config.autoFreeze);\n    if (typeof config?.useStrictShallowCopy === \"boolean\")\n      this.setUseStrictShallowCopy(config.useStrictShallowCopy);\n  }\n  createDraft(base) {\n    if (!isDraftable(base))\n      die(8);\n    if (isDraft(base))\n      base = current(base);\n    const scope = enterScope(this);\n    const proxy = createProxy(base, void 0);\n    proxy[DRAFT_STATE].isManual_ = true;\n    leaveScope(scope);\n    return proxy;\n  }\n  finishDraft(draft, patchListener) {\n    const state = draft && draft[DRAFT_STATE];\n    if (!state || !state.isManual_)\n      die(9);\n    const { scope_: scope } = state;\n    usePatchesInScope(scope, patchListener);\n    return processResult(void 0, scope);\n  }\n  /**\n   * Pass true to automatically freeze all copies created by Immer.\n   *\n   * By default, auto-freezing is enabled.\n   */\n  setAutoFreeze(value) {\n    this.autoFreeze_ = value;\n  }\n  /**\n   * Pass true to enable strict shallow copy.\n   *\n   * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n   */\n  setUseStrictShallowCopy(value) {\n    this.useStrictShallowCopy_ = value;\n  }\n  applyPatches(base, patches) {\n    let i;\n    for (i = patches.length - 1; i >= 0; i--) {\n      const patch = patches[i];\n      if (patch.path.length === 0 && patch.op === \"replace\") {\n        base = patch.value;\n        break;\n      }\n    }\n    if (i > -1) {\n      patches = patches.slice(i + 1);\n    }\n    const applyPatchesImpl = getPlugin(\"Patches\").applyPatches_;\n    if (isDraft(base)) {\n      return applyPatchesImpl(base, patches);\n    }\n    return this.produce(\n      base,\n      (draft) => applyPatchesImpl(draft, patches)\n    );\n  }\n};\nfunction createProxy(value, parent) {\n  const draft = isMap(value) ? getPlugin(\"MapSet\").proxyMap_(value, parent) : isSet(value) ? getPlugin(\"MapSet\").proxySet_(value, parent) : createProxyProxy(value, parent);\n  const scope = parent ? parent.scope_ : getCurrentScope();\n  scope.drafts_.push(draft);\n  return draft;\n}\n\n// src/core/current.ts\nfunction current(value) {\n  if (!isDraft(value))\n    die(10, value);\n  return currentImpl(value);\n}\nfunction currentImpl(value) {\n  if (!isDraftable(value) || isFrozen(value))\n    return value;\n  const state = value[DRAFT_STATE];\n  let copy;\n  if (state) {\n    if (!state.modified_)\n      return state.base_;\n    state.finalized_ = true;\n    copy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_);\n  } else {\n    copy = shallowCopy(value, true);\n  }\n  each(copy, (key, childValue) => {\n    set(copy, key, currentImpl(childValue));\n  });\n  if (state) {\n    state.finalized_ = false;\n  }\n  return copy;\n}\n\n// src/plugins/patches.ts\nfunction enablePatches() {\n  const errorOffset = 16;\n  if (process.env.NODE_ENV !== \"production\") {\n    errors.push(\n      'Sets cannot have \"replace\" patches.',\n      function(op) {\n        return \"Unsupported patch operation: \" + op;\n      },\n      function(path) {\n        return \"Cannot apply patch, path doesn't resolve: \" + path;\n      },\n      \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n    );\n  }\n  const REPLACE = \"replace\";\n  const ADD = \"add\";\n  const REMOVE = \"remove\";\n  function generatePatches_(state, basePath, patches, inversePatches) {\n    switch (state.type_) {\n      case 0 /* Object */:\n      case 2 /* Map */:\n        return generatePatchesFromAssigned(\n          state,\n          basePath,\n          patches,\n          inversePatches\n        );\n      case 1 /* Array */:\n        return generateArrayPatches(state, basePath, patches, inversePatches);\n      case 3 /* Set */:\n        return generateSetPatches(\n          state,\n          basePath,\n          patches,\n          inversePatches\n        );\n    }\n  }\n  function generateArrayPatches(state, basePath, patches, inversePatches) {\n    let { base_, assigned_ } = state;\n    let copy_ = state.copy_;\n    if (copy_.length < base_.length) {\n      ;\n      [base_, copy_] = [copy_, base_];\n      [patches, inversePatches] = [inversePatches, patches];\n    }\n    for (let i = 0; i < base_.length; i++) {\n      if (assigned_[i] && copy_[i] !== base_[i]) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: REPLACE,\n          path,\n          // Need to maybe clone it, as it can in fact be the original value\n          // due to the base/copy inversion at the start of this function\n          value: clonePatchValueIfNeeded(copy_[i])\n        });\n        inversePatches.push({\n          op: REPLACE,\n          path,\n          value: clonePatchValueIfNeeded(base_[i])\n        });\n      }\n    }\n    for (let i = base_.length; i < copy_.length; i++) {\n      const path = basePath.concat([i]);\n      patches.push({\n        op: ADD,\n        path,\n        // Need to maybe clone it, as it can in fact be the original value\n        // due to the base/copy inversion at the start of this function\n        value: clonePatchValueIfNeeded(copy_[i])\n      });\n    }\n    for (let i = copy_.length - 1; base_.length <= i; --i) {\n      const path = basePath.concat([i]);\n      inversePatches.push({\n        op: REMOVE,\n        path\n      });\n    }\n  }\n  function generatePatchesFromAssigned(state, basePath, patches, inversePatches) {\n    const { base_, copy_ } = state;\n    each(state.assigned_, (key, assignedValue) => {\n      const origValue = get(base_, key);\n      const value = get(copy_, key);\n      const op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD;\n      if (origValue === value && op === REPLACE)\n        return;\n      const path = basePath.concat(key);\n      patches.push(op === REMOVE ? { op, path } : { op, path, value });\n      inversePatches.push(\n        op === ADD ? { op: REMOVE, path } : op === REMOVE ? { op: ADD, path, value: clonePatchValueIfNeeded(origValue) } : { op: REPLACE, path, value: clonePatchValueIfNeeded(origValue) }\n      );\n    });\n  }\n  function generateSetPatches(state, basePath, patches, inversePatches) {\n    let { base_, copy_ } = state;\n    let i = 0;\n    base_.forEach((value) => {\n      if (!copy_.has(value)) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: REMOVE,\n          path,\n          value\n        });\n        inversePatches.unshift({\n          op: ADD,\n          path,\n          value\n        });\n      }\n      i++;\n    });\n    i = 0;\n    copy_.forEach((value) => {\n      if (!base_.has(value)) {\n        const path = basePath.concat([i]);\n        patches.push({\n          op: ADD,\n          path,\n          value\n        });\n        inversePatches.unshift({\n          op: REMOVE,\n          path,\n          value\n        });\n      }\n      i++;\n    });\n  }\n  function generateReplacementPatches_(baseValue, replacement, patches, inversePatches) {\n    patches.push({\n      op: REPLACE,\n      path: [],\n      value: replacement === NOTHING ? void 0 : replacement\n    });\n    inversePatches.push({\n      op: REPLACE,\n      path: [],\n      value: baseValue\n    });\n  }\n  function applyPatches_(draft, patches) {\n    patches.forEach((patch) => {\n      const { path, op } = patch;\n      let base = draft;\n      for (let i = 0; i < path.length - 1; i++) {\n        const parentType = getArchtype(base);\n        let p = path[i];\n        if (typeof p !== \"string\" && typeof p !== \"number\") {\n          p = \"\" + p;\n        }\n        if ((parentType === 0 /* Object */ || parentType === 1 /* Array */) && (p === \"__proto__\" || p === \"constructor\"))\n          die(errorOffset + 3);\n        if (typeof base === \"function\" && p === \"prototype\")\n          die(errorOffset + 3);\n        base = get(base, p);\n        if (typeof base !== \"object\")\n          die(errorOffset + 2, path.join(\"/\"));\n      }\n      const type = getArchtype(base);\n      const value = deepClonePatchValue(patch.value);\n      const key = path[path.length - 1];\n      switch (op) {\n        case REPLACE:\n          switch (type) {\n            case 2 /* Map */:\n              return base.set(key, value);\n            case 3 /* Set */:\n              die(errorOffset);\n            default:\n              return base[key] = value;\n          }\n        case ADD:\n          switch (type) {\n            case 1 /* Array */:\n              return key === \"-\" ? base.push(value) : base.splice(key, 0, value);\n            case 2 /* Map */:\n              return base.set(key, value);\n            case 3 /* Set */:\n              return base.add(value);\n            default:\n              return base[key] = value;\n          }\n        case REMOVE:\n          switch (type) {\n            case 1 /* Array */:\n              return base.splice(key, 1);\n            case 2 /* Map */:\n              return base.delete(key);\n            case 3 /* Set */:\n              return base.delete(patch.value);\n            default:\n              return delete base[key];\n          }\n        default:\n          die(errorOffset + 1, op);\n      }\n    });\n    return draft;\n  }\n  function deepClonePatchValue(obj) {\n    if (!isDraftable(obj))\n      return obj;\n    if (Array.isArray(obj))\n      return obj.map(deepClonePatchValue);\n    if (isMap(obj))\n      return new Map(\n        Array.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n      );\n    if (isSet(obj))\n      return new Set(Array.from(obj).map(deepClonePatchValue));\n    const cloned = Object.create(getPrototypeOf(obj));\n    for (const key in obj)\n      cloned[key] = deepClonePatchValue(obj[key]);\n    if (has(obj, DRAFTABLE))\n      cloned[DRAFTABLE] = obj[DRAFTABLE];\n    return cloned;\n  }\n  function clonePatchValueIfNeeded(obj) {\n    if (isDraft(obj)) {\n      return deepClonePatchValue(obj);\n    } else\n      return obj;\n  }\n  loadPlugin(\"Patches\", {\n    applyPatches_,\n    generatePatches_,\n    generateReplacementPatches_\n  });\n}\n\n// src/plugins/mapset.ts\nfunction enableMapSet() {\n  class DraftMap extends Map {\n    constructor(target, parent) {\n      super();\n      this[DRAFT_STATE] = {\n        type_: 2 /* Map */,\n        parent_: parent,\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        modified_: false,\n        finalized_: false,\n        copy_: void 0,\n        assigned_: void 0,\n        base_: target,\n        draft_: this,\n        isManual_: false,\n        revoked_: false\n      };\n    }\n    get size() {\n      return latest(this[DRAFT_STATE]).size;\n    }\n    has(key) {\n      return latest(this[DRAFT_STATE]).has(key);\n    }\n    set(key, value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!latest(state).has(key) || latest(state).get(key) !== value) {\n        prepareMapCopy(state);\n        markChanged(state);\n        state.assigned_.set(key, true);\n        state.copy_.set(key, value);\n        state.assigned_.set(key, true);\n      }\n      return this;\n    }\n    delete(key) {\n      if (!this.has(key)) {\n        return false;\n      }\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareMapCopy(state);\n      markChanged(state);\n      if (state.base_.has(key)) {\n        state.assigned_.set(key, false);\n      } else {\n        state.assigned_.delete(key);\n      }\n      state.copy_.delete(key);\n      return true;\n    }\n    clear() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (latest(state).size) {\n        prepareMapCopy(state);\n        markChanged(state);\n        state.assigned_ = /* @__PURE__ */ new Map();\n        each(state.base_, (key) => {\n          state.assigned_.set(key, false);\n        });\n        state.copy_.clear();\n      }\n    }\n    forEach(cb, thisArg) {\n      const state = this[DRAFT_STATE];\n      latest(state).forEach((_value, key, _map) => {\n        cb.call(thisArg, this.get(key), key, this);\n      });\n    }\n    get(key) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      const value = latest(state).get(key);\n      if (state.finalized_ || !isDraftable(value)) {\n        return value;\n      }\n      if (value !== state.base_.get(key)) {\n        return value;\n      }\n      const draft = createProxy(value, state);\n      prepareMapCopy(state);\n      state.copy_.set(key, draft);\n      return draft;\n    }\n    keys() {\n      return latest(this[DRAFT_STATE]).keys();\n    }\n    values() {\n      const iterator = this.keys();\n      return {\n        [Symbol.iterator]: () => this.values(),\n        next: () => {\n          const r = iterator.next();\n          if (r.done)\n            return r;\n          const value = this.get(r.value);\n          return {\n            done: false,\n            value\n          };\n        }\n      };\n    }\n    entries() {\n      const iterator = this.keys();\n      return {\n        [Symbol.iterator]: () => this.entries(),\n        next: () => {\n          const r = iterator.next();\n          if (r.done)\n            return r;\n          const value = this.get(r.value);\n          return {\n            done: false,\n            value: [r.value, value]\n          };\n        }\n      };\n    }\n    [(DRAFT_STATE, Symbol.iterator)]() {\n      return this.entries();\n    }\n  }\n  function proxyMap_(target, parent) {\n    return new DraftMap(target, parent);\n  }\n  function prepareMapCopy(state) {\n    if (!state.copy_) {\n      state.assigned_ = /* @__PURE__ */ new Map();\n      state.copy_ = new Map(state.base_);\n    }\n  }\n  class DraftSet extends Set {\n    constructor(target, parent) {\n      super();\n      this[DRAFT_STATE] = {\n        type_: 3 /* Set */,\n        parent_: parent,\n        scope_: parent ? parent.scope_ : getCurrentScope(),\n        modified_: false,\n        finalized_: false,\n        copy_: void 0,\n        base_: target,\n        draft_: this,\n        drafts_: /* @__PURE__ */ new Map(),\n        revoked_: false,\n        isManual_: false\n      };\n    }\n    get size() {\n      return latest(this[DRAFT_STATE]).size;\n    }\n    has(value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!state.copy_) {\n        return state.base_.has(value);\n      }\n      if (state.copy_.has(value))\n        return true;\n      if (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n        return true;\n      return false;\n    }\n    add(value) {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (!this.has(value)) {\n        prepareSetCopy(state);\n        markChanged(state);\n        state.copy_.add(value);\n      }\n      return this;\n    }\n    delete(value) {\n      if (!this.has(value)) {\n        return false;\n      }\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      markChanged(state);\n      return state.copy_.delete(value) || (state.drafts_.has(value) ? state.copy_.delete(state.drafts_.get(value)) : (\n        /* istanbul ignore next */\n        false\n      ));\n    }\n    clear() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      if (latest(state).size) {\n        prepareSetCopy(state);\n        markChanged(state);\n        state.copy_.clear();\n      }\n    }\n    values() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      return state.copy_.values();\n    }\n    entries() {\n      const state = this[DRAFT_STATE];\n      assertUnrevoked(state);\n      prepareSetCopy(state);\n      return state.copy_.entries();\n    }\n    keys() {\n      return this.values();\n    }\n    [(DRAFT_STATE, Symbol.iterator)]() {\n      return this.values();\n    }\n    forEach(cb, thisArg) {\n      const iterator = this.values();\n      let result = iterator.next();\n      while (!result.done) {\n        cb.call(thisArg, result.value, result.value, this);\n        result = iterator.next();\n      }\n    }\n  }\n  function proxySet_(target, parent) {\n    return new DraftSet(target, parent);\n  }\n  function prepareSetCopy(state) {\n    if (!state.copy_) {\n      state.copy_ = /* @__PURE__ */ new Set();\n      state.base_.forEach((value) => {\n        if (isDraftable(value)) {\n          const draft = createProxy(value, state);\n          state.drafts_.set(value, draft);\n          state.copy_.add(draft);\n        } else {\n          state.copy_.add(value);\n        }\n      });\n    }\n  }\n  function assertUnrevoked(state) {\n    if (state.revoked_)\n      die(3, JSON.stringify(latest(state)));\n  }\n  loadPlugin(\"MapSet\", { proxyMap_, proxySet_ });\n}\n\n// src/immer.ts\nvar immer = new Immer2();\nvar produce = immer.produce;\nvar produceWithPatches = immer.produceWithPatches.bind(\n  immer\n);\nvar setAutoFreeze = immer.setAutoFreeze.bind(immer);\nvar setUseStrictShallowCopy = immer.setUseStrictShallowCopy.bind(immer);\nvar applyPatches = immer.applyPatches.bind(immer);\nvar createDraft = immer.createDraft.bind(immer);\nvar finishDraft = immer.finishDraft.bind(immer);\nfunction castDraft(value) {\n  return value;\n}\nfunction castImmutable(value) {\n  return value;\n}\nexport {\n  Immer2 as Immer,\n  applyPatches,\n  castDraft,\n  castImmutable,\n  createDraft,\n  current,\n  enableMapSet,\n  enablePatches,\n  finishDraft,\n  freeze,\n  DRAFTABLE as immerable,\n  isDraft,\n  isDraftable,\n  NOTHING as nothing,\n  original,\n  produce,\n  produceWithPatches,\n  setAutoFreeze,\n  setUseStrictShallowCopy\n};\n//# sourceMappingURL=immer.mjs.map", "/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** Used as references for various `Number` constants. */\nvar MAX_SAFE_INTEGER = 9007199254740991;\n\n/** `Object#toString` result references. */\nvar argsTag = '[object Arguments]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]';\n\n/** Used to detect unsigned integer values. */\nvar reIsUint = /^(?:0|[1-9]\\d*)$/;\n\n/**\n * A faster alternative to `Function#apply`, this function invokes `func`\n * with the `this` binding of `thisArg` and the arguments of `args`.\n *\n * @private\n * @param {Function} func The function to invoke.\n * @param {*} thisArg The `this` binding of `func`.\n * @param {Array} args The arguments to invoke `func` with.\n * @returns {*} Returns the result of `func`.\n */\nfunction apply(func, thisArg, args) {\n  switch (args.length) {\n    case 0: return func.call(thisArg);\n    case 1: return func.call(thisArg, args[0]);\n    case 2: return func.call(thisArg, args[0], args[1]);\n    case 3: return func.call(thisArg, args[0], args[1], args[2]);\n  }\n  return func.apply(thisArg, args);\n}\n\n/**\n * The base implementation of `_.times` without support for iteratee shorthands\n * or max array length checks.\n *\n * @private\n * @param {number} n The number of times to invoke `iteratee`.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {Array} Returns the array of results.\n */\nfunction baseTimes(n, iteratee) {\n  var index = -1,\n      result = Array(n);\n\n  while (++index < n) {\n    result[index] = iteratee(index);\n  }\n  return result;\n}\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Creates an array of the enumerable property names of the array-like `value`.\n *\n * @private\n * @param {*} value The value to query.\n * @param {boolean} inherited Specify returning inherited property names.\n * @returns {Array} Returns the array of property names.\n */\nfunction arrayLikeKeys(value, inherited) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  // Safari 9 makes `arguments.length` enumerable in strict mode.\n  var result = (isArray(value) || isArguments(value))\n    ? baseTimes(value.length, String)\n    : [];\n\n  var length = result.length,\n      skipIndexes = !!length;\n\n  for (var key in value) {\n    if ((inherited || hasOwnProperty.call(value, key)) &&\n        !(skipIndexes && (key == 'length' || isIndex(key, length)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Assigns `value` to `key` of `object` if the existing value is not equivalent\n * using [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignValue(object, key, value) {\n  var objValue = object[key];\n  if (!(hasOwnProperty.call(object, key) && eq(objValue, value)) ||\n      (value === undefined && !(key in object))) {\n    object[key] = value;\n  }\n}\n\n/**\n * The base implementation of `_.keysIn` which doesn't treat sparse arrays as dense.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction baseKeysIn(object) {\n  if (!isObject(object)) {\n    return nativeKeysIn(object);\n  }\n  var isProto = isPrototype(object),\n      result = [];\n\n  for (var key in object) {\n    if (!(key == 'constructor' && (isProto || !hasOwnProperty.call(object, key)))) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * The base implementation of `_.rest` which doesn't validate or coerce arguments.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @param {number} [start=func.length-1] The start position of the rest parameter.\n * @returns {Function} Returns the new function.\n */\nfunction baseRest(func, start) {\n  start = nativeMax(start === undefined ? (func.length - 1) : start, 0);\n  return function() {\n    var args = arguments,\n        index = -1,\n        length = nativeMax(args.length - start, 0),\n        array = Array(length);\n\n    while (++index < length) {\n      array[index] = args[start + index];\n    }\n    index = -1;\n    var otherArgs = Array(start + 1);\n    while (++index < start) {\n      otherArgs[index] = args[index];\n    }\n    otherArgs[start] = array;\n    return apply(func, this, otherArgs);\n  };\n}\n\n/**\n * Copies properties of `source` to `object`.\n *\n * @private\n * @param {Object} source The object to copy properties from.\n * @param {Array} props The property identifiers to copy.\n * @param {Object} [object={}] The object to copy properties to.\n * @param {Function} [customizer] The function to customize copied values.\n * @returns {Object} Returns `object`.\n */\nfunction copyObject(source, props, object, customizer) {\n  object || (object = {});\n\n  var index = -1,\n      length = props.length;\n\n  while (++index < length) {\n    var key = props[index];\n\n    var newValue = customizer\n      ? customizer(object[key], source[key], key, object, source)\n      : undefined;\n\n    assignValue(object, key, newValue === undefined ? source[key] : newValue);\n  }\n  return object;\n}\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\n/**\n * Checks if `value` is a valid array-like index.\n *\n * @private\n * @param {*} value The value to check.\n * @param {number} [length=MAX_SAFE_INTEGER] The upper bounds of a valid index.\n * @returns {boolean} Returns `true` if `value` is a valid index, else `false`.\n */\nfunction isIndex(value, length) {\n  length = length == null ? MAX_SAFE_INTEGER : length;\n  return !!length &&\n    (typeof value == 'number' || reIsUint.test(value)) &&\n    (value > -1 && value % 1 == 0 && value < length);\n}\n\n/**\n * Checks if the given arguments are from an iteratee call.\n *\n * @private\n * @param {*} value The potential iteratee value argument.\n * @param {*} index The potential iteratee index or key argument.\n * @param {*} object The potential iteratee object argument.\n * @returns {boolean} Returns `true` if the arguments are from an iteratee call,\n *  else `false`.\n */\nfunction isIterateeCall(value, index, object) {\n  if (!isObject(object)) {\n    return false;\n  }\n  var type = typeof index;\n  if (type == 'number'\n        ? (isArrayLike(object) && isIndex(index, object.length))\n        : (type == 'string' && index in object)\n      ) {\n    return eq(object[index], value);\n  }\n  return false;\n}\n\n/**\n * Checks if `value` is likely a prototype object.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a prototype, else `false`.\n */\nfunction isPrototype(value) {\n  var Ctor = value && value.constructor,\n      proto = (typeof Ctor == 'function' && Ctor.prototype) || objectProto;\n\n  return value === proto;\n}\n\n/**\n * This function is like\n * [`Object.keys`](http://ecma-international.org/ecma-262/7.0/#sec-object.keys)\n * except that it includes inherited enumerable properties.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n */\nfunction nativeKeysIn(object) {\n  var result = [];\n  if (object != null) {\n    for (var key in Object(object)) {\n      result.push(key);\n    }\n  }\n  return result;\n}\n\n/**\n * Performs a\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * comparison between two values to determine if they are equivalent.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if the values are equivalent, else `false`.\n * @example\n *\n * var object = { 'a': 1 };\n * var other = { 'a': 1 };\n *\n * _.eq(object, object);\n * // => true\n *\n * _.eq(object, other);\n * // => false\n *\n * _.eq('a', 'a');\n * // => true\n *\n * _.eq('a', Object('a'));\n * // => false\n *\n * _.eq(NaN, NaN);\n * // => true\n */\nfunction eq(value, other) {\n  return value === other || (value !== value && other !== other);\n}\n\n/**\n * Checks if `value` is likely an `arguments` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an `arguments` object,\n *  else `false`.\n * @example\n *\n * _.isArguments(function() { return arguments; }());\n * // => true\n *\n * _.isArguments([1, 2, 3]);\n * // => false\n */\nfunction isArguments(value) {\n  // Safari 8.1 makes `arguments.callee` enumerable in strict mode.\n  return isArrayLikeObject(value) && hasOwnProperty.call(value, 'callee') &&\n    (!propertyIsEnumerable.call(value, 'callee') || objectToString.call(value) == argsTag);\n}\n\n/**\n * Checks if `value` is classified as an `Array` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array, else `false`.\n * @example\n *\n * _.isArray([1, 2, 3]);\n * // => true\n *\n * _.isArray(document.body.children);\n * // => false\n *\n * _.isArray('abc');\n * // => false\n *\n * _.isArray(_.noop);\n * // => false\n */\nvar isArray = Array.isArray;\n\n/**\n * Checks if `value` is array-like. A value is considered array-like if it's\n * not a function and has a `value.length` that's an integer greater than or\n * equal to `0` and less than or equal to `Number.MAX_SAFE_INTEGER`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is array-like, else `false`.\n * @example\n *\n * _.isArrayLike([1, 2, 3]);\n * // => true\n *\n * _.isArrayLike(document.body.children);\n * // => true\n *\n * _.isArrayLike('abc');\n * // => true\n *\n * _.isArrayLike(_.noop);\n * // => false\n */\nfunction isArrayLike(value) {\n  return value != null && isLength(value.length) && !isFunction(value);\n}\n\n/**\n * This method is like `_.isArrayLike` except that it also checks if `value`\n * is an object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array-like object,\n *  else `false`.\n * @example\n *\n * _.isArrayLikeObject([1, 2, 3]);\n * // => true\n *\n * _.isArrayLikeObject(document.body.children);\n * // => true\n *\n * _.isArrayLikeObject('abc');\n * // => false\n *\n * _.isArrayLikeObject(_.noop);\n * // => false\n */\nfunction isArrayLikeObject(value) {\n  return isObjectLike(value) && isArrayLike(value);\n}\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 8-9 which returns 'object' for typed array and other constructors.\n  var tag = isObject(value) ? objectToString.call(value) : '';\n  return tag == funcTag || tag == genTag;\n}\n\n/**\n * Checks if `value` is a valid array-like length.\n *\n * **Note:** This method is loosely based on\n * [`ToLength`](http://ecma-international.org/ecma-262/7.0/#sec-tolength).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a valid length, else `false`.\n * @example\n *\n * _.isLength(3);\n * // => true\n *\n * _.isLength(Number.MIN_VALUE);\n * // => false\n *\n * _.isLength(Infinity);\n * // => false\n *\n * _.isLength('3');\n * // => false\n */\nfunction isLength(value) {\n  return typeof value == 'number' &&\n    value > -1 && value % 1 == 0 && value <= MAX_SAFE_INTEGER;\n}\n\n/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\nfunction isObject(value) {\n  var type = typeof value;\n  return !!value && (type == 'object' || type == 'function');\n}\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * This method is like `_.assign` except that it iterates over own and\n * inherited source properties.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @alias extend\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.assign\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * function Bar() {\n *   this.c = 3;\n * }\n *\n * Foo.prototype.b = 2;\n * Bar.prototype.d = 4;\n *\n * _.assignIn({ 'a': 0 }, new Foo, new Bar);\n * // => { 'a': 1, 'b': 2, 'c': 3, 'd': 4 }\n */\nvar assignIn = createAssigner(function(object, source) {\n  copyObject(source, keysIn(source), object);\n});\n\n/**\n * Creates an array of the own and inherited enumerable property names of `object`.\n *\n * **Note:** Non-object values are coerced to objects.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of property names.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.keysIn(new Foo);\n * // => ['a', 'b', 'c'] (iteration order is not guaranteed)\n */\nfunction keysIn(object) {\n  return isArrayLike(object) ? arrayLikeKeys(object, true) : baseKeysIn(object);\n}\n\nmodule.exports = assignIn;\n", "!function(e,t){\"object\"==typeof exports&&\"undefined\"!=typeof module?t(exports):\"function\"==typeof define&&define.amd?define([\"exports\"],t):t(e.reduxLogger=e.reduxLogger||{})}(this,function(e){\"use strict\";function t(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}function r(e,t){Object.defineProperty(this,\"kind\",{value:e,enumerable:!0}),t&&t.length&&Object.defineProperty(this,\"path\",{value:t,enumerable:!0})}function n(e,t,r){n.super_.call(this,\"E\",e),Object.defineProperty(this,\"lhs\",{value:t,enumerable:!0}),Object.defineProperty(this,\"rhs\",{value:r,enumerable:!0})}function o(e,t){o.super_.call(this,\"N\",e),Object.defineProperty(this,\"rhs\",{value:t,enumerable:!0})}function i(e,t){i.super_.call(this,\"D\",e),Object.defineProperty(this,\"lhs\",{value:t,enumerable:!0})}function a(e,t,r){a.super_.call(this,\"A\",e),Object.defineProperty(this,\"index\",{value:t,enumerable:!0}),Object.defineProperty(this,\"item\",{value:r,enumerable:!0})}function f(e,t,r){var n=e.slice((r||t)+1||e.length);return e.length=t<0?e.length+t:t,e.push.apply(e,n),e}function u(e){var t=\"undefined\"==typeof e?\"undefined\":N(e);return\"object\"!==t?t:e===Math?\"math\":null===e?\"null\":Array.isArray(e)?\"array\":\"[object Date]\"===Object.prototype.toString.call(e)?\"date\":\"function\"==typeof e.toString&&/^\\/.*\\//.test(e.toString())?\"regexp\":\"object\"}function l(e,t,r,c,s,d,p){s=s||[],p=p||[];var g=s.slice(0);if(\"undefined\"!=typeof d){if(c){if(\"function\"==typeof c&&c(g,d))return;if(\"object\"===(\"undefined\"==typeof c?\"undefined\":N(c))){if(c.prefilter&&c.prefilter(g,d))return;if(c.normalize){var h=c.normalize(g,d,e,t);h&&(e=h[0],t=h[1])}}}g.push(d)}\"regexp\"===u(e)&&\"regexp\"===u(t)&&(e=e.toString(),t=t.toString());var y=\"undefined\"==typeof e?\"undefined\":N(e),v=\"undefined\"==typeof t?\"undefined\":N(t),b=\"undefined\"!==y||p&&p[p.length-1].lhs&&p[p.length-1].lhs.hasOwnProperty(d),m=\"undefined\"!==v||p&&p[p.length-1].rhs&&p[p.length-1].rhs.hasOwnProperty(d);if(!b&&m)r(new o(g,t));else if(!m&&b)r(new i(g,e));else if(u(e)!==u(t))r(new n(g,e,t));else if(\"date\"===u(e)&&e-t!==0)r(new n(g,e,t));else if(\"object\"===y&&null!==e&&null!==t)if(p.filter(function(t){return t.lhs===e}).length)e!==t&&r(new n(g,e,t));else{if(p.push({lhs:e,rhs:t}),Array.isArray(e)){var w;e.length;for(w=0;w<e.length;w++)w>=t.length?r(new a(g,w,new i(void 0,e[w]))):l(e[w],t[w],r,c,g,w,p);for(;w<t.length;)r(new a(g,w,new o(void 0,t[w++])))}else{var x=Object.keys(e),S=Object.keys(t);x.forEach(function(n,o){var i=S.indexOf(n);i>=0?(l(e[n],t[n],r,c,g,n,p),S=f(S,i)):l(e[n],void 0,r,c,g,n,p)}),S.forEach(function(e){l(void 0,t[e],r,c,g,e,p)})}p.length=p.length-1}else e!==t&&(\"number\"===y&&isNaN(e)&&isNaN(t)||r(new n(g,e,t)))}function c(e,t,r,n){return n=n||[],l(e,t,function(e){e&&n.push(e)},r),n.length?n:void 0}function s(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case\"A\":s(o[r.path[n]],r.index,r.item);break;case\"D\":delete o[r.path[n]];break;case\"E\":case\"N\":o[r.path[n]]=r.rhs}}else switch(r.kind){case\"A\":s(e[t],r.index,r.item);break;case\"D\":e=f(e,t);break;case\"E\":case\"N\":e[t]=r.rhs}return e}function d(e,t,r){if(e&&t&&r&&r.kind){for(var n=e,o=-1,i=r.path?r.path.length-1:0;++o<i;)\"undefined\"==typeof n[r.path[o]]&&(n[r.path[o]]=\"number\"==typeof r.path[o]?[]:{}),n=n[r.path[o]];switch(r.kind){case\"A\":s(r.path?n[r.path[o]]:n,r.index,r.item);break;case\"D\":delete n[r.path[o]];break;case\"E\":case\"N\":n[r.path[o]]=r.rhs}}}function p(e,t,r){if(r.path&&r.path.length){var n,o=e[t],i=r.path.length-1;for(n=0;n<i;n++)o=o[r.path[n]];switch(r.kind){case\"A\":p(o[r.path[n]],r.index,r.item);break;case\"D\":o[r.path[n]]=r.lhs;break;case\"E\":o[r.path[n]]=r.lhs;break;case\"N\":delete o[r.path[n]]}}else switch(r.kind){case\"A\":p(e[t],r.index,r.item);break;case\"D\":e[t]=r.lhs;break;case\"E\":e[t]=r.lhs;break;case\"N\":e=f(e,t)}return e}function g(e,t,r){if(e&&t&&r&&r.kind){var n,o,i=e;for(o=r.path.length-1,n=0;n<o;n++)\"undefined\"==typeof i[r.path[n]]&&(i[r.path[n]]={}),i=i[r.path[n]];switch(r.kind){case\"A\":p(i[r.path[n]],r.index,r.item);break;case\"D\":i[r.path[n]]=r.lhs;break;case\"E\":i[r.path[n]]=r.lhs;break;case\"N\":delete i[r.path[n]]}}}function h(e,t,r){if(e&&t){var n=function(n){r&&!r(e,t,n)||d(e,t,n)};l(e,t,n)}}function y(e){return\"color: \"+F[e].color+\"; font-weight: bold\"}function v(e){var t=e.kind,r=e.path,n=e.lhs,o=e.rhs,i=e.index,a=e.item;switch(t){case\"E\":return[r.join(\".\"),n,\"→\",o];case\"N\":return[r.join(\".\"),o];case\"D\":return[r.join(\".\")];case\"A\":return[r.join(\".\")+\"[\"+i+\"]\",a];default:return[]}}function b(e,t,r,n){var o=c(e,t);try{n?r.groupCollapsed(\"diff\"):r.group(\"diff\")}catch(e){r.log(\"diff\")}o?o.forEach(function(e){var t=e.kind,n=v(e);r.log.apply(r,[\"%c \"+F[t].text,y(t)].concat(P(n)))}):r.log(\"—— no diff ——\");try{r.groupEnd()}catch(e){r.log(\"—— diff end —— \")}}function m(e,t,r,n){switch(\"undefined\"==typeof e?\"undefined\":N(e)){case\"object\":return\"function\"==typeof e[n]?e[n].apply(e,P(r)):e[n];case\"function\":return e(t);default:return e}}function w(e){var t=e.timestamp,r=e.duration;return function(e,n,o){var i=[\"action\"];return i.push(\"%c\"+String(e.type)),t&&i.push(\"%c@ \"+n),r&&i.push(\"%c(in \"+o.toFixed(2)+\" ms)\"),i.join(\" \")}}function x(e,t){var r=t.logger,n=t.actionTransformer,o=t.titleFormatter,i=void 0===o?w(t):o,a=t.collapsed,f=t.colors,u=t.level,l=t.diff,c=\"undefined\"==typeof t.titleFormatter;e.forEach(function(o,s){var d=o.started,p=o.startedTime,g=o.action,h=o.prevState,y=o.error,v=o.took,w=o.nextState,x=e[s+1];x&&(w=x.prevState,v=x.started-d);var S=n(g),k=\"function\"==typeof a?a(function(){return w},g,o):a,j=D(p),E=f.title?\"color: \"+f.title(S)+\";\":\"\",A=[\"color: gray; font-weight: lighter;\"];A.push(E),t.timestamp&&A.push(\"color: gray; font-weight: lighter;\"),t.duration&&A.push(\"color: gray; font-weight: lighter;\");var O=i(S,j,v);try{k?f.title&&c?r.groupCollapsed.apply(r,[\"%c \"+O].concat(A)):r.groupCollapsed(O):f.title&&c?r.group.apply(r,[\"%c \"+O].concat(A)):r.group(O)}catch(e){r.log(O)}var N=m(u,S,[h],\"prevState\"),P=m(u,S,[S],\"action\"),C=m(u,S,[y,h],\"error\"),F=m(u,S,[w],\"nextState\");if(N)if(f.prevState){var L=\"color: \"+f.prevState(h)+\"; font-weight: bold\";r[N](\"%c prev state\",L,h)}else r[N](\"prev state\",h);if(P)if(f.action){var T=\"color: \"+f.action(S)+\"; font-weight: bold\";r[P](\"%c action    \",T,S)}else r[P](\"action    \",S);if(y&&C)if(f.error){var M=\"color: \"+f.error(y,h)+\"; font-weight: bold;\";r[C](\"%c error     \",M,y)}else r[C](\"error     \",y);if(F)if(f.nextState){var _=\"color: \"+f.nextState(w)+\"; font-weight: bold\";r[F](\"%c next state\",_,w)}else r[F](\"next state\",w);l&&b(h,w,r,k);try{r.groupEnd()}catch(e){r.log(\"—— log end ——\")}})}function S(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=Object.assign({},L,e),r=t.logger,n=t.stateTransformer,o=t.errorTransformer,i=t.predicate,a=t.logErrors,f=t.diffPredicate;if(\"undefined\"==typeof r)return function(){return function(e){return function(t){return e(t)}}};if(e.getState&&e.dispatch)return console.error(\"[redux-logger] redux-logger not installed. Make sure to pass logger instance as middleware:\\n// Logger with default options\\nimport { logger } from 'redux-logger'\\nconst store = createStore(\\n  reducer,\\n  applyMiddleware(logger)\\n)\\n// Or you can create your own logger with custom options http://bit.ly/redux-logger-options\\nimport createLogger from 'redux-logger'\\nconst logger = createLogger({\\n  // ...options\\n});\\nconst store = createStore(\\n  reducer,\\n  applyMiddleware(logger)\\n)\\n\"),function(){return function(e){return function(t){return e(t)}}};var u=[];return function(e){var r=e.getState;return function(e){return function(l){if(\"function\"==typeof i&&!i(r,l))return e(l);var c={};u.push(c),c.started=O.now(),c.startedTime=new Date,c.prevState=n(r()),c.action=l;var s=void 0;if(a)try{s=e(l)}catch(e){c.error=o(e)}else s=e(l);c.took=O.now()-c.started,c.nextState=n(r());var d=t.diff&&\"function\"==typeof f?f(r,l):t.diff;if(x(u,Object.assign({},t,{diff:d})),u.length=0,c.error)throw c.error;return s}}}}var k,j,E=function(e,t){return new Array(t+1).join(e)},A=function(e,t){return E(\"0\",t-e.toString().length)+e},D=function(e){return A(e.getHours(),2)+\":\"+A(e.getMinutes(),2)+\":\"+A(e.getSeconds(),2)+\".\"+A(e.getMilliseconds(),3)},O=\"undefined\"!=typeof performance&&null!==performance&&\"function\"==typeof performance.now?performance:Date,N=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\"function\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\"symbol\":typeof e},P=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)},C=[];k=\"object\"===(\"undefined\"==typeof global?\"undefined\":N(global))&&global?global:\"undefined\"!=typeof window?window:{},j=k.DeepDiff,j&&C.push(function(){\"undefined\"!=typeof j&&k.DeepDiff===c&&(k.DeepDiff=j,j=void 0)}),t(n,r),t(o,r),t(i,r),t(a,r),Object.defineProperties(c,{diff:{value:c,enumerable:!0},observableDiff:{value:l,enumerable:!0},applyDiff:{value:h,enumerable:!0},applyChange:{value:d,enumerable:!0},revertChange:{value:g,enumerable:!0},isConflict:{value:function(){return\"undefined\"!=typeof j},enumerable:!0},noConflict:{value:function(){return C&&(C.forEach(function(e){e()}),C=null),c},enumerable:!0}});var F={E:{color:\"#2196F3\",text:\"CHANGED:\"},N:{color:\"#4CAF50\",text:\"ADDED:\"},D:{color:\"#F44336\",text:\"DELETED:\"},A:{color:\"#2196F3\",text:\"ARRAY:\"}},L={level:\"log\",logger:console,logErrors:!0,collapsed:void 0,predicate:void 0,duration:!1,timestamp:!0,stateTransformer:function(e){return e},actionTransformer:function(e){return e},errorTransformer:function(e){return e},colors:{title:function(){return\"inherit\"},prevState:function(){return\"#9E9E9E\"},action:function(){return\"#03A9F4\"},nextState:function(){return\"#4CAF50\"},error:function(){return\"#F20404\"}},diff:!1,diffPredicate:void 0,transformer:void 0},T=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.dispatch,r=e.getState;return\"function\"==typeof t||\"function\"==typeof r?S()({dispatch:t,getState:r}):void console.error(\"\\n[redux-logger v3] BREAKING CHANGE\\n[redux-logger v3] Since 3.0.0 redux-logger exports by default logger with default settings.\\n[redux-logger v3] Change\\n[redux-logger v3] import createLogger from 'redux-logger'\\n[redux-logger v3] to\\n[redux-logger v3] import { createLogger } from 'redux-logger'\\n\")};e.defaults=L,e.createLogger=S,e.logger=T,e.default=T,Object.defineProperty(e,\"__esModule\",{value:!0})});\n", "// src/index.ts\nfunction createThunkMiddleware(extraArgument) {\n  const middleware = ({ dispatch, getState }) => (next) => (action) => {\n    if (typeof action === \"function\") {\n      return action(dispatch, getState, extraArgument);\n    }\n    return next(action);\n  };\n  return middleware;\n}\nvar thunk = createThunkMiddleware();\nvar withExtraArgument = createThunkMiddleware;\nexport {\n  thunk,\n  withExtraArgument\n};\n", "// src/utils/formatProdErrorMessage.ts\nfunction formatProdErrorMessage(code) {\n  return `Minified Redux error #${code}; visit https://redux.js.org/Errors?code=${code} for the full message or use the non-minified dev environment for full errors. `;\n}\n\n// src/utils/symbol-observable.ts\nvar $$observable = /* @__PURE__ */ (() => typeof Symbol === \"function\" && Symbol.observable || \"@@observable\")();\nvar symbol_observable_default = $$observable;\n\n// src/utils/actionTypes.ts\nvar randomString = () => Math.random().toString(36).substring(7).split(\"\").join(\".\");\nvar ActionTypes = {\n  INIT: `@@redux/INIT${/* @__PURE__ */ randomString()}`,\n  REPLACE: `@@redux/REPLACE${/* @__PURE__ */ randomString()}`,\n  PROBE_UNKNOWN_ACTION: () => `@@redux/PROBE_UNKNOWN_ACTION${randomString()}`\n};\nvar actionTypes_default = ActionTypes;\n\n// src/utils/isPlainObject.ts\nfunction isPlainObject(obj) {\n  if (typeof obj !== \"object\" || obj === null)\n    return false;\n  let proto = obj;\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n  return Object.getPrototypeOf(obj) === proto || Object.getPrototypeOf(obj) === null;\n}\n\n// src/utils/kindOf.ts\nfunction miniKindOf(val) {\n  if (val === void 0)\n    return \"undefined\";\n  if (val === null)\n    return \"null\";\n  const type = typeof val;\n  switch (type) {\n    case \"boolean\":\n    case \"string\":\n    case \"number\":\n    case \"symbol\":\n    case \"function\": {\n      return type;\n    }\n  }\n  if (Array.isArray(val))\n    return \"array\";\n  if (isDate(val))\n    return \"date\";\n  if (isError(val))\n    return \"error\";\n  const constructorName = ctorName(val);\n  switch (constructorName) {\n    case \"Symbol\":\n    case \"Promise\":\n    case \"WeakMap\":\n    case \"WeakSet\":\n    case \"Map\":\n    case \"Set\":\n      return constructorName;\n  }\n  return Object.prototype.toString.call(val).slice(8, -1).toLowerCase().replace(/\\s/g, \"\");\n}\nfunction ctorName(val) {\n  return typeof val.constructor === \"function\" ? val.constructor.name : null;\n}\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === \"string\" && val.constructor && typeof val.constructor.stackTraceLimit === \"number\";\n}\nfunction isDate(val) {\n  if (val instanceof Date)\n    return true;\n  return typeof val.toDateString === \"function\" && typeof val.getDate === \"function\" && typeof val.setDate === \"function\";\n}\nfunction kindOf(val) {\n  let typeOfVal = typeof val;\n  if (process.env.NODE_ENV !== \"production\") {\n    typeOfVal = miniKindOf(val);\n  }\n  return typeOfVal;\n}\n\n// src/createStore.ts\nfunction createStore(reducer, preloadedState, enhancer) {\n  if (typeof reducer !== \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : `Expected the root reducer to be a function. Instead, received: '${kindOf(reducer)}'`);\n  }\n  if (typeof preloadedState === \"function\" && typeof enhancer === \"function\" || typeof enhancer === \"function\" && typeof arguments[3] === \"function\") {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : \"It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.\");\n  }\n  if (typeof preloadedState === \"function\" && typeof enhancer === \"undefined\") {\n    enhancer = preloadedState;\n    preloadedState = void 0;\n  }\n  if (typeof enhancer !== \"undefined\") {\n    if (typeof enhancer !== \"function\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : `Expected the enhancer to be a function. Instead, received: '${kindOf(enhancer)}'`);\n    }\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n  let currentReducer = reducer;\n  let currentState = preloadedState;\n  let currentListeners = /* @__PURE__ */ new Map();\n  let nextListeners = currentListeners;\n  let listenerIdCounter = 0;\n  let isDispatching = false;\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = /* @__PURE__ */ new Map();\n      currentListeners.forEach((listener, key) => {\n        nextListeners.set(key, listener);\n      });\n    }\n  }\n  function getState() {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : \"You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.\");\n    }\n    return currentState;\n  }\n  function subscribe(listener) {\n    if (typeof listener !== \"function\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : `Expected the listener to be a function. Instead, received: '${kindOf(listener)}'`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : \"You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.\");\n    }\n    let isSubscribed = true;\n    ensureCanMutateNextListeners();\n    const listenerId = listenerIdCounter++;\n    nextListeners.set(listenerId, listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : \"You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.\");\n      }\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      nextListeners.delete(listenerId);\n      currentListeners = null;\n    };\n  }\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : `Actions must be plain objects. Instead, the actual type was: '${kindOf(action)}'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.`);\n    }\n    if (typeof action.type === \"undefined\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n    if (typeof action.type !== \"string\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(17) : `Action \"type\" property must be a string. Instead, the actual type was: '${kindOf(action.type)}'. Value was: '${action.type}' (stringified)`);\n    }\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(9) : \"Reducers may not dispatch actions.\");\n    }\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n    const listeners = currentListeners = nextListeners;\n    listeners.forEach((listener) => {\n      listener();\n    });\n    return action;\n  }\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== \"function\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(10) : `Expected the nextReducer to be a function. Instead, received: '${kindOf(nextReducer)}`);\n    }\n    currentReducer = nextReducer;\n    dispatch({\n      type: actionTypes_default.REPLACE\n    });\n  }\n  function observable() {\n    const outerSubscribe = subscribe;\n    return {\n      /**\n       * The minimal observable subscription method.\n       * @param observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer) {\n        if (typeof observer !== \"object\" || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : `Expected the observer to be an object. Instead, received: '${kindOf(observer)}'`);\n        }\n        function observeState() {\n          const observerAsObserver = observer;\n          if (observerAsObserver.next) {\n            observerAsObserver.next(getState());\n          }\n        }\n        observeState();\n        const unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe\n        };\n      },\n      [symbol_observable_default]() {\n        return this;\n      }\n    };\n  }\n  dispatch({\n    type: actionTypes_default.INIT\n  });\n  const store = {\n    dispatch,\n    subscribe,\n    getState,\n    replaceReducer,\n    [symbol_observable_default]: observable\n  };\n  return store;\n}\nfunction legacy_createStore(reducer, preloadedState, enhancer) {\n  return createStore(reducer, preloadedState, enhancer);\n}\n\n// src/utils/warning.ts\nfunction warning(message) {\n  if (typeof console !== \"undefined\" && typeof console.error === \"function\") {\n    console.error(message);\n  }\n  try {\n    throw new Error(message);\n  } catch (e) {\n  }\n}\n\n// src/combineReducers.ts\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  const reducerKeys = Object.keys(reducers);\n  const argumentName = action && action.type === actionTypes_default.INIT ? \"preloadedState argument passed to createStore\" : \"previous state received by the reducer\";\n  if (reducerKeys.length === 0) {\n    return \"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.\";\n  }\n  if (!isPlainObject(inputState)) {\n    return `The ${argumentName} has unexpected type of \"${kindOf(inputState)}\". Expected argument to be an object with the following keys: \"${reducerKeys.join('\", \"')}\"`;\n  }\n  const unexpectedKeys = Object.keys(inputState).filter((key) => !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]);\n  unexpectedKeys.forEach((key) => {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === actionTypes_default.REPLACE)\n    return;\n  if (unexpectedKeys.length > 0) {\n    return `Unexpected ${unexpectedKeys.length > 1 ? \"keys\" : \"key\"} \"${unexpectedKeys.join('\", \"')}\" found in ${argumentName}. Expected to find one of the known reducer keys instead: \"${reducerKeys.join('\", \"')}\". Unexpected keys will be ignored.`;\n  }\n}\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach((key) => {\n    const reducer = reducers[key];\n    const initialState = reducer(void 0, {\n      type: actionTypes_default.INIT\n    });\n    if (typeof initialState === \"undefined\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : `The slice reducer for key \"${key}\" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);\n    }\n    if (typeof reducer(void 0, {\n      type: actionTypes_default.PROBE_UNKNOWN_ACTION()\n    }) === \"undefined\") {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : `The slice reducer for key \"${key}\" returned undefined when probed with a random type. Don't try to handle '${actionTypes_default.INIT}' or other actions in \"redux/*\" namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.`);\n    }\n  });\n}\nfunction combineReducers(reducers) {\n  const reducerKeys = Object.keys(reducers);\n  const finalReducers = {};\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i];\n    if (process.env.NODE_ENV !== \"production\") {\n      if (typeof reducers[key] === \"undefined\") {\n        warning(`No reducer provided for key \"${key}\"`);\n      }\n    }\n    if (typeof reducers[key] === \"function\") {\n      finalReducers[key] = reducers[key];\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers);\n  let unexpectedKeyCache;\n  if (process.env.NODE_ENV !== \"production\") {\n    unexpectedKeyCache = {};\n  }\n  let shapeAssertionError;\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n  return function combination(state = {}, action) {\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n    if (process.env.NODE_ENV !== \"production\") {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n    let hasChanged = false;\n    const nextState = {};\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i];\n      const reducer = finalReducers[key];\n      const previousStateForKey = state[key];\n      const nextStateForKey = reducer(previousStateForKey, action);\n      if (typeof nextStateForKey === \"undefined\") {\n        const actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : `When called with an action of type ${actionType ? `\"${String(actionType)}\"` : \"(unknown type)\"}, the slice reducer for key \"${key}\" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.`);\n      }\n      nextState[key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\n// src/bindActionCreators.ts\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function(...args) {\n    return dispatch(actionCreator.apply(this, args));\n  };\n}\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === \"function\") {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n  if (typeof actionCreators !== \"object\" || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : `bindActionCreators expected an object or a function, but instead received: '${kindOf(actionCreators)}'. Did you write \"import ActionCreators from\" instead of \"import * as ActionCreators from\"?`);\n  }\n  const boundActionCreators = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === \"function\") {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n  return boundActionCreators;\n}\n\n// src/compose.ts\nfunction compose(...funcs) {\n  if (funcs.length === 0) {\n    return (arg) => arg;\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce((a, b) => (...args) => a(b(...args)));\n}\n\n// src/applyMiddleware.ts\nfunction applyMiddleware(...middlewares) {\n  return (createStore2) => (reducer, preloadedState) => {\n    const store = createStore2(reducer, preloadedState);\n    let dispatch = () => {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : \"Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.\");\n    };\n    const middlewareAPI = {\n      getState: store.getState,\n      dispatch: (action, ...args) => dispatch(action, ...args)\n    };\n    const chain = middlewares.map((middleware) => middleware(middlewareAPI));\n    dispatch = compose(...chain)(store.dispatch);\n    return {\n      ...store,\n      dispatch\n    };\n  };\n}\n\n// src/utils/isAction.ts\nfunction isAction(action) {\n  return isPlainObject(action) && \"type\" in action && typeof action.type === \"string\";\n}\nexport {\n  actionTypes_default as __DO_NOT_USE__ActionTypes,\n  applyMiddleware,\n  bindActionCreators,\n  combineReducers,\n  compose,\n  createStore,\n  isAction,\n  isPlainObject,\n  legacy_createStore\n};\n//# sourceMappingURL=redux.mjs.map", "// src/devModeChecks/identityFunctionCheck.ts\nvar runIdentityFunctionCheck = (resultFunc, inputSelectorsResults, outputSelectorResult) => {\n  if (inputSelectorsResults.length === 1 && inputSelectorsResults[0] === outputSelectorResult) {\n    let isInputSameAsOutput = false;\n    try {\n      const emptyObject = {};\n      if (resultFunc(emptyObject) === emptyObject)\n        isInputSameAsOutput = true;\n    } catch {\n    }\n    if (isInputSameAsOutput) {\n      let stack = void 0;\n      try {\n        throw new Error();\n      } catch (e) {\n        ;\n        ({ stack } = e);\n      }\n      console.warn(\n        \"The result function returned its own inputs without modification. e.g\\n`createSelector([state => state.todos], todos => todos)`\\nThis could lead to inefficient memoization and unnecessary re-renders.\\nEnsure transformation logic is in the result function, and extraction logic is in the input selectors.\",\n        { stack }\n      );\n    }\n  }\n};\n\n// src/devModeChecks/inputStabilityCheck.ts\nvar runInputStabilityCheck = (inputSelectorResultsObject, options, inputSelectorArgs) => {\n  const { memoize, memoizeOptions } = options;\n  const { inputSelectorResults, inputSelectorResultsCopy } = inputSelectorResultsObject;\n  const createAnEmptyObject = memoize(() => ({}), ...memoizeOptions);\n  const areInputSelectorResultsEqual = createAnEmptyObject.apply(null, inputSelectorResults) === createAnEmptyObject.apply(null, inputSelectorResultsCopy);\n  if (!areInputSelectorResultsEqual) {\n    let stack = void 0;\n    try {\n      throw new Error();\n    } catch (e) {\n      ;\n      ({ stack } = e);\n    }\n    console.warn(\n      \"An input selector returned a different result when passed same arguments.\\nThis means your output selector will likely run more frequently than intended.\\nAvoid returning a new reference inside your input selector, e.g.\\n`createSelector([state => state.todos.map(todo => todo.id)], todoIds => todoIds.length)`\",\n      {\n        arguments: inputSelectorArgs,\n        firstInputs: inputSelectorResults,\n        secondInputs: inputSelectorResultsCopy,\n        stack\n      }\n    );\n  }\n};\n\n// src/devModeChecks/setGlobalDevModeChecks.ts\nvar globalDevModeChecks = {\n  inputStabilityCheck: \"once\",\n  identityFunctionCheck: \"once\"\n};\nvar setGlobalDevModeChecks = (devModeChecks) => {\n  Object.assign(globalDevModeChecks, devModeChecks);\n};\n\n// src/utils.ts\nvar NOT_FOUND = /* @__PURE__ */ Symbol(\"NOT_FOUND\");\nfunction assertIsFunction(func, errorMessage = `expected a function, instead received ${typeof func}`) {\n  if (typeof func !== \"function\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsObject(object, errorMessage = `expected an object, instead received ${typeof object}`) {\n  if (typeof object !== \"object\") {\n    throw new TypeError(errorMessage);\n  }\n}\nfunction assertIsArrayOfFunctions(array, errorMessage = `expected all items to be functions, instead received the following types: `) {\n  if (!array.every((item) => typeof item === \"function\")) {\n    const itemTypes = array.map(\n      (item) => typeof item === \"function\" ? `function ${item.name || \"unnamed\"}()` : typeof item\n    ).join(\", \");\n    throw new TypeError(`${errorMessage}[${itemTypes}]`);\n  }\n}\nvar ensureIsArray = (item) => {\n  return Array.isArray(item) ? item : [item];\n};\nfunction getDependencies(createSelectorArgs) {\n  const dependencies = Array.isArray(createSelectorArgs[0]) ? createSelectorArgs[0] : createSelectorArgs;\n  assertIsArrayOfFunctions(\n    dependencies,\n    `createSelector expects all input-selectors to be functions, but received the following types: `\n  );\n  return dependencies;\n}\nfunction collectInputSelectorResults(dependencies, inputSelectorArgs) {\n  const inputSelectorResults = [];\n  const { length } = dependencies;\n  for (let i = 0; i < length; i++) {\n    inputSelectorResults.push(dependencies[i].apply(null, inputSelectorArgs));\n  }\n  return inputSelectorResults;\n}\nvar getDevModeChecksExecutionInfo = (firstRun, devModeChecks) => {\n  const { identityFunctionCheck, inputStabilityCheck } = {\n    ...globalDevModeChecks,\n    ...devModeChecks\n  };\n  return {\n    identityFunctionCheck: {\n      shouldRun: identityFunctionCheck === \"always\" || identityFunctionCheck === \"once\" && firstRun,\n      run: runIdentityFunctionCheck\n    },\n    inputStabilityCheck: {\n      shouldRun: inputStabilityCheck === \"always\" || inputStabilityCheck === \"once\" && firstRun,\n      run: runInputStabilityCheck\n    }\n  };\n};\n\n// src/autotrackMemoize/autotracking.ts\nvar $REVISION = 0;\nvar CURRENT_TRACKER = null;\nvar Cell = class {\n  revision = $REVISION;\n  _value;\n  _lastValue;\n  _isEqual = tripleEq;\n  constructor(initialValue, isEqual = tripleEq) {\n    this._value = this._lastValue = initialValue;\n    this._isEqual = isEqual;\n  }\n  // Whenever a storage value is read, it'll add itself to the current tracker if\n  // one exists, entangling its state with that cache.\n  get value() {\n    CURRENT_TRACKER?.add(this);\n    return this._value;\n  }\n  // Whenever a storage value is updated, we bump the global revision clock,\n  // assign the revision for this storage to the new value, _and_ we schedule a\n  // rerender. This is important, and it's what makes autotracking  _pull_\n  // based. We don't actively tell the caches which depend on the storage that\n  // anything has happened. Instead, we recompute the caches when needed.\n  set value(newValue) {\n    if (this.value === newValue)\n      return;\n    this._value = newValue;\n    this.revision = ++$REVISION;\n  }\n};\nfunction tripleEq(a, b) {\n  return a === b;\n}\nvar TrackingCache = class {\n  _cachedValue;\n  _cachedRevision = -1;\n  _deps = [];\n  hits = 0;\n  fn;\n  constructor(fn) {\n    this.fn = fn;\n  }\n  clear() {\n    this._cachedValue = void 0;\n    this._cachedRevision = -1;\n    this._deps = [];\n    this.hits = 0;\n  }\n  get value() {\n    if (this.revision > this._cachedRevision) {\n      const { fn } = this;\n      const currentTracker = /* @__PURE__ */ new Set();\n      const prevTracker = CURRENT_TRACKER;\n      CURRENT_TRACKER = currentTracker;\n      this._cachedValue = fn();\n      CURRENT_TRACKER = prevTracker;\n      this.hits++;\n      this._deps = Array.from(currentTracker);\n      this._cachedRevision = this.revision;\n    }\n    CURRENT_TRACKER?.add(this);\n    return this._cachedValue;\n  }\n  get revision() {\n    return Math.max(...this._deps.map((d) => d.revision), 0);\n  }\n};\nfunction getValue(cell) {\n  if (!(cell instanceof Cell)) {\n    console.warn(\"Not a valid cell! \", cell);\n  }\n  return cell.value;\n}\nfunction setValue(storage, value) {\n  if (!(storage instanceof Cell)) {\n    throw new TypeError(\n      \"setValue must be passed a tracked store created with `createStorage`.\"\n    );\n  }\n  storage.value = storage._lastValue = value;\n}\nfunction createCell(initialValue, isEqual = tripleEq) {\n  return new Cell(initialValue, isEqual);\n}\nfunction createCache(fn) {\n  assertIsFunction(\n    fn,\n    \"the first parameter to `createCache` must be a function\"\n  );\n  return new TrackingCache(fn);\n}\n\n// src/autotrackMemoize/tracking.ts\nvar neverEq = (a, b) => false;\nfunction createTag() {\n  return createCell(null, neverEq);\n}\nfunction dirtyTag(tag, value) {\n  setValue(tag, value);\n}\nvar consumeCollection = (node) => {\n  let tag = node.collectionTag;\n  if (tag === null) {\n    tag = node.collectionTag = createTag();\n  }\n  getValue(tag);\n};\nvar dirtyCollection = (node) => {\n  const tag = node.collectionTag;\n  if (tag !== null) {\n    dirtyTag(tag, null);\n  }\n};\n\n// src/autotrackMemoize/proxy.ts\nvar REDUX_PROXY_LABEL = Symbol();\nvar nextId = 0;\nvar proto = Object.getPrototypeOf({});\nvar ObjectTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy(this, objectProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar objectProxyHandler = {\n  get(node, key) {\n    function calculateResult() {\n      const { value } = node;\n      const childValue = Reflect.get(value, key);\n      if (typeof key === \"symbol\") {\n        return childValue;\n      }\n      if (key in proto) {\n        return childValue;\n      }\n      if (typeof childValue === \"object\" && childValue !== null) {\n        let childNode = node.children[key];\n        if (childNode === void 0) {\n          childNode = node.children[key] = createNode(childValue);\n        }\n        if (childNode.tag) {\n          getValue(childNode.tag);\n        }\n        return childNode.proxy;\n      } else {\n        let tag = node.tags[key];\n        if (tag === void 0) {\n          tag = node.tags[key] = createTag();\n          tag.value = childValue;\n        }\n        getValue(tag);\n        return childValue;\n      }\n    }\n    const res = calculateResult();\n    return res;\n  },\n  ownKeys(node) {\n    consumeCollection(node);\n    return Reflect.ownKeys(node.value);\n  },\n  getOwnPropertyDescriptor(node, prop) {\n    return Reflect.getOwnPropertyDescriptor(node.value, prop);\n  },\n  has(node, prop) {\n    return Reflect.has(node.value, prop);\n  }\n};\nvar ArrayTreeNode = class {\n  constructor(value) {\n    this.value = value;\n    this.value = value;\n    this.tag.value = value;\n  }\n  proxy = new Proxy([this], arrayProxyHandler);\n  tag = createTag();\n  tags = {};\n  children = {};\n  collectionTag = null;\n  id = nextId++;\n};\nvar arrayProxyHandler = {\n  get([node], key) {\n    if (key === \"length\") {\n      consumeCollection(node);\n    }\n    return objectProxyHandler.get(node, key);\n  },\n  ownKeys([node]) {\n    return objectProxyHandler.ownKeys(node);\n  },\n  getOwnPropertyDescriptor([node], prop) {\n    return objectProxyHandler.getOwnPropertyDescriptor(node, prop);\n  },\n  has([node], prop) {\n    return objectProxyHandler.has(node, prop);\n  }\n};\nfunction createNode(value) {\n  if (Array.isArray(value)) {\n    return new ArrayTreeNode(value);\n  }\n  return new ObjectTreeNode(value);\n}\nfunction updateNode(node, newValue) {\n  const { value, tags, children } = node;\n  node.value = newValue;\n  if (Array.isArray(value) && Array.isArray(newValue) && value.length !== newValue.length) {\n    dirtyCollection(node);\n  } else {\n    if (value !== newValue) {\n      let oldKeysSize = 0;\n      let newKeysSize = 0;\n      let anyKeysAdded = false;\n      for (const _key in value) {\n        oldKeysSize++;\n      }\n      for (const key in newValue) {\n        newKeysSize++;\n        if (!(key in value)) {\n          anyKeysAdded = true;\n          break;\n        }\n      }\n      const isDifferent = anyKeysAdded || oldKeysSize !== newKeysSize;\n      if (isDifferent) {\n        dirtyCollection(node);\n      }\n    }\n  }\n  for (const key in tags) {\n    const childValue = value[key];\n    const newChildValue = newValue[key];\n    if (childValue !== newChildValue) {\n      dirtyCollection(node);\n      dirtyTag(tags[key], newChildValue);\n    }\n    if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      delete tags[key];\n    }\n  }\n  for (const key in children) {\n    const childNode = children[key];\n    const newChildValue = newValue[key];\n    const childValue = childNode.value;\n    if (childValue === newChildValue) {\n      continue;\n    } else if (typeof newChildValue === \"object\" && newChildValue !== null) {\n      updateNode(childNode, newChildValue);\n    } else {\n      deleteNode(childNode);\n      delete children[key];\n    }\n  }\n}\nfunction deleteNode(node) {\n  if (node.tag) {\n    dirtyTag(node.tag, null);\n  }\n  dirtyCollection(node);\n  for (const key in node.tags) {\n    dirtyTag(node.tags[key], null);\n  }\n  for (const key in node.children) {\n    deleteNode(node.children[key]);\n  }\n}\n\n// src/lruMemoize.ts\nfunction createSingletonCache(equals) {\n  let entry;\n  return {\n    get(key) {\n      if (entry && equals(entry.key, key)) {\n        return entry.value;\n      }\n      return NOT_FOUND;\n    },\n    put(key, value) {\n      entry = { key, value };\n    },\n    getEntries() {\n      return entry ? [entry] : [];\n    },\n    clear() {\n      entry = void 0;\n    }\n  };\n}\nfunction createLruCache(maxSize, equals) {\n  let entries = [];\n  function get(key) {\n    const cacheIndex = entries.findIndex((entry) => equals(key, entry.key));\n    if (cacheIndex > -1) {\n      const entry = entries[cacheIndex];\n      if (cacheIndex > 0) {\n        entries.splice(cacheIndex, 1);\n        entries.unshift(entry);\n      }\n      return entry.value;\n    }\n    return NOT_FOUND;\n  }\n  function put(key, value) {\n    if (get(key) === NOT_FOUND) {\n      entries.unshift({ key, value });\n      if (entries.length > maxSize) {\n        entries.pop();\n      }\n    }\n  }\n  function getEntries() {\n    return entries;\n  }\n  function clear() {\n    entries = [];\n  }\n  return { get, put, getEntries, clear };\n}\nvar referenceEqualityCheck = (a, b) => a === b;\nfunction createCacheKeyComparator(equalityCheck) {\n  return function areArgumentsShallowlyEqual(prev, next) {\n    if (prev === null || next === null || prev.length !== next.length) {\n      return false;\n    }\n    const { length } = prev;\n    for (let i = 0; i < length; i++) {\n      if (!equalityCheck(prev[i], next[i])) {\n        return false;\n      }\n    }\n    return true;\n  };\n}\nfunction lruMemoize(func, equalityCheckOrOptions) {\n  const providedOptions = typeof equalityCheckOrOptions === \"object\" ? equalityCheckOrOptions : { equalityCheck: equalityCheckOrOptions };\n  const {\n    equalityCheck = referenceEqualityCheck,\n    maxSize = 1,\n    resultEqualityCheck\n  } = providedOptions;\n  const comparator = createCacheKeyComparator(equalityCheck);\n  let resultsCount = 0;\n  const cache = maxSize <= 1 ? createSingletonCache(comparator) : createLruCache(maxSize, comparator);\n  function memoized() {\n    let value = cache.get(arguments);\n    if (value === NOT_FOUND) {\n      value = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const entries = cache.getEntries();\n        const matchingEntry = entries.find(\n          (entry) => resultEqualityCheck(entry.value, value)\n        );\n        if (matchingEntry) {\n          value = matchingEntry.value;\n          resultsCount !== 0 && resultsCount--;\n        }\n      }\n      cache.put(arguments, value);\n    }\n    return value;\n  }\n  memoized.clearCache = () => {\n    cache.clear();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/autotrackMemoize/autotrackMemoize.ts\nfunction autotrackMemoize(func) {\n  const node = createNode(\n    []\n  );\n  let lastArgs = null;\n  const shallowEqual = createCacheKeyComparator(referenceEqualityCheck);\n  const cache = createCache(() => {\n    const res = func.apply(null, node.proxy);\n    return res;\n  });\n  function memoized() {\n    if (!shallowEqual(lastArgs, arguments)) {\n      updateNode(node, arguments);\n      lastArgs = arguments;\n    }\n    return cache.value;\n  }\n  memoized.clearCache = () => {\n    return cache.clear();\n  };\n  return memoized;\n}\n\n// src/weakMapMemoize.ts\nvar StrongRef = class {\n  constructor(value) {\n    this.value = value;\n  }\n  deref() {\n    return this.value;\n  }\n};\nvar Ref = typeof WeakRef !== \"undefined\" ? WeakRef : StrongRef;\nvar UNTERMINATED = 0;\nvar TERMINATED = 1;\nfunction createCacheNode() {\n  return {\n    s: UNTERMINATED,\n    v: void 0,\n    o: null,\n    p: null\n  };\n}\nfunction weakMapMemoize(func, options = {}) {\n  let fnNode = createCacheNode();\n  const { resultEqualityCheck } = options;\n  let lastResult;\n  let resultsCount = 0;\n  function memoized() {\n    let cacheNode = fnNode;\n    const { length } = arguments;\n    for (let i = 0, l = length; i < l; i++) {\n      const arg = arguments[i];\n      if (typeof arg === \"function\" || typeof arg === \"object\" && arg !== null) {\n        let objectCache = cacheNode.o;\n        if (objectCache === null) {\n          cacheNode.o = objectCache = /* @__PURE__ */ new WeakMap();\n        }\n        const objectNode = objectCache.get(arg);\n        if (objectNode === void 0) {\n          cacheNode = createCacheNode();\n          objectCache.set(arg, cacheNode);\n        } else {\n          cacheNode = objectNode;\n        }\n      } else {\n        let primitiveCache = cacheNode.p;\n        if (primitiveCache === null) {\n          cacheNode.p = primitiveCache = /* @__PURE__ */ new Map();\n        }\n        const primitiveNode = primitiveCache.get(arg);\n        if (primitiveNode === void 0) {\n          cacheNode = createCacheNode();\n          primitiveCache.set(arg, cacheNode);\n        } else {\n          cacheNode = primitiveNode;\n        }\n      }\n    }\n    const terminatedNode = cacheNode;\n    let result;\n    if (cacheNode.s === TERMINATED) {\n      result = cacheNode.v;\n    } else {\n      result = func.apply(null, arguments);\n      resultsCount++;\n      if (resultEqualityCheck) {\n        const lastResultValue = lastResult?.deref?.() ?? lastResult;\n        if (lastResultValue != null && resultEqualityCheck(lastResultValue, result)) {\n          result = lastResultValue;\n          resultsCount !== 0 && resultsCount--;\n        }\n        const needsWeakRef = typeof result === \"object\" && result !== null || typeof result === \"function\";\n        lastResult = needsWeakRef ? new Ref(result) : result;\n      }\n    }\n    terminatedNode.s = TERMINATED;\n    terminatedNode.v = result;\n    return result;\n  }\n  memoized.clearCache = () => {\n    fnNode = createCacheNode();\n    memoized.resetResultsCount();\n  };\n  memoized.resultsCount = () => resultsCount;\n  memoized.resetResultsCount = () => {\n    resultsCount = 0;\n  };\n  return memoized;\n}\n\n// src/createSelectorCreator.ts\nfunction createSelectorCreator(memoizeOrOptions, ...memoizeOptionsFromArgs) {\n  const createSelectorCreatorOptions = typeof memoizeOrOptions === \"function\" ? {\n    memoize: memoizeOrOptions,\n    memoizeOptions: memoizeOptionsFromArgs\n  } : memoizeOrOptions;\n  const createSelector2 = (...createSelectorArgs) => {\n    let recomputations = 0;\n    let dependencyRecomputations = 0;\n    let lastResult;\n    let directlyPassedOptions = {};\n    let resultFunc = createSelectorArgs.pop();\n    if (typeof resultFunc === \"object\") {\n      directlyPassedOptions = resultFunc;\n      resultFunc = createSelectorArgs.pop();\n    }\n    assertIsFunction(\n      resultFunc,\n      `createSelector expects an output function after the inputs, but received: [${typeof resultFunc}]`\n    );\n    const combinedOptions = {\n      ...createSelectorCreatorOptions,\n      ...directlyPassedOptions\n    };\n    const {\n      memoize,\n      memoizeOptions = [],\n      argsMemoize = weakMapMemoize,\n      argsMemoizeOptions = [],\n      devModeChecks = {}\n    } = combinedOptions;\n    const finalMemoizeOptions = ensureIsArray(memoizeOptions);\n    const finalArgsMemoizeOptions = ensureIsArray(argsMemoizeOptions);\n    const dependencies = getDependencies(createSelectorArgs);\n    const memoizedResultFunc = memoize(function recomputationWrapper() {\n      recomputations++;\n      return resultFunc.apply(\n        null,\n        arguments\n      );\n    }, ...finalMemoizeOptions);\n    let firstRun = true;\n    const selector = argsMemoize(function dependenciesChecker() {\n      dependencyRecomputations++;\n      const inputSelectorResults = collectInputSelectorResults(\n        dependencies,\n        arguments\n      );\n      lastResult = memoizedResultFunc.apply(null, inputSelectorResults);\n      if (process.env.NODE_ENV !== \"production\") {\n        const { identityFunctionCheck, inputStabilityCheck } = getDevModeChecksExecutionInfo(firstRun, devModeChecks);\n        if (identityFunctionCheck.shouldRun) {\n          identityFunctionCheck.run(\n            resultFunc,\n            inputSelectorResults,\n            lastResult\n          );\n        }\n        if (inputStabilityCheck.shouldRun) {\n          const inputSelectorResultsCopy = collectInputSelectorResults(\n            dependencies,\n            arguments\n          );\n          inputStabilityCheck.run(\n            { inputSelectorResults, inputSelectorResultsCopy },\n            { memoize, memoizeOptions: finalMemoizeOptions },\n            arguments\n          );\n        }\n        if (firstRun)\n          firstRun = false;\n      }\n      return lastResult;\n    }, ...finalArgsMemoizeOptions);\n    return Object.assign(selector, {\n      resultFunc,\n      memoizedResultFunc,\n      dependencies,\n      dependencyRecomputations: () => dependencyRecomputations,\n      resetDependencyRecomputations: () => {\n        dependencyRecomputations = 0;\n      },\n      lastResult: () => lastResult,\n      recomputations: () => recomputations,\n      resetRecomputations: () => {\n        recomputations = 0;\n      },\n      memoize,\n      argsMemoize\n    });\n  };\n  Object.assign(createSelector2, {\n    withTypes: () => createSelector2\n  });\n  return createSelector2;\n}\nvar createSelector = /* @__PURE__ */ createSelectorCreator(weakMapMemoize);\n\n// src/createStructuredSelector.ts\nvar createStructuredSelector = Object.assign(\n  (inputSelectorsObject, selectorCreator = createSelector) => {\n    assertIsObject(\n      inputSelectorsObject,\n      `createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof inputSelectorsObject}`\n    );\n    const inputSelectorKeys = Object.keys(inputSelectorsObject);\n    const dependencies = inputSelectorKeys.map(\n      (key) => inputSelectorsObject[key]\n    );\n    const structuredSelector = selectorCreator(\n      dependencies,\n      (...inputSelectorResults) => {\n        return inputSelectorResults.reduce((composition, value, index) => {\n          composition[inputSelectorKeys[index]] = value;\n          return composition;\n        }, {});\n      }\n    );\n    return structuredSelector;\n  },\n  { withTypes: () => createStructuredSelector }\n);\nexport {\n  createSelector,\n  createSelectorCreator,\n  createStructuredSelector,\n  lruMemoize,\n  referenceEqualityCheck,\n  setGlobalDevModeChecks,\n  autotrackMemoize as unstable_autotrackMemoize,\n  weakMapMemoize\n};\n//# sourceMappingURL=reselect.mjs.map", "var highlightRegExp = /highlight-(?:text|source)-([a-z0-9]+)/;\n\nfunction highlightedCodeBlock (turndownService) {\n  turndownService.addRule('highlightedCodeBlock', {\n    filter: function (node) {\n      var firstChild = node.firstChild;\n      return (\n        node.nodeName === 'DIV' &&\n        highlightRegExp.test(node.className) &&\n        firstChild &&\n        firstChild.nodeName === 'PRE'\n      )\n    },\n    replacement: function (content, node, options) {\n      var className = node.className || '';\n      var language = (className.match(highlightRegExp) || [null, ''])[1];\n\n      return (\n        '\\n\\n' + options.fence + language + '\\n' +\n        node.firstChild.textContent +\n        '\\n' + options.fence + '\\n\\n'\n      )\n    }\n  });\n}\n\nfunction strikethrough (turndownService) {\n  turndownService.addRule('strikethrough', {\n    filter: ['del', 's', 'strike'],\n    replacement: function (content) {\n      return '~' + content + '~'\n    }\n  });\n}\n\nvar indexOf = Array.prototype.indexOf;\nvar every = Array.prototype.every;\nvar rules = {};\n\nrules.tableCell = {\n  filter: ['th', 'td'],\n  replacement: function (content, node) {\n    return cell(content, node)\n  }\n};\n\nrules.tableRow = {\n  filter: 'tr',\n  replacement: function (content, node) {\n    var borderCells = '';\n    var alignMap = { left: ':--', right: '--:', center: ':-:' };\n\n    if (isHeadingRow(node)) {\n      for (var i = 0; i < node.childNodes.length; i++) {\n        var border = '---';\n        var align = (\n          node.childNodes[i].getAttribute('align') || ''\n        ).toLowerCase();\n\n        if (align) border = alignMap[align] || border;\n\n        borderCells += cell(border, node.childNodes[i]);\n      }\n    }\n    return '\\n' + content + (borderCells ? '\\n' + borderCells : '')\n  }\n};\n\nrules.table = {\n  // Only convert tables with a heading row.\n  // Tables with no heading row are kept using `keep` (see below).\n  filter: function (node) {\n    return node.nodeName === 'TABLE' && isHeadingRow(node.rows[0])\n  },\n\n  replacement: function (content) {\n    // Ensure there are no blank lines\n    content = content.replace('\\n\\n', '\\n');\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.tableSection = {\n  filter: ['thead', 'tbody', 'tfoot'],\n  replacement: function (content) {\n    return content\n  }\n};\n\n// A tr is a heading row if:\n// - the parent is a THEAD\n// - or if its the first child of the TABLE or the first TBODY (possibly\n//   following a blank THEAD)\n// - and every cell is a TH\nfunction isHeadingRow (tr) {\n  var parentNode = tr.parentNode;\n  return (\n    parentNode.nodeName === 'THEAD' ||\n    (\n      parentNode.firstChild === tr &&\n      (parentNode.nodeName === 'TABLE' || isFirstTbody(parentNode)) &&\n      every.call(tr.childNodes, function (n) { return n.nodeName === 'TH' })\n    )\n  )\n}\n\nfunction isFirstTbody (element) {\n  var previousSibling = element.previousSibling;\n  return (\n    element.nodeName === 'TBODY' && (\n      !previousSibling ||\n      (\n        previousSibling.nodeName === 'THEAD' &&\n        /^\\s*$/i.test(previousSibling.textContent)\n      )\n    )\n  )\n}\n\nfunction cell (content, node) {\n  var index = indexOf.call(node.parentNode.childNodes, node);\n  var prefix = ' ';\n  if (index === 0) prefix = '| ';\n  return prefix + content + ' |'\n}\n\nfunction tables (turndownService) {\n  turndownService.keep(function (node) {\n    return node.nodeName === 'TABLE' && !isHeadingRow(node.rows[0])\n  });\n  for (var key in rules) turndownService.addRule(key, rules[key]);\n}\n\nfunction taskListItems (turndownService) {\n  turndownService.addRule('taskListItems', {\n    filter: function (node) {\n      return node.type === 'checkbox' && node.parentNode.nodeName === 'LI'\n    },\n    replacement: function (content, node) {\n      return (node.checked ? '[x]' : '[ ]') + ' '\n    }\n  });\n}\n\nfunction gfm (turndownService) {\n  turndownService.use([\n    highlightedCodeBlock,\n    strikethrough,\n    tables,\n    taskListItems\n  ]);\n}\n\nexport { gfm, highlightedCodeBlock, strikethrough, tables, taskListItems };\n", "function extend (destination) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (source.hasOwnProperty(key)) destination[key] = source[key];\n    }\n  }\n  return destination\n}\n\nfunction repeat (character, count) {\n  return Array(count + 1).join(character)\n}\n\nfunction trimLeadingNewlines (string) {\n  return string.replace(/^\\n*/, '')\n}\n\nfunction trimTrailingNewlines (string) {\n  // avoid match-at-end regexp bottleneck, see #370\n  var indexEnd = string.length;\n  while (indexEnd > 0 && string[indexEnd - 1] === '\\n') indexEnd--;\n  return string.substring(0, indexEnd)\n}\n\nvar blockElements = [\n  'ADDRESS', 'ARTICLE', 'ASIDE', 'AUDIO', 'BLOCKQUOTE', 'BODY', 'CANVAS',\n  'CENTER', 'DD', 'DIR', 'DIV', 'DL', 'DT', 'FIELDSET', 'FIGCAPTION', 'FIGURE',\n  'FOOTER', 'FORM', 'FRAMESET', 'H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'HEADER',\n  'HGROUP', 'HR', 'HTML', 'ISINDEX', 'LI', 'MAIN', 'MENU', 'NAV', 'NOFRAMES',\n  'NOSCRIPT', 'OL', 'OUTPUT', 'P', 'PRE', 'SECTION', 'TABLE', 'TBODY', 'TD',\n  'TFOOT', 'TH', 'THEAD', 'TR', 'UL'\n];\n\nfunction isBlock (node) {\n  return is(node, blockElements)\n}\n\nvar voidElements = [\n  'AREA', 'BASE', 'BR', 'COL', 'COMMAND', 'EMBED', 'HR', 'IMG', 'INPUT',\n  'KEYGEN', 'LINK', 'META', 'PARAM', 'SOURCE', 'TRACK', 'WBR'\n];\n\nfunction isVoid (node) {\n  return is(node, voidElements)\n}\n\nfunction hasVoid (node) {\n  return has(node, voidElements)\n}\n\nvar meaningfulWhenBlankElements = [\n  'A', 'TABLE', 'THEAD', 'TBODY', 'TFOOT', 'TH', 'TD', 'IFRAME', 'SCRIPT',\n  'AUDIO', 'VIDEO'\n];\n\nfunction isMeaningfulWhenBlank (node) {\n  return is(node, meaningfulWhenBlankElements)\n}\n\nfunction hasMeaningfulWhenBlank (node) {\n  return has(node, meaningfulWhenBlankElements)\n}\n\nfunction is (node, tagNames) {\n  return tagNames.indexOf(node.nodeName) >= 0\n}\n\nfunction has (node, tagNames) {\n  return (\n    node.getElementsByTagName &&\n    tagNames.some(function (tagName) {\n      return node.getElementsByTagName(tagName).length\n    })\n  )\n}\n\nvar rules = {};\n\nrules.paragraph = {\n  filter: 'p',\n\n  replacement: function (content) {\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.lineBreak = {\n  filter: 'br',\n\n  replacement: function (content, node, options) {\n    return options.br + '\\n'\n  }\n};\n\nrules.heading = {\n  filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],\n\n  replacement: function (content, node, options) {\n    var hLevel = Number(node.nodeName.charAt(1));\n\n    if (options.headingStyle === 'setext' && hLevel < 3) {\n      var underline = repeat((hLevel === 1 ? '=' : '-'), content.length);\n      return (\n        '\\n\\n' + content + '\\n' + underline + '\\n\\n'\n      )\n    } else {\n      return '\\n\\n' + repeat('#', hLevel) + ' ' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.blockquote = {\n  filter: 'blockquote',\n\n  replacement: function (content) {\n    content = content.replace(/^\\n+|\\n+$/g, '');\n    content = content.replace(/^/gm, '> ');\n    return '\\n\\n' + content + '\\n\\n'\n  }\n};\n\nrules.list = {\n  filter: ['ul', 'ol'],\n\n  replacement: function (content, node) {\n    var parent = node.parentNode;\n    if (parent.nodeName === 'LI' && parent.lastElementChild === node) {\n      return '\\n' + content\n    } else {\n      return '\\n\\n' + content + '\\n\\n'\n    }\n  }\n};\n\nrules.listItem = {\n  filter: 'li',\n\n  replacement: function (content, node, options) {\n    content = content\n      .replace(/^\\n+/, '') // remove leading newlines\n      .replace(/\\n+$/, '\\n') // replace trailing newlines with just a single one\n      .replace(/\\n/gm, '\\n    '); // indent\n    var prefix = options.bulletListMarker + '   ';\n    var parent = node.parentNode;\n    if (parent.nodeName === 'OL') {\n      var start = parent.getAttribute('start');\n      var index = Array.prototype.indexOf.call(parent.children, node);\n      prefix = (start ? Number(start) + index : index + 1) + '.  ';\n    }\n    return (\n      prefix + content + (node.nextSibling && !/\\n$/.test(content) ? '\\n' : '')\n    )\n  }\n};\n\nrules.indentedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'indented' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    return (\n      '\\n\\n    ' +\n      node.firstChild.textContent.replace(/\\n/g, '\\n    ') +\n      '\\n\\n'\n    )\n  }\n};\n\nrules.fencedCodeBlock = {\n  filter: function (node, options) {\n    return (\n      options.codeBlockStyle === 'fenced' &&\n      node.nodeName === 'PRE' &&\n      node.firstChild &&\n      node.firstChild.nodeName === 'CODE'\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var className = node.firstChild.getAttribute('class') || '';\n    var language = (className.match(/language-(\\S+)/) || [null, ''])[1];\n    var code = node.firstChild.textContent;\n\n    var fenceChar = options.fence.charAt(0);\n    var fenceSize = 3;\n    var fenceInCodeRegex = new RegExp('^' + fenceChar + '{3,}', 'gm');\n\n    var match;\n    while ((match = fenceInCodeRegex.exec(code))) {\n      if (match[0].length >= fenceSize) {\n        fenceSize = match[0].length + 1;\n      }\n    }\n\n    var fence = repeat(fenceChar, fenceSize);\n\n    return (\n      '\\n\\n' + fence + language + '\\n' +\n      code.replace(/\\n$/, '') +\n      '\\n' + fence + '\\n\\n'\n    )\n  }\n};\n\nrules.horizontalRule = {\n  filter: 'hr',\n\n  replacement: function (content, node, options) {\n    return '\\n\\n' + options.hr + '\\n\\n'\n  }\n};\n\nrules.inlineLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'inlined' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node) {\n    var href = node.getAttribute('href');\n    if (href) href = href.replace(/([()])/g, '\\\\$1');\n    var title = cleanAttribute(node.getAttribute('title'));\n    if (title) title = ' \"' + title.replace(/\"/g, '\\\\\"') + '\"';\n    return '[' + content + '](' + href + title + ')'\n  }\n};\n\nrules.referenceLink = {\n  filter: function (node, options) {\n    return (\n      options.linkStyle === 'referenced' &&\n      node.nodeName === 'A' &&\n      node.getAttribute('href')\n    )\n  },\n\n  replacement: function (content, node, options) {\n    var href = node.getAttribute('href');\n    var title = cleanAttribute(node.getAttribute('title'));\n    if (title) title = ' \"' + title + '\"';\n    var replacement;\n    var reference;\n\n    switch (options.linkReferenceStyle) {\n      case 'collapsed':\n        replacement = '[' + content + '][]';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      case 'shortcut':\n        replacement = '[' + content + ']';\n        reference = '[' + content + ']: ' + href + title;\n        break\n      default:\n        var id = this.references.length + 1;\n        replacement = '[' + content + '][' + id + ']';\n        reference = '[' + id + ']: ' + href + title;\n    }\n\n    this.references.push(reference);\n    return replacement\n  },\n\n  references: [],\n\n  append: function (options) {\n    var references = '';\n    if (this.references.length) {\n      references = '\\n\\n' + this.references.join('\\n') + '\\n\\n';\n      this.references = []; // Reset references\n    }\n    return references\n  }\n};\n\nrules.emphasis = {\n  filter: ['em', 'i'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.emDelimiter + content + options.emDelimiter\n  }\n};\n\nrules.strong = {\n  filter: ['strong', 'b'],\n\n  replacement: function (content, node, options) {\n    if (!content.trim()) return ''\n    return options.strongDelimiter + content + options.strongDelimiter\n  }\n};\n\nrules.code = {\n  filter: function (node) {\n    var hasSiblings = node.previousSibling || node.nextSibling;\n    var isCodeBlock = node.parentNode.nodeName === 'PRE' && !hasSiblings;\n\n    return node.nodeName === 'CODE' && !isCodeBlock\n  },\n\n  replacement: function (content) {\n    if (!content) return ''\n    content = content.replace(/\\r?\\n|\\r/g, ' ');\n\n    var extraSpace = /^`|^ .*?[^ ].* $|`$/.test(content) ? ' ' : '';\n    var delimiter = '`';\n    var matches = content.match(/`+/gm) || [];\n    while (matches.indexOf(delimiter) !== -1) delimiter = delimiter + '`';\n\n    return delimiter + extraSpace + content + extraSpace + delimiter\n  }\n};\n\nrules.image = {\n  filter: 'img',\n\n  replacement: function (content, node) {\n    var alt = cleanAttribute(node.getAttribute('alt'));\n    var src = node.getAttribute('src') || '';\n    var title = cleanAttribute(node.getAttribute('title'));\n    var titlePart = title ? ' \"' + title + '\"' : '';\n    return src ? '![' + alt + ']' + '(' + src + titlePart + ')' : ''\n  }\n};\n\nfunction cleanAttribute (attribute) {\n  return attribute ? attribute.replace(/(\\n+\\s*)+/g, '\\n') : ''\n}\n\n/**\n * Manages a collection of rules used to convert HTML to Markdown\n */\n\nfunction Rules (options) {\n  this.options = options;\n  this._keep = [];\n  this._remove = [];\n\n  this.blankRule = {\n    replacement: options.blankReplacement\n  };\n\n  this.keepReplacement = options.keepReplacement;\n\n  this.defaultRule = {\n    replacement: options.defaultReplacement\n  };\n\n  this.array = [];\n  for (var key in options.rules) this.array.push(options.rules[key]);\n}\n\nRules.prototype = {\n  add: function (key, rule) {\n    this.array.unshift(rule);\n  },\n\n  keep: function (filter) {\n    this._keep.unshift({\n      filter: filter,\n      replacement: this.keepReplacement\n    });\n  },\n\n  remove: function (filter) {\n    this._remove.unshift({\n      filter: filter,\n      replacement: function () {\n        return ''\n      }\n    });\n  },\n\n  forNode: function (node) {\n    if (node.isBlank) return this.blankRule\n    var rule;\n\n    if ((rule = findRule(this.array, node, this.options))) return rule\n    if ((rule = findRule(this._keep, node, this.options))) return rule\n    if ((rule = findRule(this._remove, node, this.options))) return rule\n\n    return this.defaultRule\n  },\n\n  forEach: function (fn) {\n    for (var i = 0; i < this.array.length; i++) fn(this.array[i], i);\n  }\n};\n\nfunction findRule (rules, node, options) {\n  for (var i = 0; i < rules.length; i++) {\n    var rule = rules[i];\n    if (filterValue(rule, node, options)) return rule\n  }\n  return void 0\n}\n\nfunction filterValue (rule, node, options) {\n  var filter = rule.filter;\n  if (typeof filter === 'string') {\n    if (filter === node.nodeName.toLowerCase()) return true\n  } else if (Array.isArray(filter)) {\n    if (filter.indexOf(node.nodeName.toLowerCase()) > -1) return true\n  } else if (typeof filter === 'function') {\n    if (filter.call(rule, node, options)) return true\n  } else {\n    throw new TypeError('`filter` needs to be a string, array, or function')\n  }\n}\n\n/**\n * The collapseWhitespace function is adapted from collapse-whitespace\n * by Luc Thevenard.\n *\n * The MIT License (MIT)\n *\n * Copyright (c) 2014 Luc Thevenard <<EMAIL>>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n */\n\n/**\n * collapseWhitespace(options) removes extraneous whitespace from an the given element.\n *\n * @param {Object} options\n */\nfunction collapseWhitespace (options) {\n  var element = options.element;\n  var isBlock = options.isBlock;\n  var isVoid = options.isVoid;\n  var isPre = options.isPre || function (node) {\n    return node.nodeName === 'PRE'\n  };\n\n  if (!element.firstChild || isPre(element)) return\n\n  var prevText = null;\n  var keepLeadingWs = false;\n\n  var prev = null;\n  var node = next(prev, element, isPre);\n\n  while (node !== element) {\n    if (node.nodeType === 3 || node.nodeType === 4) { // Node.TEXT_NODE or Node.CDATA_SECTION_NODE\n      var text = node.data.replace(/[ \\r\\n\\t]+/g, ' ');\n\n      if ((!prevText || / $/.test(prevText.data)) &&\n          !keepLeadingWs && text[0] === ' ') {\n        text = text.substr(1);\n      }\n\n      // `text` might be empty at this point.\n      if (!text) {\n        node = remove(node);\n        continue\n      }\n\n      node.data = text;\n\n      prevText = node;\n    } else if (node.nodeType === 1) { // Node.ELEMENT_NODE\n      if (isBlock(node) || node.nodeName === 'BR') {\n        if (prevText) {\n          prevText.data = prevText.data.replace(/ $/, '');\n        }\n\n        prevText = null;\n        keepLeadingWs = false;\n      } else if (isVoid(node) || isPre(node)) {\n        // Avoid trimming space around non-block, non-BR void elements and inline PRE.\n        prevText = null;\n        keepLeadingWs = true;\n      } else if (prevText) {\n        // Drop protection if set previously.\n        keepLeadingWs = false;\n      }\n    } else {\n      node = remove(node);\n      continue\n    }\n\n    var nextNode = next(prev, node, isPre);\n    prev = node;\n    node = nextNode;\n  }\n\n  if (prevText) {\n    prevText.data = prevText.data.replace(/ $/, '');\n    if (!prevText.data) {\n      remove(prevText);\n    }\n  }\n}\n\n/**\n * remove(node) removes the given node from the DOM and returns the\n * next node in the sequence.\n *\n * @param {Node} node\n * @return {Node} node\n */\nfunction remove (node) {\n  var next = node.nextSibling || node.parentNode;\n\n  node.parentNode.removeChild(node);\n\n  return next\n}\n\n/**\n * next(prev, current, isPre) returns the next node in the sequence, given the\n * current and previous nodes.\n *\n * @param {Node} prev\n * @param {Node} current\n * @param {Function} isPre\n * @return {Node}\n */\nfunction next (prev, current, isPre) {\n  if ((prev && prev.parentNode === current) || isPre(current)) {\n    return current.nextSibling || current.parentNode\n  }\n\n  return current.firstChild || current.nextSibling || current.parentNode\n}\n\n/*\n * Set up window for Node.js\n */\n\nvar root = (typeof window !== 'undefined' ? window : {});\n\n/*\n * Parsing HTML strings\n */\n\nfunction canParseHTMLNatively () {\n  var Parser = root.DOMParser;\n  var canParse = false;\n\n  // Adapted from https://gist.github.com/1129031\n  // Firefox/Opera/IE throw errors on unsupported types\n  try {\n    // WebKit returns null on unsupported types\n    if (new Parser().parseFromString('', 'text/html')) {\n      canParse = true;\n    }\n  } catch (e) {}\n\n  return canParse\n}\n\nfunction createHTMLParser () {\n  var Parser = function () {};\n\n  {\n    if (shouldUseActiveX()) {\n      Parser.prototype.parseFromString = function (string) {\n        var doc = new window.ActiveXObject('htmlfile');\n        doc.designMode = 'on'; // disable on-page scripts\n        doc.open();\n        doc.write(string);\n        doc.close();\n        return doc\n      };\n    } else {\n      Parser.prototype.parseFromString = function (string) {\n        var doc = document.implementation.createHTMLDocument('');\n        doc.open();\n        doc.write(string);\n        doc.close();\n        return doc\n      };\n    }\n  }\n  return Parser\n}\n\nfunction shouldUseActiveX () {\n  var useActiveX = false;\n  try {\n    document.implementation.createHTMLDocument('').open();\n  } catch (e) {\n    if (root.ActiveXObject) useActiveX = true;\n  }\n  return useActiveX\n}\n\nvar HTMLParser = canParseHTMLNatively() ? root.DOMParser : createHTMLParser();\n\nfunction RootNode (input, options) {\n  var root;\n  if (typeof input === 'string') {\n    var doc = htmlParser().parseFromString(\n      // DOM parsers arrange elements in the <head> and <body>.\n      // Wrapping in a custom element ensures elements are reliably arranged in\n      // a single element.\n      '<x-turndown id=\"turndown-root\">' + input + '</x-turndown>',\n      'text/html'\n    );\n    root = doc.getElementById('turndown-root');\n  } else {\n    root = input.cloneNode(true);\n  }\n  collapseWhitespace({\n    element: root,\n    isBlock: isBlock,\n    isVoid: isVoid,\n    isPre: options.preformattedCode ? isPreOrCode : null\n  });\n\n  return root\n}\n\nvar _htmlParser;\nfunction htmlParser () {\n  _htmlParser = _htmlParser || new HTMLParser();\n  return _htmlParser\n}\n\nfunction isPreOrCode (node) {\n  return node.nodeName === 'PRE' || node.nodeName === 'CODE'\n}\n\nfunction Node (node, options) {\n  node.isBlock = isBlock(node);\n  node.isCode = node.nodeName === 'CODE' || node.parentNode.isCode;\n  node.isBlank = isBlank(node);\n  node.flankingWhitespace = flankingWhitespace(node, options);\n  return node\n}\n\nfunction isBlank (node) {\n  return (\n    !isVoid(node) &&\n    !isMeaningfulWhenBlank(node) &&\n    /^\\s*$/i.test(node.textContent) &&\n    !hasVoid(node) &&\n    !hasMeaningfulWhenBlank(node)\n  )\n}\n\nfunction flankingWhitespace (node, options) {\n  if (node.isBlock || (options.preformattedCode && node.isCode)) {\n    return { leading: '', trailing: '' }\n  }\n\n  var edges = edgeWhitespace(node.textContent);\n\n  // abandon leading ASCII WS if left-flanked by ASCII WS\n  if (edges.leadingAscii && isFlankedByWhitespace('left', node, options)) {\n    edges.leading = edges.leadingNonAscii;\n  }\n\n  // abandon trailing ASCII WS if right-flanked by ASCII WS\n  if (edges.trailingAscii && isFlankedByWhitespace('right', node, options)) {\n    edges.trailing = edges.trailingNonAscii;\n  }\n\n  return { leading: edges.leading, trailing: edges.trailing }\n}\n\nfunction edgeWhitespace (string) {\n  var m = string.match(/^(([ \\t\\r\\n]*)(\\s*))(?:(?=\\S)[\\s\\S]*\\S)?((\\s*?)([ \\t\\r\\n]*))$/);\n  return {\n    leading: m[1], // whole string for whitespace-only strings\n    leadingAscii: m[2],\n    leadingNonAscii: m[3],\n    trailing: m[4], // empty for whitespace-only strings\n    trailingNonAscii: m[5],\n    trailingAscii: m[6]\n  }\n}\n\nfunction isFlankedByWhitespace (side, node, options) {\n  var sibling;\n  var regExp;\n  var isFlanked;\n\n  if (side === 'left') {\n    sibling = node.previousSibling;\n    regExp = / $/;\n  } else {\n    sibling = node.nextSibling;\n    regExp = /^ /;\n  }\n\n  if (sibling) {\n    if (sibling.nodeType === 3) {\n      isFlanked = regExp.test(sibling.nodeValue);\n    } else if (options.preformattedCode && sibling.nodeName === 'CODE') {\n      isFlanked = false;\n    } else if (sibling.nodeType === 1 && !isBlock(sibling)) {\n      isFlanked = regExp.test(sibling.textContent);\n    }\n  }\n  return isFlanked\n}\n\nvar reduce = Array.prototype.reduce;\nvar escapes = [\n  [/\\\\/g, '\\\\\\\\'],\n  [/\\*/g, '\\\\*'],\n  [/^-/g, '\\\\-'],\n  [/^\\+ /g, '\\\\+ '],\n  [/^(=+)/g, '\\\\$1'],\n  [/^(#{1,6}) /g, '\\\\$1 '],\n  [/`/g, '\\\\`'],\n  [/^~~~/g, '\\\\~~~'],\n  [/\\[/g, '\\\\['],\n  [/\\]/g, '\\\\]'],\n  [/^>/g, '\\\\>'],\n  [/_/g, '\\\\_'],\n  [/^(\\d+)\\. /g, '$1\\\\. ']\n];\n\nfunction TurndownService (options) {\n  if (!(this instanceof TurndownService)) return new TurndownService(options)\n\n  var defaults = {\n    rules: rules,\n    headingStyle: 'setext',\n    hr: '* * *',\n    bulletListMarker: '*',\n    codeBlockStyle: 'indented',\n    fence: '```',\n    emDelimiter: '_',\n    strongDelimiter: '**',\n    linkStyle: 'inlined',\n    linkReferenceStyle: 'full',\n    br: '  ',\n    preformattedCode: false,\n    blankReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' : ''\n    },\n    keepReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + node.outerHTML + '\\n\\n' : node.outerHTML\n    },\n    defaultReplacement: function (content, node) {\n      return node.isBlock ? '\\n\\n' + content + '\\n\\n' : content\n    }\n  };\n  this.options = extend({}, defaults, options);\n  this.rules = new Rules(this.options);\n}\n\nTurndownService.prototype = {\n  /**\n   * The entry point for converting a string or DOM node to Markdown\n   * @public\n   * @param {String|HTMLElement} input The string or DOM node to convert\n   * @returns A Markdown representation of the input\n   * @type String\n   */\n\n  turndown: function (input) {\n    if (!canConvert(input)) {\n      throw new TypeError(\n        input + ' is not a string, or an element/document/fragment node.'\n      )\n    }\n\n    if (input === '') return ''\n\n    var output = process.call(this, new RootNode(input, this.options));\n    return postProcess.call(this, output)\n  },\n\n  /**\n   * Add one or more plugins\n   * @public\n   * @param {Function|Array} plugin The plugin or array of plugins to add\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  use: function (plugin) {\n    if (Array.isArray(plugin)) {\n      for (var i = 0; i < plugin.length; i++) this.use(plugin[i]);\n    } else if (typeof plugin === 'function') {\n      plugin(this);\n    } else {\n      throw new TypeError('plugin must be a Function or an Array of Functions')\n    }\n    return this\n  },\n\n  /**\n   * Adds a rule\n   * @public\n   * @param {String} key The unique key of the rule\n   * @param {Object} rule The rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  addRule: function (key, rule) {\n    this.rules.add(key, rule);\n    return this\n  },\n\n  /**\n   * Keep a node (as HTML) that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  keep: function (filter) {\n    this.rules.keep(filter);\n    return this\n  },\n\n  /**\n   * Remove a node that matches the filter\n   * @public\n   * @param {String|Array|Function} filter The unique key of the rule\n   * @returns The Turndown instance for chaining\n   * @type Object\n   */\n\n  remove: function (filter) {\n    this.rules.remove(filter);\n    return this\n  },\n\n  /**\n   * Escapes Markdown syntax\n   * @public\n   * @param {String} string The string to escape\n   * @returns A string with Markdown syntax escaped\n   * @type String\n   */\n\n  escape: function (string) {\n    return escapes.reduce(function (accumulator, escape) {\n      return accumulator.replace(escape[0], escape[1])\n    }, string)\n  }\n};\n\n/**\n * Reduces a DOM node down to its Markdown string equivalent\n * @private\n * @param {HTMLElement} parentNode The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction process (parentNode) {\n  var self = this;\n  return reduce.call(parentNode.childNodes, function (output, node) {\n    node = new Node(node, self.options);\n\n    var replacement = '';\n    if (node.nodeType === 3) {\n      replacement = node.isCode ? node.nodeValue : self.escape(node.nodeValue);\n    } else if (node.nodeType === 1) {\n      replacement = replacementForNode.call(self, node);\n    }\n\n    return join(output, replacement)\n  }, '')\n}\n\n/**\n * Appends strings as each rule requires and trims the output\n * @private\n * @param {String} output The conversion output\n * @returns A trimmed version of the ouput\n * @type String\n */\n\nfunction postProcess (output) {\n  var self = this;\n  this.rules.forEach(function (rule) {\n    if (typeof rule.append === 'function') {\n      output = join(output, rule.append(self.options));\n    }\n  });\n\n  return output.replace(/^[\\t\\r\\n]+/, '').replace(/[\\t\\r\\n\\s]+$/, '')\n}\n\n/**\n * Converts an element node to its Markdown equivalent\n * @private\n * @param {HTMLElement} node The node to convert\n * @returns A Markdown representation of the node\n * @type String\n */\n\nfunction replacementForNode (node) {\n  var rule = this.rules.forNode(node);\n  var content = process.call(this, node);\n  var whitespace = node.flankingWhitespace;\n  if (whitespace.leading || whitespace.trailing) content = content.trim();\n  return (\n    whitespace.leading +\n    rule.replacement(content, node, this.options) +\n    whitespace.trailing\n  )\n}\n\n/**\n * Joins replacement to the current output with appropriate number of new lines\n * @private\n * @param {String} output The current conversion output\n * @param {String} replacement The string to append to the output\n * @returns Joined output\n * @type String\n */\n\nfunction join (output, replacement) {\n  var s1 = trimTrailingNewlines(output);\n  var s2 = trimLeadingNewlines(replacement);\n  var nls = Math.max(output.length - s1.length, replacement.length - s2.length);\n  var separator = '\\n\\n'.substring(0, nls);\n\n  return s1 + separator + s2\n}\n\n/**\n * Determines whether an input can be converted\n * @private\n * @param {String|HTMLElement} input Describe this parameter\n * @returns Describe what it returns\n * @type String|Object|Array|Boolean|Number\n */\n\nfunction canConvert (input) {\n  return (\n    input != null && (\n      typeof input === 'string' ||\n      (input.nodeType && (\n        input.nodeType === 1 || input.nodeType === 9 || input.nodeType === 11\n      ))\n    )\n  )\n}\n\nexport default TurndownService;\n", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\n/**\n * Simple middleware intercepts actions and replaces with\n * another by calling an alias function with the original action\n * @type {object} aliases an object that maps action types (keys) to alias functions (values) (e.g. { SOME_ACTION: newActionAliasFunc })\n */\nvar _default = exports[\"default\"] = function _default(aliases) {\n  return function () {\n    return function (next) {\n      return function (action) {\n        var alias = aliases[action.type];\n        if (alias) {\n          return next(alias(action));\n        }\n        return next(action);\n      };\n    };\n  };\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.STATE_TYPE = exports.PATCH_STATE_TYPE = exports.FETCH_STATE_TYPE = exports.DISPATCH_TYPE = exports.DEFAULT_CHANNEL_NAME = void 0;\n// Message type used for dispatch events\n// from the Proxy Stores to background\nvar DISPATCH_TYPE = exports.DISPATCH_TYPE = \"webext.dispatch\";\n\n// Message type for fetching current state from\n// background to Proxy Stores\nvar FETCH_STATE_TYPE = exports.FETCH_STATE_TYPE = \"webext.fetch_state\";\n\n// Message type for state update events from\n// background to Proxy Stores\nvar STATE_TYPE = exports.STATE_TYPE = \"webext.state\";\n\n// Message type for state patch events from\n// background to Proxy Stores\nvar PATCH_STATE_TYPE = exports.PATCH_STATE_TYPE = \"webext.patch_state\";\n\n// The default name for the store channel\nvar DEFAULT_CHANNEL_NAME = exports.DEFAULT_CHANNEL_NAME = \"webext.channel\";", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"Store\", {\n  enumerable: true,\n  get: function get() {\n    return _Store[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"alias\", {\n  enumerable: true,\n  get: function get() {\n    return _alias[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"applyMiddleware\", {\n  enumerable: true,\n  get: function get() {\n    return _applyMiddleware[\"default\"];\n  }\n});\nObject.defineProperty(exports, \"createWrapStore\", {\n  enumerable: true,\n  get: function get() {\n    return _wrapStore[\"default\"];\n  }\n});\nvar _Store = _interopRequireDefault(require(\"./store/Store\"));\nvar _applyMiddleware = _interopRequireDefault(require(\"./store/applyMiddleware\"));\nvar _wrapStore = _interopRequireDefault(require(\"./wrap-store/wrapStore\"));\nvar _alias = _interopRequireDefault(require(\"./alias/alias\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { \"default\": e }; }", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createDeferredListener = void 0;\n/**\n * Returns a function that can be passed as a listener callback to a browser\n * API. The listener will queue events until setListener is called.\n *\n * @param {Function} filter - A function that filters messages to be handled by\n * the listener. This is important to avoid telling the browser to expect an\n * async response when the message is not intended for this listener.\n *\n * @example\n * const filter = (message, sender, sendResponse) => {\n *   return message.type === \"my_type\"\n * }\n *\n * const { listener, setListener } = createDeferredListener(filter);\n * chrome.runtime.onMessage.addListener(listener);\n *\n * // Later, define the listener to handle messages. Messages received\n * // before this point are queued.\n * setListener((message, sender, sendResponse) => {\n *  console.log(message);\n * });\n */\nvar createDeferredListener = exports.createDeferredListener = function createDeferredListener(filter) {\n  var resolve = function resolve() {};\n  var fnPromise = new Promise(function (resolve_) {\n    return resolve = resolve_;\n  });\n  var listener = function listener(message, sender, sendResponse) {\n    if (!filter(message, sender, sendResponse)) {\n      return;\n    }\n    fnPromise.then(function (fn) {\n      fn(message, sender, sendResponse);\n    });\n\n    // Allow response to be async\n    return true;\n  };\n  return {\n    setListener: resolve,\n    listener: listener\n  };\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.withSerializer = exports.withDeserializer = exports.noop = void 0;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar noop = exports.noop = function noop(payload) {\n  return payload;\n};\nvar transformPayload = function transformPayload(message) {\n  var transformer = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n  return _objectSpread(_objectSpread({}, message), message.payload ? {\n    payload: transformer(message.payload)\n  } : {});\n};\nvar deserializeListener = function deserializeListener(listener) {\n  var deserializer = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : noop;\n  var shouldDeserialize = arguments.length > 2 ? arguments[2] : undefined;\n  // If a shouldDeserialize function is passed, return a function that uses it\n  // to check if any given message payload should be deserialized\n  if (shouldDeserialize) {\n    return function (message) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      if (shouldDeserialize.apply(void 0, [message].concat(args))) {\n        return listener.apply(void 0, [transformPayload(message, deserializer)].concat(args));\n      }\n      return listener.apply(void 0, [message].concat(args));\n    };\n  }\n  // Otherwise, return a function that tries to deserialize on every message\n  return function (message) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n    return listener.apply(void 0, [transformPayload(message, deserializer)].concat(args));\n  };\n};\n\n/**\n * A function returned from withDeserializer that, when called, wraps addListenerFn with the\n * deserializer passed to withDeserializer.\n * @name AddListenerDeserializer\n * @function\n * @param {Function} addListenerFn The add listener function to wrap.\n * @returns {DeserializedAddListener}\n */\n\n/**\n * A wrapped add listener function that registers the given listener.\n * @name DeserializedAddListener\n * @function\n * @param {Function} listener The listener function to register. It should expect the (optionally)\n * deserialized message as its first argument.\n * @param {Function} [shouldDeserialize] A function that takes the arguments passed to the listener\n * and returns whether the message payload should be deserialized. Not all messages (notably, messages\n * this listener doesn't care about) should be attempted to be deserialized.\n */\n\n/**\n * Given a deserializer, returns an AddListenerDeserializer function that that takes an add listener\n * function and returns a DeserializedAddListener that automatically deserializes message payloads.\n * Each message listener is expected to take the message as its first argument.\n * @param {Function} deserializer A function that deserializes a message payload.\n * @returns {AddListenerDeserializer}\n * Example Usage:\n *   const withJsonDeserializer = withDeserializer(payload => JSON.parse(payload));\n *   const deserializedChromeListener = withJsonDeserializer(chrome.runtime.onMessage.addListener);\n *   const shouldDeserialize = (message) => message.type === 'DESERIALIZE_ME';\n *   deserializedChromeListener(message => console.log(\"Payload:\", message.payload), shouldDeserialize);\n *   chrome.runtime.sendMessage(\"{'type:'DESERIALIZE_ME','payload':{'prop':4}}\");\n *   //Payload: { prop: 4 };\n *   chrome.runtime.sendMessage(\"{'payload':{'prop':4}}\");\n *   //Payload: \"{'prop':4}\";\n */\nvar withDeserializer = exports.withDeserializer = function withDeserializer() {\n  var deserializer = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : noop;\n  return function (addListenerFn) {\n    return function (listener, shouldDeserialize) {\n      return addListenerFn(deserializeListener(listener, deserializer, shouldDeserialize));\n    };\n  };\n};\n\n/**\n * Given a serializer, returns a function that takes a message sending\n * function as its sole argument and returns a wrapped message sender that\n * automaticaly serializes message payloads. The message sender\n * is expected to take the message as its first argument, unless messageArgIndex\n * is nonzero, in which case it is expected in the position specified by messageArgIndex.\n * @param {Function} serializer A function that serializes a message payload\n * Example Usage:\n *   const withJsonSerializer = withSerializer(payload => JSON.stringify(payload))\n *   const serializedChromeSender = withJsonSerializer(chrome.runtime.sendMessage)\n *   chrome.runtime.addListener(message => console.log(\"Payload:\", message.payload))\n *   serializedChromeSender({ payload: { prop: 4 }})\n *   //Payload: \"{'prop':4}\"\n */\nvar withSerializer = exports.withSerializer = function withSerializer() {\n  var serializer = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : noop;\n  return function (sendMessageFn) {\n    var messageArgIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n    return function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      if (args.length <= messageArgIndex) {\n        throw new Error(\"Message in request could not be serialized. \" + \"Expected message in position \".concat(messageArgIndex, \" but only received \").concat(args.length, \" args.\"));\n      }\n      args[messageArgIndex] = transformPayload(args[messageArgIndex], serializer);\n      return sendMessageFn.apply(void 0, args);\n    };\n  };\n};", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _lodash = _interopRequireDefault(require(\"lodash.assignin\"));\nvar _constants = require(\"../constants\");\nvar _serialization = require(\"../serialization\");\nvar _patch = _interopRequireDefault(require(\"../strategies/shallowDiff/patch\"));\nvar _util = require(\"../util\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { \"default\": e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar backgroundErrPrefix = '\\nLooks like there is an error in the background page. ' + 'You might want to inspect your background page for more details.\\n';\nvar defaultOpts = {\n  channelName: _constants.DEFAULT_CHANNEL_NAME,\n  state: {},\n  serializer: _serialization.noop,\n  deserializer: _serialization.noop,\n  patchStrategy: _patch[\"default\"]\n};\nvar Store = /*#__PURE__*/function () {\n  /**\n   * Creates a new Proxy store\n   * @param  {object} options\n   * @param {string} options.channelName The name of the channel for this store.\n   * @param {object} options.state The initial state of the store (default\n   * `{}`).\n   * @param {function} options.serializer A function to serialize outgoing\n   * messages (default is passthrough).\n   * @param {function} options.deserializer A function to deserialize incoming\n   * messages (default is passthrough).\n   * @param {function} options.patchStrategy A function to patch the state with\n   * incoming messages. Use one of the included patching strategies or a custom\n   * patching function. (default is shallow diff).\n   */\n  function Store() {\n    var _this = this;\n    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : defaultOpts,\n      _ref$channelName = _ref.channelName,\n      channelName = _ref$channelName === void 0 ? defaultOpts.channelName : _ref$channelName,\n      _ref$state = _ref.state,\n      state = _ref$state === void 0 ? defaultOpts.state : _ref$state,\n      _ref$serializer = _ref.serializer,\n      serializer = _ref$serializer === void 0 ? defaultOpts.serializer : _ref$serializer,\n      _ref$deserializer = _ref.deserializer,\n      deserializer = _ref$deserializer === void 0 ? defaultOpts.deserializer : _ref$deserializer,\n      _ref$patchStrategy = _ref.patchStrategy,\n      patchStrategy = _ref$patchStrategy === void 0 ? defaultOpts.patchStrategy : _ref$patchStrategy;\n    _classCallCheck(this, Store);\n    if (!channelName) {\n      throw new Error('channelName is required in options');\n    }\n    if (typeof serializer !== 'function') {\n      throw new Error('serializer must be a function');\n    }\n    if (typeof deserializer !== 'function') {\n      throw new Error('deserializer must be a function');\n    }\n    if (typeof patchStrategy !== 'function') {\n      throw new Error('patchStrategy must be one of the included patching strategies or a custom patching function');\n    }\n    this.channelName = channelName;\n    this.readyResolved = false;\n    this.readyPromise = new Promise(function (resolve) {\n      return _this.readyResolve = resolve;\n    });\n    this.browserAPI = (0, _util.getBrowserAPI)();\n    this.initializeStore = this.initializeStore.bind(this);\n\n    // We request the latest available state data to initialise our store\n    this.browserAPI.runtime.sendMessage({\n      type: _constants.FETCH_STATE_TYPE,\n      channelName: channelName\n    }, undefined, this.initializeStore);\n    this.deserializer = deserializer;\n    this.serializedPortListener = (0, _serialization.withDeserializer)(deserializer)(function () {\n      var _this$browserAPI$runt;\n      return (_this$browserAPI$runt = _this.browserAPI.runtime.onMessage).addListener.apply(_this$browserAPI$runt, arguments);\n    });\n    this.serializedMessageSender = (0, _serialization.withSerializer)(serializer)(function () {\n      var _this$browserAPI$runt2;\n      return (_this$browserAPI$runt2 = _this.browserAPI.runtime).sendMessage.apply(_this$browserAPI$runt2, arguments);\n    });\n    this.listeners = [];\n    this.state = state;\n    this.patchStrategy = patchStrategy;\n\n    /**\n     * Determine if the message should be run through the deserializer. We want\n     * to skip processing messages that probably didn't come from this library.\n     * Note that the listener below is still called for each message so it needs\n     * its own guard, the shouldDeserialize predicate only skips _deserializing_\n     * the message.\n     */\n    var shouldDeserialize = function shouldDeserialize(message) {\n      return Boolean(message) && typeof message.type === \"string\" && message.channelName === _this.channelName;\n    };\n    this.serializedPortListener(function (message) {\n      if (!message || message.channelName !== _this.channelName) {\n        return;\n      }\n      switch (message.type) {\n        case _constants.STATE_TYPE:\n          _this.replaceState(message.payload);\n          if (!_this.readyResolved) {\n            _this.readyResolved = true;\n            _this.readyResolve();\n          }\n          break;\n        case _constants.PATCH_STATE_TYPE:\n          _this.patchState(message.payload);\n          break;\n        default:\n        // do nothing\n      }\n    }, shouldDeserialize);\n    this.dispatch = this.dispatch.bind(this); // add this context to dispatch\n    this.getState = this.getState.bind(this); // add this context to getState\n    this.subscribe = this.subscribe.bind(this); // add this context to subscribe\n  }\n\n  /**\n  * Returns a promise that resolves when the store is ready. Optionally a callback may be passed in instead.\n  * @param [function] callback An optional callback that may be passed in and will fire when the store is ready.\n  * @return {object} promise A promise that resolves when the store has established a connection with the background page.\n  */\n  return _createClass(Store, [{\n    key: \"ready\",\n    value: function ready() {\n      var cb = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n      if (cb !== null) {\n        return this.readyPromise.then(cb);\n      }\n      return this.readyPromise;\n    }\n\n    /**\n     * Subscribes a listener function for all state changes\n     * @param  {function} listener A listener function to be called when store state changes\n     * @return {function}          An unsubscribe function which can be called to remove the listener from state updates\n     */\n  }, {\n    key: \"subscribe\",\n    value: function subscribe(listener) {\n      var _this2 = this;\n      this.listeners.push(listener);\n      return function () {\n        _this2.listeners = _this2.listeners.filter(function (l) {\n          return l !== listener;\n        });\n      };\n    }\n\n    /**\n     * Replaces the state for only the keys in the updated state. Notifies all listeners of state change.\n     * @param {object} state the new (partial) redux state\n     */\n  }, {\n    key: \"patchState\",\n    value: function patchState(difference) {\n      this.state = this.patchStrategy(this.state, difference);\n      this.listeners.forEach(function (l) {\n        return l();\n      });\n    }\n\n    /**\n     * Replace the current state with a new state. Notifies all listeners of state change.\n     * @param  {object} state The new state for the store\n     */\n  }, {\n    key: \"replaceState\",\n    value: function replaceState(state) {\n      this.state = state;\n      this.listeners.forEach(function (l) {\n        return l();\n      });\n    }\n\n    /**\n     * Get the current state of the store\n     * @return {object} the current store state\n     */\n  }, {\n    key: \"getState\",\n    value: function getState() {\n      return this.state;\n    }\n\n    /**\n     * Stub function to stay consistent with Redux Store API. No-op.\n     */\n  }, {\n    key: \"replaceReducer\",\n    value: function replaceReducer() {\n      return;\n    }\n\n    /**\n     * Dispatch an action to the background using messaging passing\n     * @param  {object} data The action data to dispatch\n     * @return {Promise}     Promise that will resolve/reject based on the action response from the background\n     */\n  }, {\n    key: \"dispatch\",\n    value: function dispatch(data) {\n      var _this3 = this;\n      return new Promise(function (resolve, reject) {\n        _this3.serializedMessageSender({\n          type: _constants.DISPATCH_TYPE,\n          channelName: _this3.channelName,\n          payload: data\n        }, null, function (resp) {\n          if (!resp) {\n            var _error = _this3.browserAPI.runtime.lastError;\n            var bgErr = new Error(\"\".concat(backgroundErrPrefix).concat(_error));\n            reject((0, _lodash[\"default\"])(bgErr, _error));\n            return;\n          }\n          var error = resp.error,\n            value = resp.value;\n          if (error) {\n            var _bgErr = new Error(\"\".concat(backgroundErrPrefix).concat(error));\n            reject((0, _lodash[\"default\"])(_bgErr, error));\n          } else {\n            resolve(value && value.payload);\n          }\n        });\n      });\n    }\n  }, {\n    key: \"initializeStore\",\n    value: function initializeStore(message) {\n      if (message && message.type === _constants.FETCH_STATE_TYPE) {\n        this.replaceState(message.payload);\n\n        // Resolve if readyPromise has not been resolved.\n        if (!this.readyResolved) {\n          this.readyResolved = true;\n          this.readyResolve();\n        }\n      }\n    }\n  }]);\n}();\nvar _default = exports[\"default\"] = Store;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = applyMiddleware;\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\n// Function taken from redux source\n// https://github.com/reactjs/redux/blob/master/src/compose.js\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n// Based on redux implementation of applyMiddleware to support all standard\n// redux middlewares\nfunction applyMiddleware(store) {\n  for (var _len2 = arguments.length, middlewares = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    middlewares[_key2 - 1] = arguments[_key2];\n  }\n  var _dispatch = function dispatch() {\n    throw new Error('Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n  };\n  var middlewareAPI = {\n    getState: store.getState.bind(store),\n    dispatch: function dispatch() {\n      return _dispatch.apply(void 0, arguments);\n    }\n  };\n  middlewares = (middlewares || []).map(function (middleware) {\n    return middleware(middlewareAPI);\n  });\n  _dispatch = compose.apply(void 0, _toConsumableArray(middlewares))(store.dispatch);\n  store.dispatch = _dispatch;\n  return store;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DIFF_STATUS_UPDATED = exports.DIFF_STATUS_REMOVED = exports.DIFF_STATUS_KEYS_UPDATED = exports.DIFF_STATUS_ARRAY_UPDATED = void 0;\n// The `change` value for updated or inserted fields resulting from shallow diff\nvar DIFF_STATUS_UPDATED = exports.DIFF_STATUS_UPDATED = 'updated';\n\n// The `change` value for removed fields resulting from shallow diff\nvar DIFF_STATUS_REMOVED = exports.DIFF_STATUS_REMOVED = 'removed';\nvar DIFF_STATUS_KEYS_UPDATED = exports.DIFF_STATUS_KEYS_UPDATED = 'updated_keys';\nvar DIFF_STATUS_ARRAY_UPDATED = exports.DIFF_STATUS_ARRAY_UPDATED = 'updated_array';", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = shallowDiff;\nvar _constants = require(\"../constants\");\n/**\n * Returns a new Object containing only the fields in `new` that differ from `old`\n *\n * @param {Object} old\n * @param {Object} new\n * @return {Array} An array of changes. The changes have a `key`, `value`, and `change`.\n *   The change is either `updated`, which is if the value has changed or been added,\n *   or `removed`.\n */\nfunction shallowDiff(oldObj, newObj) {\n  var difference = [];\n  Object.keys(newObj).forEach(function (key) {\n    if (oldObj[key] !== newObj[key]) {\n      difference.push({\n        key: key,\n        value: newObj[key],\n        change: _constants.DIFF_STATUS_UPDATED\n      });\n    }\n  });\n  Object.keys(oldObj).forEach(function (key) {\n    if (!newObj.hasOwnProperty(key)) {\n      difference.push({\n        key: key,\n        change: _constants.DIFF_STATUS_REMOVED\n      });\n    }\n  });\n  return difference;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = _default;\nvar _constants = require(\"../constants\");\nfunction _default(obj, difference) {\n  var newObj = Object.assign({}, obj);\n  difference.forEach(function (_ref) {\n    var change = _ref.change,\n      key = _ref.key,\n      value = _ref.value;\n    switch (change) {\n      case _constants.DIFF_STATUS_UPDATED:\n        newObj[key] = value;\n        break;\n      case _constants.DIFF_STATUS_REMOVED:\n        Reflect.deleteProperty(newObj, key);\n        break;\n      default:\n      // do nothing\n    }\n  });\n  return newObj;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getBrowserAPI = getBrowserAPI;\n/**\n * Looks for a global browser api, first checking the chrome namespace and then\n * checking the browser namespace. If no appropriate namespace is present, this\n * function will throw an error.\n */\nfunction getBrowserAPI() {\n  var api;\n  try {\n    // eslint-disable-next-line no-undef\n    api = self.chrome || self.browser || browser;\n  } catch (error) {\n    // eslint-disable-next-line no-undef\n    api = browser;\n  }\n  if (!api) {\n    throw new Error(\"Browser API is not present\");\n  }\n  return api;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _constants = require(\"../constants\");\nvar _serialization = require(\"../serialization\");\nvar _util = require(\"../util\");\nvar _diff = _interopRequireDefault(require(\"../strategies/shallowDiff/diff\"));\nvar _listener = require(\"../listener\");\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { \"default\": e }; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t[\"return\"] || t[\"return\"](); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\n/**\n * Responder for promisified results\n * @param  {object} dispatchResult The result from `store.dispatch()`\n * @param  {function} send         The function used to respond to original message\n * @return {undefined}\n */\nvar promiseResponder = function promiseResponder(dispatchResult, send) {\n  Promise.resolve(dispatchResult).then(function (res) {\n    send({\n      error: null,\n      value: res\n    });\n  })[\"catch\"](function (err) {\n    console.error(\"error dispatching result:\", err);\n    send({\n      error: err.message,\n      value: null\n    });\n  });\n};\nvar defaultOpts = {\n  channelName: _constants.DEFAULT_CHANNEL_NAME,\n  dispatchResponder: promiseResponder,\n  serializer: _serialization.noop,\n  deserializer: _serialization.noop,\n  diffStrategy: _diff[\"default\"]\n};\n\n/**\n * @typedef {function} WrapStore\n * @param {Object} store A Redux store\n * @param {Object} options\n * @param {function} options.dispatchResponder A function that takes the result\n * of a store dispatch and optionally implements custom logic for responding to\n * the original dispatch message.\n * @param {function} options.serializer A function to serialize outgoing message\n * payloads (default is passthrough).\n * @param {function} options.deserializer A function to deserialize incoming\n * message payloads (default is passthrough).\n * @param {function} options.diffStrategy A function to diff the previous state\n * and the new state (default is shallow diff).\n */\n\n/**\n * Wraps a Redux store so that proxy stores can connect to it. This function\n * must be called synchronously when the extension loads to avoid dropping\n * messages that woke the service worker.\n * @param {Object} options\n * @param {string} options.channelName The name of the channel for this store.\n * @return {WrapStore} The wrapStore function that accepts a Redux store and\n * options. See {@link WrapStore}.\n */\nvar _default = exports[\"default\"] = function _default() {\n  var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : defaultOpts,\n    _ref$channelName = _ref.channelName,\n    channelName = _ref$channelName === void 0 ? defaultOpts.channelName : _ref$channelName;\n  var browserAPI = (0, _util.getBrowserAPI)();\n  var filterStateMessages = function filterStateMessages(message) {\n    return message.type === _constants.FETCH_STATE_TYPE && message.channelName === channelName;\n  };\n  var filterActionMessages = function filterActionMessages(message) {\n    return message.type === _constants.DISPATCH_TYPE && message.channelName === channelName;\n  };\n\n  // Setup message listeners synchronously to avoid dropping messages if the\n  // extension is woken by a message.\n  var stateProviderListener = (0, _listener.createDeferredListener)(filterStateMessages);\n  var actionListener = (0, _listener.createDeferredListener)(filterActionMessages);\n  browserAPI.runtime.onMessage.addListener(stateProviderListener.listener);\n  browserAPI.runtime.onMessage.addListener(actionListener.listener);\n  return function (store) {\n    var _ref2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultOpts,\n      _ref2$dispatchRespond = _ref2.dispatchResponder,\n      dispatchResponder = _ref2$dispatchRespond === void 0 ? defaultOpts.dispatchResponder : _ref2$dispatchRespond,\n      _ref2$serializer = _ref2.serializer,\n      serializer = _ref2$serializer === void 0 ? defaultOpts.serializer : _ref2$serializer,\n      _ref2$deserializer = _ref2.deserializer,\n      deserializer = _ref2$deserializer === void 0 ? defaultOpts.deserializer : _ref2$deserializer,\n      _ref2$diffStrategy = _ref2.diffStrategy,\n      diffStrategy = _ref2$diffStrategy === void 0 ? defaultOpts.diffStrategy : _ref2$diffStrategy;\n    if (typeof serializer !== \"function\") {\n      throw new Error(\"serializer must be a function\");\n    }\n    if (typeof deserializer !== \"function\") {\n      throw new Error(\"deserializer must be a function\");\n    }\n    if (typeof diffStrategy !== \"function\") {\n      throw new Error(\"diffStrategy must be one of the included diffing strategies or a custom diff function\");\n    }\n\n    /**\n     * Respond to dispatches from UI components\n     */\n    var dispatchResponse = function dispatchResponse(request, sender, sendResponse) {\n      //  Only called with messages that pass the filterActionMessages filter.\n      var action = Object.assign({}, request.payload, {\n        _sender: sender\n      });\n      var dispatchResult = null;\n      try {\n        dispatchResult = store.dispatch(action);\n      } catch (e) {\n        dispatchResult = Promise.reject(e.message);\n        console.error(e);\n      }\n      dispatchResponder(dispatchResult, sendResponse);\n    };\n\n    /**\n     * Setup for state updates\n     */\n    var serializedMessagePoster = (0, _serialization.withSerializer)(serializer)(function () {\n      var _browserAPI$runtime;\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      var onErrorCallback = function onErrorCallback() {\n        if (browserAPI.runtime.lastError) {\n          // do nothing - errors can be present\n          // if no content script exists on receiver\n        }\n      };\n      (_browserAPI$runtime = browserAPI.runtime).sendMessage.apply(_browserAPI$runtime, args.concat([onErrorCallback]));\n      // We will broadcast state changes to all tabs to sync state across content scripts\n      return browserAPI.tabs.query({}, function (tabs) {\n        var _iterator = _createForOfIteratorHelper(tabs),\n          _step;\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var _browserAPI$tabs;\n            var tab = _step.value;\n            (_browserAPI$tabs = browserAPI.tabs).sendMessage.apply(_browserAPI$tabs, [tab.id].concat(args, [onErrorCallback]));\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      });\n    });\n    var currentState = store.getState();\n    var patchState = function patchState() {\n      var newState = store.getState();\n      var diff = diffStrategy(currentState, newState);\n      if (diff.length) {\n        currentState = newState;\n        serializedMessagePoster({\n          type: _constants.PATCH_STATE_TYPE,\n          payload: diff,\n          channelName: channelName // Notifying what store is broadcasting the state changes\n        });\n      }\n    };\n\n    // Send patched state to listeners on every redux store state change\n    store.subscribe(patchState);\n\n    // Send store's initial state\n    serializedMessagePoster({\n      type: _constants.STATE_TYPE,\n      payload: currentState,\n      channelName: channelName // Notifying what store is broadcasting the state changes\n    });\n\n    /**\n     * State provider for content-script initialization\n     */\n    stateProviderListener.setListener(function (request, sender, sendResponse) {\n      // This listener is only called with messages that pass filterStateMessages\n      var state = store.getState();\n      sendResponse({\n        type: _constants.FETCH_STATE_TYPE,\n        payload: state\n      });\n    });\n\n    /**\n     * Setup action handler\n     */\n    var withPayloadDeserializer = (0, _serialization.withDeserializer)(deserializer);\n    withPayloadDeserializer(actionListener.setListener)(dispatchResponse, filterActionMessages);\n  };\n};", "import { createSlice } from '@reduxjs/toolkit';\nimport { ThunkType } from 'src/state/State';\n\nexport interface ContentState {\n    isLoaded: boolean;\n}\n\nexport const contentDefaultState: ContentState = {\n  isLoaded: false,\n};\n\nconst slice = createSlice({\n  name: 'content',\n  initialState: contentDefaultState,\n  reducers: {\n    reset: () => contentDefaultState,\n    contentLoaded: state => {\n      state.isLoaded = true;\n    },\n  }\n});\n\n/**\n * this is an example of a thunk, you could add api requests from here\n * and dispatch actions to update the state\n */\nexport const contentLoaded = (): ThunkType => async (dispatch, getState) => {\n  const { isLoaded } = getState().content || {};\n\n  if (isLoaded) return;\n\n  await dispatch(slice.actions.contentLoaded());\n};\n\nconst { actions, reducer } = slice;\nconst aliases = {};\n\nexport {\n actions, aliases, reducer \n};\n", "import { createSlice } from '@reduxjs/toolkit';\n\nexport enum ModalConfirmActions {\n  Default = 'default',\n  ConfirmDeleteCard = 'confirmDeleteCard',\n}\n\nexport enum ModalCancelActions {\n  Default = 'default',\n}\n\nexport interface SidePanelState {\n  isOpen: boolean;\n}\n\nexport const sidePanelDefaultState: SidePanelState = { isOpen: false };\n\nconst slice = createSlice({\n  name: 'sidePanel',\n  initialState: sidePanelDefaultState,\n  reducers: { reset: () => sidePanelDefaultState }\n});\n\nconst { actions, reducer } = slice;\nconst aliases = {};\n\nexport {\n actions, aliases,reducer \n};\n", "import {\n  combineReducers,\n  configureStore,\n  createSerializableStateInvariantMiddleware,\n  Middleware,\n  Slice,\n  UnknownAction\n} from '@reduxjs/toolkit';\nimport { logger } from 'redux-logger';\nimport { thunk } from 'redux-thunk';\nimport {\n alias, applyMiddleware, Store, createWrapStore\n} from 'webext-redux';\n\nimport * as contentSlice from 'src/state/slices/content';\nimport * as sidePanelSlice from 'src/state/slices/sidePanel';\nimport { State } from 'src/state/State';\n\ntype BuildStoreOptions = {\n    reducers?: {\n        [key in string]: Slice\n    };\n    channelName?: string;\n};\n\nconst backgroundAliases = { ...sidePanelSlice.aliases, ...contentSlice.aliases };\n\nconst middleware: Middleware[] = [\n  alias(backgroundAliases) as Middleware,\n  thunk as Middleware,\n  createSerializableStateInvariantMiddleware(),\n  logger as Middleware\n];\n\nconst middlewareForProxy: Middleware[] = [\n  alias(backgroundAliases) as Middleware,\n  thunk as Middleware,\n  createSerializableStateInvariantMiddleware(),\n  logger as Middleware,\n];\n\nconst additionalMiddlewareForConfigureStore: Middleware[] = [\n  alias(backgroundAliases) as Middleware,\n  logger as Middleware,\n];\n\nconst buildStoreWithDefaults = ({ channelName }: BuildStoreOptions = {}) => {\n  const reducer = combineReducers({\n    sidePanel: sidePanelSlice.reducer,\n    content: contentSlice.reducer\n  });\n\n  const store = configureStore({\n    devTools: true,\n    reducer,\n    middleware: (getDefaultMiddleware) =>\n      getDefaultMiddleware().concat(additionalMiddlewareForConfigureStore),\n  });\n\n  if (channelName) {\n    const specificWrapStore = createWrapStore({ channelName });\n    specificWrapStore(store);\n  }\n\n  return store;\n};\n\nexport default buildStoreWithDefaults;\n\nexport const createStoreProxy = (channelName: string) => {\n  const store = new Store<State, UnknownAction>({ channelName });\n\n  applyMiddleware(store, ...middlewareForProxy); \n\n  return store;\n};\n", "enum ChannelNames {\n  ContentPort = 'content',\n  SidePanelPort = 'sidePanel',\n  SAVE_NOTE_TO_FILE= 'save-note-to-file',\n}\n\nexport default ChannelNames;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "import { contentLoaded } from 'src/state/slices/content';\nimport { createStoreProxy } from 'src/state/store';\nimport ChannelNames from '../types/ChannelNames';\nimport Defuddle from 'defuddle';\nimport * as turndownPluginGfm from 'turndown-plugin-gfm';\nimport TurndownService from 'turndown';\n\n(async () => {\n  try {\n    if (\n      window.location.protocol === 'chrome:' ||\n      window.location.protocol === 'chrome-extension:'\n    ) {\n      return;\n    }\n    \n\n    const store = createStoreProxy(ChannelNames.ContentPort);\n    \n\n    try {\n      await store.ready();\n      \n      store.dispatch(contentLoaded());\n      \n    } catch (initError) {\n      console.error('ChromePanion - Content script store init error:', initError);\n    }\n  } catch (err) {\n    console.error('ChromePanion - Content script main initialization error:', err);\n  }\n})();\n\nchrome.runtime.onMessage.addListener((request, sender, sendResponse) => {\n  if (request.type === 'DEFUDDLE_PAGE_CONTENT') {\n    let turndownService: TurndownService | null = null;\n\n    try {\n      if (document.contentType === 'application/pdf') {\n        sendResponse({\n          success: false,\n          error: 'Cannot defuddle PDF content directly. Please save or copy text manually.',\n          title: document.title || 'PDF Document'\n        });\n        return true;\n      }\n\n      if (typeof Defuddle === 'undefined') {\n        sendResponse({ success: false, error: 'Defuddle library not available.', title: document.title });\n        return true;\n      }\n\n      const defuddleInstance = new Defuddle(document, {\n        markdown: false,\n        url: document.location.href\n      });\n      const defuddleResult = defuddleInstance.parse();\n\n      if (typeof TurndownService === 'undefined') {\n        sendResponse({ success: false, error: 'TurndownService library not available.', title: document.title });\n        return true;\n      }\n\n      turndownService = new TurndownService({ headingStyle: 'atx', codeBlockStyle: 'fenced' })\n        .use(turndownPluginGfm.gfm);\n\n      const markdownContent = turndownService.turndown(defuddleResult.content || '');\n\n      const firstHeading = document.querySelector('h1, h2, h3')?.textContent?.trim();\n      const fallbackTitle = document.title || 'Untitled Note';\n\n      sendResponse({\n        success: true,\n        title: firstHeading || defuddleResult.title || fallbackTitle,\n        content: markdownContent\n     });\n    } catch (error: any) {\n      sendResponse({ success: false, error: error.message, title: document.title });\n    }\n    \n    return true;\n  }\n\n});\n\nexport {};"], "names": [], "sourceRoot": ""}